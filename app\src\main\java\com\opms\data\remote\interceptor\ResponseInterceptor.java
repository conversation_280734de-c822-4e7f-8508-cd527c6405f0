package com.opms.data.remote.interceptor;

import android.content.Context;
import android.content.SharedPreferences;

import com.opms.common.constants.ApiConstants;
import com.opms.data.model.response.ApiResponse;

import java.io.IOException;

import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okio.Buffer;
import okio.BufferedSource;
import retrofit2.Converter;

public class ResponseInterceptor implements Interceptor {
    private final Context context;
    private final SharedPreferences preferences;
    private final Converter<ResponseBody, ApiResponse<?>> converter;
    private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

    public ResponseInterceptor(Context context, Converter<ResponseBody, ApiResponse<?>> converter) {
        this.context = context;
        this.preferences = context.getSharedPreferences("opms_prefs", Context.MODE_PRIVATE);
        this.converter = converter;
    }

    @Override
    public Response intercept(Chain chain) throws IOException {
        Response response = chain.proceed(chain.request());
        ResponseBody originalBody = response.body();

        if (originalBody == null) {
            return response;
        }

        // 读取并缓存响应体内容
        BufferedSource source = originalBody.source();
        source.request(Long.MAX_VALUE); // Buffer the entire body
        Buffer buffer = source.getBuffer();
        String responseString = buffer.clone().readString(originalBody.contentType().charset());

        // 创建一个新的ResponseBody用于转换
        ResponseBody copyBody = ResponseBody.create(originalBody.contentType(), responseString);

        try {
            ApiResponse<?> apiResponse = converter.convert(copyBody);
            if (apiResponse != null && !apiResponse.isSuccess()) {
                if (apiResponse.getCode() == ApiConstants.UNAUTHORIZED) {
                    // Token过期，清除本地存储的token和userName
                    preferences.edit()
                            .remove("token")
                            .remove("userName")
                            .apply();
                }

                String msg = apiResponse.getMessage() == null ? "" : apiResponse.getMessage();
                ResponseBody errorBody = ResponseBody.create(
                        JSON,
                        "{\"code\":" + apiResponse.getCode() +
                                ",\"message\":\"" + msg +
                                "\",\"success\":false}"
                );

                return new Response.Builder()
                        .request(response.request())
                        .protocol(response.protocol())
                        .code(apiResponse.getCode())
                        .message(msg)
                        .body(errorBody)
                        .headers(response.headers())
                        .build();
            }
        } catch (Exception e) {
            // 如果转换失败，返回原始响应
            return response;
        }

        // 创建新的ResponseBody返回原始内容
        return response.newBuilder()
                .body(ResponseBody.create(originalBody.contentType(), responseString))
                .build();
    }
} 