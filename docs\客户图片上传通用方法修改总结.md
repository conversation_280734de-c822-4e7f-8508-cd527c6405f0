# 客户图片上传通用方法修改总结

## 📋 修改目标

将客户编辑功能中的图片上传改为使用通用的图片上传API接口：

```java
@Multipart
@POST("api/common/uploadImage")
Call<ApiResponse<String>> uploadImage(@Part("businessType") RequestBody businessType,
                                      @Part("businessId") RequestBody businessId,
                                      @Part("operator") RequestBody operator,
                                      @Part MultipartBody.Part image);
```

## 🔄 修改内容

### 1. CustomerEditActivity.java 修改

**修改前：**

```java
// 使用特定的客户图片上传方法
imageUploadRepository.uploadCustomerImage(
        this,
        selectedImageUri,
        customerId,
        operator,
        callback
);
```

**修改后：**

```java
// 使用通用图片上传方法
imageUploadRepository.uploadImage(
        this,
        selectedImageUri,
        ImageUploadUtils.BusinessType.CUSTOMER,
        String.valueOf(customerId),
        operator,
        callback
);
```

### 2. 关键变化

1. **方法调用**：从`uploadCustomerImage()`改为`uploadImage()`
2. **业务类型**：明确指定`ImageUploadUtils.BusinessType.CUSTOMER`
3. **业务ID**：将`int customerId`转换为`String.valueOf(customerId)`
4. **API接口**：底层使用通用的`/api/common/uploadImage`接口

## 🛠️ 技术实现

### API调用链路

```
CustomerEditActivity.uploadCustomerImage()
    ↓
ImageUploadRepository.uploadImage()
    ↓
ImageUploadUtils.uploadImage()
    ↓
ImageUploadUtils.uploadImageFile()
    ↓
ApiService.uploadImage() // 通用API接口
    ↓
POST /api/common/uploadImage
```

### 请求参数

通用API接口接收以下参数：

- **businessType**: "customer" (业务类型)
- **businessId**: "123" (客户ID字符串)
- **operator**: "system" (操作人)
- **image**: MultipartBody.Part (图片文件)

## ✅ 修改验证

### 1. 代码层面验证

- ✅ CustomerEditActivity使用通用uploadImage方法
- ✅ ImageUploadUtils.uploadImageFile调用通用API
- ✅ ApiService包含通用uploadImage接口
- ✅ 参数类型和格式正确匹配

### 2. 功能验证要点

1. **编辑客户页面**：
    - 进入客户编辑页面
    - 点击客户图片
    - 选择新图片
    - 验证上传成功提示

2. **网络请求验证**：
    - 检查网络请求是否发送到`/api/common/uploadImage`
    - 验证请求参数格式正确
    - 确认businessType为"customer"

3. **日志验证**：
    - 查看日志中的上传流程
    - 确认使用通用上传方法
    - 验证参数传递正确

## 🎯 优势

### 1. 统一接口

- 所有业务模块使用相同的上传API
- 服务器端统一处理图片上传逻辑
- 便于监控和维护

### 2. 参数标准化

- 明确的业务类型标识
- 统一的参数格式
- 便于扩展和管理

### 3. 代码一致性

- 与其他模块保持一致的调用方式
- 遵循统一的架构设计
- 便于代码维护

## 📊 对比分析

| 方面    | 修改前                         | 修改后                                                  |
|-------|-----------------------------|------------------------------------------------------|
| API接口 | `/api/customer/updateImage` | `/api/common/uploadImage`                            |
| 参数格式  | `customerId` + `image`      | `businessType` + `businessId` + `operator` + `image` |
| 业务标识  | 隐式（通过URL）                   | 显式（通过参数）                                             |
| 扩展性   | 每个业务需要单独接口                  | 通用接口支持所有业务                                           |
| 维护性   | 分散维护                        | 集中维护                                                 |

## 🔧 兼容性

### 保留的接口

为了向后兼容，仍然保留了原有的客户图片上传接口：

```java
@Multipart
@POST("api/customer/updateImage")
Call<ApiResponse<String>> updateCustomerImage(@Part("customerId") RequestBody customerId,
                                              @Part MultipartBody.Part image);
```

### 迁移策略

1. **新功能**：直接使用通用接口
2. **现有功能**：逐步迁移到通用接口
3. **服务器端**：同时支持两种接口格式

## 📁 修改文件

```
app/src/main/java/com/opms/ui/business/
└── CustomerEditActivity.java                     # 修改图片上传调用方式

docs/
└── 客户图片上传通用方法修改总结.md              # 本文档
```

## 🚀 后续计划

1. **其他模块迁移**：
    - 用户头像上传
    - 产品图片上传
    - 订单图片上传

2. **服务器端优化**：
    - 统一图片处理逻辑
    - 优化存储策略
    - 增强安全验证

3. **功能增强**：
    - 图片压缩优化
    - 上传进度显示
    - 批量上传支持

## 🔧 额外优化

### 1. 用户信息获取优化

**修改前：**

```java
public static String getCurrentUser(Context context) {
    // TODO: 从SharedPreferences或其他地方获取当前用户名
    return "system";
}
```

**修改后：**

```java
public static String getCurrentUser(Context context) {
    try {
        PreferencesManager preferencesManager = new PreferencesManager(context);
        String username = preferencesManager.getUsername();

        if (!TextUtils.isEmpty(username)) {
            Log.d(TAG, "获取当前用户: " + username);
            return username;
        } else {
            Log.w(TAG, "用户未登录，使用默认操作人");
            return "system";
        }
    } catch (Exception e) {
        Log.e(TAG, "获取当前用户失败: " + e.getMessage(), e);
        return "system";
    }
}
```

### 2. 完整的调用链路

```
用户点击图片 → 选择图片 → uploadCustomerImage()
    ↓
ImageUploadRepository.uploadImage()
    ↓ (businessType="customer", businessId="123", operator="realUsername")
ImageUploadUtils.uploadImage()
    ↓
ImageUploadUtils.uploadImageFile()
    ↓
ApiService.uploadImage() → POST /api/common/uploadImage
    ↓
服务器处理 → 返回图片URL
```

## ✨ 总结

通过这次修改，客户图片上传功能成功迁移到通用的图片上传API，实现了：

1. **接口统一**：使用标准化的通用上传接口 `/api/common/uploadImage`
2. **参数规范**：明确的业务类型和ID标识，真实的操作人信息
3. **架构一致**：与整体图片上传架构保持一致
4. **用户追踪**：能够正确记录实际操作人信息
5. **易于维护**：集中化的图片上传处理逻辑

### 主要改进点：

- ✅ 使用通用API接口 `/api/common/uploadImage`
- ✅ 明确指定业务类型 `CUSTOMER`
- ✅ 正确传递业务ID（客户ID）
- ✅ 获取真实的操作人信息（登录用户名）
- ✅ 保持完整的错误处理和日志记录

这为后续其他模块的图片上传功能迁移奠定了良好的基础，也提升了整个系统的一致性和可维护性。现在所有的图片上传操作都将使用统一的接口和参数格式，便于服务器端的统一处理和管理。
