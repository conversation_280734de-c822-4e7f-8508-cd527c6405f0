package com.opms.data.model.response;

import com.google.gson.annotations.SerializedName;

/**
 * 用户头像响应模型
 */
public class AvatarResponse {
    @SerializedName("url")
    private String url;

    @SerializedName("base64")
    private String base64;

    @SerializedName("filename")
    private String filename;

    @SerializedName("contentType")
    private String contentType;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getBase64() {
        return base64;
    }

    public void setBase64(String base64) {
        this.base64 = base64;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
}
