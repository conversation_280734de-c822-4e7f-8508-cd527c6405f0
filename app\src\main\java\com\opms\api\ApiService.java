package com.opms.api;

import com.opms.common.constants.ApiConstants;
import com.opms.data.local.entity.Registration;
import com.opms.data.local.entity.User;

import java.util.List;
import java.util.Map;

import retrofit2.Call;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.Header;
import retrofit2.http.POST;
import retrofit2.http.PUT;
import retrofit2.http.Path;

public class ApiService {
    private static ApiService instance;
    private final RegistrationApi registrationApi;
    private final AuthApi authApi;

    private ApiService() {
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(ApiConstants.BASE_URL)
                .addConverterFactory(GsonConverterFactory.create())
                .build();

        registrationApi = retrofit.create(RegistrationApi.class);
        authApi = retrofit.create(AuthApi.class);
    }

    public static synchronized ApiService getInstance() {
        if (instance == null) {
            instance = new ApiService();
        }
        return instance;
    }

    public Call<Registration> register(Registration registration) {
        return registrationApi.register(registration);
    }

    public Call<List<Registration>> getRegistrations() {
        return registrationApi.getRegistrations();
    }

    public Call<List<Registration>> searchRegistrations(String query) {
        return registrationApi.searchRegistrations(query);
    }

    public Call<Registration> getRegistration(String registrationId) {
        return registrationApi.getRegistration(registrationId);
    }

    public Call<Registration> updateRegistration(Registration registration) {
        return registrationApi.updateRegistration(registration.getId(), registration);
    }

    public Call<Map<String, String>> login(Map<String, String> loginRequest) {
        return authApi.login(loginRequest);
    }

    @POST("auth/register")
    public Call<Void> register(@Body User user) {
        return null;
    }

    @GET("users/pending")
    public Call<List<User>> getPendingUsers(@Header("token") String token, @Header("userName") String userName) {
        return null;
    }

    @PUT("users/{userId}/approve")
    public Call<Void> approveUser(
            @Header("token") String token,
            @Header("userName") String userName,
            @Path("userId") Long userId,
            @Body Map<String, String> approvalData
    ) {
        return null;
    }

    @PUT("users/{userId}/reject")
    public Call<Void> rejectUser(
            @Header("token") String token,
            @Header("userName") String userName,
            @Path("userId") Long userId,
            @Body Map<String, String> rejectionData
    ) {
        return null;
    }

    @GET("users/current")
    public Call<User> getCurrentUser(@Header("token") String token, @Header("userName") String userName) {
        return null;
    }

    @PUT("users/current")
    public Call<User> updateCurrentUser(
            @Header("token") String token,
            @Header("userName") String userName,
            @Body User user
    ) {
        return null;
    }
}