package com.opms.di;

import com.opms.data.repository.ProcessTemplateRepository;
import com.opms.data.repository.ProcessTemplateRepositoryImpl;
import com.opms.data.repository.UserAuditRepository;
import com.opms.data.repository.UserAuditRepositoryImpl;
import com.opms.data.repository.UserRepository;
import com.opms.data.repository.UserRepositoryImpl;

import javax.inject.Singleton;

import dagger.Binds;
import dagger.Module;
import dagger.hilt.InstallIn;
import dagger.hilt.components.SingletonComponent;

@Module
@InstallIn(SingletonComponent.class)
public abstract class RepositoryModule {
    @Binds
    @Singleton
    public abstract UserRepository bindUserRepository(UserRepositoryImpl impl);

    @Binds
    @Singleton
    public abstract ProcessTemplateRepository bindProcessTemplateRepository(ProcessTemplateRepositoryImpl impl);

    @Binds
    @Singleton
    public abstract UserAuditRepository bindUserAuditRepository(UserAuditRepositoryImpl impl);
}