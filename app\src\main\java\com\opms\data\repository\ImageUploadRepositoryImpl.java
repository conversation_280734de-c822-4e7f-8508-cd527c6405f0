package com.opms.data.repository;

import android.content.Context;
import android.net.Uri;

import com.opms.common.enums.BusinessImgType;
import com.opms.common.utils.ImageUploadUtils;
import com.opms.data.remote.ApiService;

import java.io.File;
import java.util.List;

import javax.inject.Inject;

/**
 * 图片上传Repository实现类
 */
public class ImageUploadRepositoryImpl implements ImageUploadRepository {

    private final ApiService apiService;

    @Inject
    public ImageUploadRepositoryImpl(ApiService apiService) {
        this.apiService = apiService;
    }

    @Override
    public void uploadImage(Context context,
                            Uri imageUri,
                            BusinessImgType businessType,
                            String businessId,
                            String operator,
                            ImageUploadUtils.ImageUploadCallback callback) {

        ImageUploadUtils.uploadImage(context, apiService, imageUri, businessType, businessId, operator, callback);
    }

    @Override
    public void uploadImageFile(File imageFile,
                                BusinessImgType businessType,
                                String businessId,
                                String operator,
                                ImageUploadUtils.ImageUploadCallback callback) {

        ImageUploadUtils.uploadImageFile(apiService, imageFile, businessType, businessId, operator, callback);
    }

    @Override
    public void uploadCustomerImage(Context context,
                                    Uri imageUri,
                                    int customerId,
                                    String operator,
                                    ImageUploadUtils.ImageUploadCallback callback) {

        uploadImage(context, imageUri, BusinessImgType.CUSTOMER,
                String.valueOf(customerId), operator, callback);
    }

    @Override
    public void uploadUserAvatar(Context context,
                                 Uri imageUri,
                                 int userId,
                                 String operator,
                                 ImageUploadUtils.ImageUploadCallback callback) {

        uploadImage(context, imageUri, BusinessImgType.USER,
                String.valueOf(userId), operator, callback);
    }

    @Override
    public void uploadMultipleImages(Context context,
                                     List<Uri> imageUris,
                                     BusinessImgType businessType,
                                     String businessId,
                                     String operator,
                                     ImageUploadUtils.MultiImageUploadCallback callback) {

        ImageUploadUtils.uploadMultipleImages(context, apiService, imageUris, businessType, businessId, operator, callback);
    }

    @Override
    public void deleteImage(BusinessImgType businessType,
                            String businessId,
                            String operator,
                            String imageUrl,
                            ImageUploadUtils.ImageDeleteCallback callback) {

        ImageUploadUtils.deleteImage(apiService, businessType, businessId, operator, imageUrl, callback);
    }

    @Override
    public void getImages(BusinessImgType businessType,
                          String businessId,
                          ImageUploadUtils.ImageListCallback callback) {

        ImageUploadUtils.getImages(apiService, businessType, businessId, callback);
    }
}
