package com.opms.ui.system;

import android.os.Bundle;
import android.util.Log;
import android.view.MenuItem;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.android.material.snackbar.Snackbar;
import com.opms.R;
import com.opms.data.model.request.PermissionItemRequest;
import com.opms.data.model.request.PermissionRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.PermissionCompleteResponse;
import com.opms.data.model.response.PermissionItemResponse;
import com.opms.data.model.response.PermissionResponse;
import com.opms.data.repository.PermissionRepository;
import com.opms.databinding.ActivityPermissionEditBinding;
import com.opms.ui.system.adapter.BusinessModuleAdapter;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class PermissionEditActivity extends AppCompatActivity
        implements BusinessModuleAdapter.OnModuleSelectionChangeListener {

    private static final String TAG = "PermissionEdit";

    @Inject
    PermissionRepository permissionRepository;

    private ActivityPermissionEditBinding binding;
    private BusinessModuleAdapter moduleAdapter;
    private boolean isEditMode = false;
    private int permissionId = -1;
    private List<PermissionResponse> allPermissions;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityPermissionEditBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setupToolbar();
        setupRecyclerView();
        setupButtons();
        loadExistingData();
        loadAllPermissions();
    }

    private void setupToolbar() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
    }

    private void setupRecyclerView() {
        moduleAdapter = new BusinessModuleAdapter(this);
        moduleAdapter.setOnModuleSelectionChangeListener(this);

        binding.rvBusinessModules.setLayoutManager(new LinearLayoutManager(this));
        binding.rvBusinessModules.setAdapter(moduleAdapter);
    }

    private void setupButtons() {
        binding.btnSave.setOnClickListener(v -> savePermission());
        binding.btnCancel.setOnClickListener(v -> finish());

        // 全选/全不选按钮
        binding.btnSelectAll.setOnClickListener(v -> {
            if (moduleAdapter.isAllSelected()) {
                moduleAdapter.clearAll();
                binding.btnSelectAll.setText("全选");
            } else {
                moduleAdapter.selectAll();
                binding.btnSelectAll.setText("全不选");
            }
        });
    }

    private void loadExistingData() {
        // 检查是否是编辑模式
        permissionId = getIntent().getIntExtra("permission_id", -1);
        if (permissionId != -1) {
            isEditMode = true;
            if (getSupportActionBar() != null) {
                getSupportActionBar().setTitle("编辑权限");
            }

            // 通过API获取权限详细信息
            loadPermissionDetails();
        } else {
            if (getSupportActionBar() != null) {
                getSupportActionBar().setTitle("添加权限");
            }
        }
    }

    private void loadPermissionDetails() {
        // 显示加载状态
        showLoading(true);

        permissionRepository.getPermissionsById(permissionId).enqueue(new Callback<ApiResponse<PermissionCompleteResponse>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<PermissionCompleteResponse>> call,
                                   @NonNull Response<ApiResponse<PermissionCompleteResponse>> response) {
                showLoading(false);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<PermissionCompleteResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        PermissionCompleteResponse permissionComplete = apiResponse.getData();
                        fillPermissionData(permissionComplete);
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "获取权限详情失败");
                    }
                } else {
                    showError("获取权限详情失败");
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<PermissionCompleteResponse>> call, @NonNull Throwable t) {
                showLoading(false);
                Log.e(TAG, "获取权限详情失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    private void showLoading(boolean isLoading) {
        if (isLoading) {
            binding.progressLoading.setVisibility(android.view.View.VISIBLE);
            binding.scrollContent.setVisibility(android.view.View.GONE);
            binding.btnSave.setEnabled(false);
        } else {
            binding.progressLoading.setVisibility(android.view.View.GONE);
            binding.scrollContent.setVisibility(android.view.View.VISIBLE);
            binding.btnSave.setEnabled(true);
        }
    }

    private void fillPermissionData(PermissionCompleteResponse permissionComplete) {
        PermissionResponse permission = permissionComplete.getPermission();
        List<PermissionItemResponse> modules = permissionComplete.getModules();
        // 填充基本信息
        binding.etName.setText(permission.getName());
        binding.etCode.setText(permission.getCode());
        binding.etCode.setEnabled(false); // 编辑时不允许修改代码
        binding.etDescription.setText(permission.getDescription());

        // 设置状态
        String status = permission.getStatus();
        binding.switchStatus.setChecked("1".equals(status) || "ACTIVE".equals(status));

        // 加载模块选择状态
        loadModuleSelections(modules);
    }

    private void loadModuleSelections(List<PermissionItemResponse> modules) {
        // 从PermissionResponse的modules获取选中状态
        if (modules != null) {
            Set<String> selectedSet = new HashSet<>();
            for (PermissionItemResponse module : modules) {
                selectedSet.add(module.getCode());
            }
            moduleAdapter.setSelectedModules(selectedSet);
            updateSelectAllButton();
        }
    }

    private void loadAllPermissions() {
        permissionRepository.getPermissions().enqueue(new Callback<ApiResponse<List<PermissionResponse>>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<List<PermissionResponse>>> call,
                                   @NonNull Response<ApiResponse<List<PermissionResponse>>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<List<PermissionResponse>> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        allPermissions = apiResponse.getData();
                    }
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<List<PermissionResponse>>> call, @NonNull Throwable t) {
                Log.e(TAG, "获取权限列表失败: " + t.getMessage(), t);
            }
        });
    }

    private void savePermission() {
        if (!validateInput()) {
            return;
        }

        String name = binding.etName.getText().toString().trim();
        String code = binding.etCode.getText().toString().trim();
        String description = binding.etDescription.getText().toString().trim();
        boolean status = binding.switchStatus.isChecked();

        PermissionRequest request = new PermissionRequest();
        request.setName(name);
        request.setCode(code);
        request.setDescription(description);
        request.setStatus(status ? "1" : "0");

        // 设置选中的模块
        List<PermissionItemRequest> moduleRequests = new ArrayList<>();
        Set<String> selectedCodes = moduleAdapter.getSelectedModuleCodes();
        for (String moduleCode : selectedCodes) {
            PermissionItemRequest itemRequest = new PermissionItemRequest();
            itemRequest.setCode(moduleCode);
            itemRequest.setParentCode(code);
            moduleRequests.add(itemRequest);
        }
        request.setModules(moduleRequests);

        if (isEditMode) {
            request.setId(permissionId);
            updatePermission(request);
        } else {
            createPermission(request);
        }
    }

    private boolean validateInput() {
        boolean isValid = true;

        // 验证名称
        String name = binding.etName.getText().toString().trim();
        if (name.isEmpty()) {
            binding.tilName.setError("权限名称不能为空");
            isValid = false;
        } else {
            binding.tilName.setError(null);
        }

        // 验证代码
        String code = binding.etCode.getText().toString().trim();
        if (code.isEmpty()) {
            binding.tilCode.setError("权限代码不能为空");
            isValid = false;
        } else {
            binding.tilCode.setError(null);
        }

        // 检查代码是否重复（基于内存中的数据）
        if (isValid && allPermissions != null) {
            for (PermissionResponse permission : allPermissions) {
                // 如果是编辑模式，跳过当前编辑的权限
                if (isEditMode && permission.getId() == permissionId) {
                    continue;
                }

                if (code.equals(permission.getCode())) {
                    String status = permission.getStatus();
                    String statusText = "1".equals(status) || "ACTIVE".equals(status) ? "" : "(已禁用)";
                    String msg = "权限代码[" + code + "]已存在[" + permission.getName() + "]" + statusText;
                    binding.tilCode.setError(msg);
                    isValid = false;
                    break;
                }
            }
        }

        // 验证至少选择一个模块
        if (moduleAdapter.getSelectedCount() == 0) {
            showError("请至少选择一个业务模块");
            isValid = false;
        }

        return isValid;
    }

    private void createPermission(PermissionRequest request) {
        binding.btnSave.setEnabled(false);

        permissionRepository.createPermission(request).enqueue(new Callback<ApiResponse<PermissionResponse>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<PermissionResponse>> call,
                                   @NonNull Response<ApiResponse<PermissionResponse>> response) {
                binding.btnSave.setEnabled(true);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<PermissionResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        Toast.makeText(PermissionEditActivity.this, "权限创建成功", Toast.LENGTH_SHORT).show();
                        setResult(RESULT_OK);
                        finish();
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "创建失败");
                    }
                } else {
                    showError("创建失败");
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<PermissionResponse>> call, @NonNull Throwable t) {
                binding.btnSave.setEnabled(true);
                Log.e(TAG, "创建权限失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    private void updatePermission(PermissionRequest request) {
        binding.btnSave.setEnabled(false);

        permissionRepository.updatePermission(permissionId, request)
                .enqueue(new Callback<ApiResponse<PermissionResponse>>() {
                    @Override
                    public void onResponse(@NonNull Call<ApiResponse<PermissionResponse>> call,
                                           @NonNull Response<ApiResponse<PermissionResponse>> response) {
                        binding.btnSave.setEnabled(true);

                        if (response.isSuccessful() && response.body() != null) {
                            ApiResponse<PermissionResponse> apiResponse = response.body();
                            if (apiResponse.isSuccess()) {
                                Toast.makeText(PermissionEditActivity.this, "权限更新成功", Toast.LENGTH_SHORT).show();
                                setResult(RESULT_OK);
                                finish();
                            } else {
                                showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "更新失败");
                            }
                        } else {
                            showError("更新失败");
                        }
                    }

                    @Override
                    public void onFailure(@NonNull Call<ApiResponse<PermissionResponse>> call, @NonNull Throwable t) {
                        binding.btnSave.setEnabled(true);
                        Log.e(TAG, "更新权限失败: " + t.getMessage(), t);
                        showError("网络错误: " + t.getMessage());
                    }
                });
    }

    private void showError(String message) {
        Snackbar.make(binding.getRoot(), message, Snackbar.LENGTH_LONG).show();
    }

    private void updateSelectAllButton() {
        if (moduleAdapter.isAllSelected()) {
            binding.btnSelectAll.setText("全不选");
        } else {
            binding.btnSelectAll.setText("全选");
        }
    }

    @Override
    public void onSelectionChanged(int selectedCount, int totalCount) {
        updateSelectAllButton();
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void finish() {
        super.finish();
        // 添加返回动画
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right);
    }
}
