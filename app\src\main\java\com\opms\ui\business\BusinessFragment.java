package com.opms.ui.business;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.google.android.material.snackbar.Snackbar;
import com.opms.R;
import com.opms.databinding.FragmentBusinessBinding;

import dagger.hilt.android.AndroidEntryPoint;

@AndroidEntryPoint
public class BusinessFragment extends Fragment {
    private FragmentBusinessBinding binding;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentBusinessBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        setupClickListeners(view);
    }

    private void setupClickListeners(View view) {
        // 客户管理
        LinearLayout llCustomerManagement = view.findViewById(R.id.ll_customer_management);
        llCustomerManagement.setOnClickListener(v -> {
            try {
                Intent intent = new Intent(getActivity(), CustomerManagementActivity.class);
                startActivity(intent);
            } catch (Exception e) {
                e.printStackTrace();
                if (getView() != null) {
                    Snackbar.make(getView(), "启动客户管理页面失败: " + e.getMessage(), Snackbar.LENGTH_SHORT).show();
                }
            }
        });

        // 产品管理
        LinearLayout llProductManagement = view.findViewById(R.id.ll_product_management);
        llProductManagement.setOnClickListener(v -> {
            try {
                Intent intent = new Intent(getActivity(), ProductManagementActivity.class);
                startActivity(intent);
            } catch (Exception e) {
                e.printStackTrace();
                if (getView() != null) {
                    Snackbar.make(getView(), "启动产品管理页面失败: " + e.getMessage(), Snackbar.LENGTH_SHORT).show();
                }
            }
        });

        // 订单录入
        LinearLayout llOrderEntry = view.findViewById(R.id.ll_order_entry);
        llOrderEntry.setOnClickListener(v -> {
            showComingSoon("订单录入");
        });

        // 订单管理
        LinearLayout llOrderManagement = view.findViewById(R.id.ll_order_management);
        llOrderManagement.setOnClickListener(v -> {
            try {
                Intent intent = new Intent(getActivity(), OrderManagementActivity.class);
                startActivity(intent);
            } catch (Exception e) {
                e.printStackTrace();
                if (getView() != null) {
                    Snackbar.make(getView(), "启动订单管理页面失败: " + e.getMessage(), Snackbar.LENGTH_SHORT).show();
                }
            }
        });

        // 部件管理
        LinearLayout llComponentManagement = view.findViewById(R.id.ll_component_management);
        llComponentManagement.setOnClickListener(v -> {
            try {
                Intent intent = new Intent(getActivity(), ComponentManagementActivity.class);
                startActivity(intent);
            } catch (Exception e) {
                e.printStackTrace();
                if (getView() != null) {
                    Snackbar.make(getView(), "启动部件管理页面失败: " + e.getMessage(), Snackbar.LENGTH_SHORT).show();
                }
            }
        });

        // 订单排期
        LinearLayout llOrderScheduling = view.findViewById(R.id.ll_order_scheduling);
        llOrderScheduling.setOnClickListener(v -> {
            showComingSoon("订单排期");
        });

        // 生产跟踪
        LinearLayout llProductionTracking = view.findViewById(R.id.ll_production_tracking);
        llProductionTracking.setOnClickListener(v -> {
            showComingSoon("生产跟踪");
        });

        // 产品质检
        LinearLayout llQualityInspection = view.findViewById(R.id.ll_quality_inspection);
        llQualityInspection.setOnClickListener(v -> {
            showComingSoon("产品质检");
        });

        // 产品入库
        LinearLayout llProductInbound = view.findViewById(R.id.ll_product_inbound);
        llProductInbound.setOnClickListener(v -> {
            showComingSoon("产品入库");
        });

        // 产品出库
        LinearLayout llProductOutbound = view.findViewById(R.id.ll_product_outbound);
        llProductOutbound.setOnClickListener(v -> {
            showComingSoon("产品出库");
        });
    }

    private void showComingSoon(String moduleName) {
        if (getView() != null) {
            Snackbar.make(getView(), moduleName + "功能即将上线", Snackbar.LENGTH_SHORT).show();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}