package com.opms.ui.system;

import android.app.DatePickerDialog;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.AutoCompleteTextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.opms.R;
import com.opms.common.enums.Gender;
import com.opms.common.enums.RoleType;
import com.opms.common.utils.AvatarCacheUtils;
import com.opms.data.model.request.RegisterRequest;
import com.opms.data.model.request.UserRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.DepartmentResponse;
import com.opms.data.model.response.PermissionResponse;
import com.opms.data.model.response.PositionResponse;
import com.opms.data.model.response.PostResponse;
import com.opms.data.model.response.UserResponse;
import com.opms.data.remote.ApiService;
import com.opms.databinding.ActivityUserEditBinding;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Locale;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class UserEditActivity extends AppCompatActivity {
    public static final String EXTRA_USERNAME = "extra_username";
    private static final String TAG = "UserEditActivity";
    private final Calendar calendar = Calendar.getInstance();
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
    @Inject
    ApiService apiService;
    private ActivityUserEditBinding binding;
    private UserResponse currentUser;
    // 下拉选项数据
    private List<DropdownItem> genderItems = new ArrayList<>();
    private List<DropdownItem> roleItems = new ArrayList<>();
    private List<DropdownItem> departmentItems = new ArrayList<>();
    private List<DropdownItem> positionItems = new ArrayList<>();
    private List<DropdownItem> jobItems = new ArrayList<>();
    private List<DropdownItem> permissionTemplateItems = new ArrayList<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityUserEditBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // 设置返回按钮
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("编辑用户信息");
        }

        String username = getIntent().getStringExtra(EXTRA_USERNAME);
        if (TextUtils.isEmpty(username)) {
            Toast.makeText(this, "用户名不能为空", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }

        setupDropdowns();
        loadDropdownData();
        setupDatePicker();
        setupButtons();
        loadUserData(username);
    }

    private void setupDropdowns() {
        // 设置性别下拉选项
        genderItems.clear();
        genderItems.add(new DropdownItem(Gender.MALE.getCode(), Gender.MALE.getName()));
        genderItems.add(new DropdownItem(Gender.FEMALE.getCode(), Gender.FEMALE.getName()));
        ArrayAdapter<DropdownItem> genderAdapter = new ArrayAdapter<DropdownItem>(
                this, android.R.layout.simple_dropdown_item_1line, genderItems);
        ((AutoCompleteTextView) binding.actGender).setAdapter(genderAdapter);

        // 设置角色类型下拉选项
        roleItems.clear();
        roleItems.add(new DropdownItem(RoleType.ADMIN.getCode(), RoleType.ADMIN.getName()));
        roleItems.add(new DropdownItem(RoleType.USER.getCode(), RoleType.USER.getName()));
        ArrayAdapter<DropdownItem> roleAdapter = new ArrayAdapter<DropdownItem>(
                this, android.R.layout.simple_dropdown_item_1line, roleItems);
        ((AutoCompleteTextView) binding.actRole).setAdapter(roleAdapter);

        // 其他下拉选项将在loadDropdownData()中设置
    }

    private void setupDatePicker() {
        binding.etBirthday.setOnClickListener(v -> showDatePicker());
    }

    private void showDatePicker() {
        DatePickerDialog datePickerDialog = new DatePickerDialog(
                this,
                (view, year, month, dayOfMonth) -> {
                    calendar.set(Calendar.YEAR, year);
                    calendar.set(Calendar.MONTH, month);
                    calendar.set(Calendar.DAY_OF_MONTH, dayOfMonth);
                    binding.etBirthday.setText(dateFormat.format(calendar.getTime()));
                },
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH));
        datePickerDialog.show();
    }

    private void setupButtons() {
        binding.btnSave.setOnClickListener(v -> saveUserData());
        binding.btnCancel.setOnClickListener(v -> finish());
        binding.btnDeleteUser.setOnClickListener(v -> showDeleteConfirmDialog());
    }

    private void loadUserData(String username) {
        showLoading(true);
        apiService.findByUsername(username).enqueue(new Callback<ApiResponse<UserResponse>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<UserResponse>> call, @NonNull Response<ApiResponse<UserResponse>> response) {
                showLoading(false);
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<UserResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        currentUser = apiResponse.getData();
                        updateUI(currentUser);
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "获取用户信息失败");
                    }
                } else {
                    showError("获取用户信息失败");
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<UserResponse>> call, @NonNull Throwable t) {
                showLoading(false);
                Log.e(TAG, "获取用户信息失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    private void loadDropdownData() {
        // 加载部门数据
        apiService.getDepartments().enqueue(new Callback<ApiResponse<List<DepartmentResponse>>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<List<DepartmentResponse>>> call, @NonNull Response<ApiResponse<List<DepartmentResponse>>> response) {
                if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                    List<DepartmentResponse> departmentList = response.body().getData();
                    if (departmentList != null) {
                        departmentItems.clear();
                        for (DepartmentResponse department : departmentList) {
                            departmentItems.add(new DropdownItem(department.getCode(), department.getName() + (department.getStatus().equals("1") ? "" : ("(" + department.getCode() + "已禁用)"))));
                        }
                        ArrayAdapter<DropdownItem> adapter = new ArrayAdapter<DropdownItem>(
                                UserEditActivity.this, android.R.layout.simple_dropdown_item_1line, departmentItems);
                        ((AutoCompleteTextView) binding.actDepartment).setAdapter(adapter);

                        // 如果用户数据已经加载，重新设置部门选择
                        if (currentUser != null) {
                            setDropdownSelection(binding.actDepartment, departmentItems, currentUser.getDepartment());
                        }
                    }
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<List<DepartmentResponse>>> call, @NonNull Throwable t) {
                Log.e(TAG, "加载部门数据失败: " + t.getMessage(), t);
            }
        });

        // 加载职位数据
        apiService.getPositions().enqueue(new Callback<ApiResponse<List<PositionResponse>>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<List<PositionResponse>>> call, @NonNull Response<ApiResponse<List<PositionResponse>>> response) {
                if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                    List<PositionResponse> positionList = response.body().getData();
                    if (positionList != null) {
                        positionItems.clear();
                        for (PositionResponse position : positionList) {
                            positionItems.add(new DropdownItem(position.getCode(), position.getName() + (position.getStatus().equals("1") ? "" : ("(" + position.getCode() + "已禁用)"))));
                        }
                        ArrayAdapter<DropdownItem> adapter = new ArrayAdapter<DropdownItem>(
                                UserEditActivity.this, android.R.layout.simple_dropdown_item_1line, positionItems);
                        ((AutoCompleteTextView) binding.actPosition).setAdapter(adapter);

                        // 如果用户数据已经加载，重新设置职位选择
                        if (currentUser != null) {
                            setDropdownSelection(binding.actPosition, positionItems, currentUser.getPosition());
                        }
                    }
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<List<PositionResponse>>> call, @NonNull Throwable t) {
                Log.e(TAG, "加载职位数据失败: " + t.getMessage(), t);
            }
        });

        // 加载岗位数据
        apiService.getPosts().enqueue(new Callback<ApiResponse<List<PostResponse>>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<List<PostResponse>>> call, @NonNull Response<ApiResponse<List<PostResponse>>> response) {
                if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                    List<PostResponse> postList = response.body().getData();
                    if (postList != null) {
                        jobItems.clear();
                        for (PostResponse post : postList) {
                            jobItems.add(new DropdownItem(post.getCode(), post.getName() + (post.getStatus().equals("1") ? "" : ("(" + post.getCode() + "已禁用)"))));
                        }
                        ArrayAdapter<DropdownItem> adapter = new ArrayAdapter<DropdownItem>(
                                UserEditActivity.this, android.R.layout.simple_dropdown_item_1line, jobItems);
                        ((AutoCompleteTextView) binding.actJob).setAdapter(adapter);

                        // 如果用户数据已经加载，重新设置岗位选择
                        if (currentUser != null) {
                            setDropdownSelection(binding.actJob, jobItems, currentUser.getJob());
                        }
                    }
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<List<PostResponse>>> call, @NonNull Throwable t) {
                Log.e(TAG, "加载岗位数据失败: " + t.getMessage(), t);
            }
        });

        // 权限模板数据
        apiService.getPermissions().enqueue(new Callback<ApiResponse<List<PermissionResponse>>>() {
            @Override
            public void onResponse(Call<ApiResponse<List<PermissionResponse>>> call, Response<ApiResponse<List<PermissionResponse>>> response) {
                if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                    List<PermissionResponse> permissionList = response.body().getData();
                    if (permissionList != null) {
                        permissionTemplateItems.clear();
                        for (PermissionResponse permission : permissionList) {
                            permissionTemplateItems.add(new DropdownItem(permission.getCode(), permission.getName() + (permission.getStatus().equals("1") ? "" : ("(" + permission.getCode() + "已禁用)"))));
                        }
                        ArrayAdapter<DropdownItem> permissionAdapter = new ArrayAdapter<DropdownItem>(
                                UserEditActivity.this, android.R.layout.simple_dropdown_item_1line, permissionTemplateItems);
                        ((AutoCompleteTextView) binding.actPermissionTemplate).setAdapter(permissionAdapter);

                        // 如果用户数据已经加载，重新设置权限模板选择
                        if (currentUser != null) {
                            setDropdownSelection(binding.actPermissionTemplate, permissionTemplateItems, currentUser.getPermissionTemplate());
                        }
                    }
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<List<PermissionResponse>>> call, Throwable t) {
                Log.e(TAG, "加载权限数据失败: " + t.getMessage(), t);
            }
        });
    }

    private void updateUI(UserResponse user) {
        if (user == null) return;

        // 设置用户名和头像
        binding.tvUsername.setText(user.getUsername());
        loadUserAvatar(user.getAvatarUrl());

        // 设置基本信息
        binding.etName.setText(user.getName());
        binding.etBirthday.setText(user.getBirthday());
        binding.etIdCard.setText(user.getIdCard());
        binding.etPhone.setText(user.getPhone());

        // 设置工作信息
        binding.etEmployeeId.setText(user.getEmployeeId());
        binding.etRemark.setText(user.getRemark());

        // 设置注册时间
        binding.tvRegisterTime.setText(user.getRegisterTime());

        // 设置日历初始日期
        try {
            if (!TextUtils.isEmpty(user.getBirthday())) {
                calendar.setTime(dateFormat.parse(user.getBirthday()));
            }
        } catch (Exception e) {
            Log.e(TAG, "解析日期失败: " + e.getMessage(), e);
        }

        // 延迟设置下拉框选择，确保适配器已经设置好
        binding.getRoot().post(new Runnable() {
            @Override
            public void run() {
                setDropdownSelections(user);
            }
        });
    }

    /**
     * 设置所有下拉框的选择
     */
    private void setDropdownSelections(UserResponse user) {
        // 设置性别选择 - 使用代码进行匹配
        setDropdownSelection(binding.actGender, genderItems, user.getGenderCode());

        // 设置角色选择 - 使用代码进行匹配
        setDropdownSelection(binding.actRole, roleItems, user.getRoleCode());

        // 设置部门、职位、岗位、权限模板选择
        setDropdownSelection(binding.actDepartment, departmentItems, user.getDepartment());
        setDropdownSelection(binding.actPosition, positionItems, user.getPosition());
        setDropdownSelection(binding.actJob, jobItems, user.getJob());
        setDropdownSelection(binding.actPermissionTemplate, permissionTemplateItems, user.getPermissionTemplate());
    }

    private void saveUserData() {
        if (!validateInput()) {
            return;
        }

        showLoading(true);
        RegisterRequest updatedUser = prepareUpdatedUserRequest();

        apiService.updateUser(currentUser.getUsername(), updatedUser).enqueue(new Callback<ApiResponse<UserResponse>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<UserResponse>> call, @NonNull Response<ApiResponse<UserResponse>> response) {
                showLoading(false);
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<UserResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        Toast.makeText(UserEditActivity.this, "用户信息更新成功", Toast.LENGTH_SHORT).show();
                        setResult(RESULT_OK);
                        finish();
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "更新失败");
                    }
                } else {
                    showError("更新失败");
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<UserResponse>> call, @NonNull Throwable t) {
                showLoading(false);
                Log.e(TAG, "更新用户信息失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    private boolean validateInput() {
        boolean isValid = true;

        // 验证姓名
        String name = binding.etName.getText().toString().trim();
        if (TextUtils.isEmpty(name)) {
            binding.tilName.setError("姓名不能为空");
            isValid = false;
        } else {
            binding.tilName.setError(null);
        }

        // 验证性别
        String gender = binding.actGender.getText().toString().trim();
        if (TextUtils.isEmpty(gender)) {
            binding.tilGender.setError("性别不能为空");
            isValid = false;
        } else {
            binding.tilGender.setError(null);
        }

        // 验证出生年月日
        String birthday = binding.etBirthday.getText().toString().trim();
        if (TextUtils.isEmpty(birthday)) {
            binding.tilBirthday.setError("出生年月日不能为空");
            isValid = false;
        } else {
            binding.tilBirthday.setError(null);
        }

        // 验证身份证号码
        String idCard = binding.etIdCard.getText().toString().trim();
        if (TextUtils.isEmpty(idCard)) {
            binding.tilIdCard.setError("身份证号码不能为空");
            isValid = false;
        } else {
            binding.tilIdCard.setError(null);
        }

        // 验证电话
        String phone = binding.etPhone.getText().toString().trim();
        if (TextUtils.isEmpty(phone)) {
            binding.tilPhone.setError("电话不能为空");
            isValid = false;
        } else {
            binding.tilPhone.setError(null);
        }

        // 验证工号
        String employeeId = binding.etEmployeeId.getText().toString().trim();
        if (TextUtils.isEmpty(employeeId)) {
            binding.tilEmployeeId.setError("工号不能为空");
            isValid = false;
        } else {
            binding.tilEmployeeId.setError(null);
        }

        // 验证角色类型
        String role = binding.actRole.getText().toString().trim();
        if (TextUtils.isEmpty(role)) {
            binding.tilRole.setError("角色类型不能为空");
            isValid = false;
        } else {
            binding.tilRole.setError(null);
        }

        // 验证部门
        String department = binding.actDepartment.getText().toString().trim();
        if (TextUtils.isEmpty(department)) {
            binding.tilDepartment.setError("部门不能为空");
            isValid = false;
        } else {
            binding.tilDepartment.setError(null);
        }

        // 验证职位
        String position = binding.actPosition.getText().toString().trim();
        if (TextUtils.isEmpty(position)) {
            binding.tilPosition.setError("职位不能为空");
            isValid = false;
        } else {
            binding.tilPosition.setError(null);
        }

        return isValid;
    }

    private RegisterRequest prepareUpdatedUserRequest() {
        RegisterRequest updatedUser = new RegisterRequest();

        // 设置基本信息
        updatedUser.setUsername(currentUser.getUsername());
        updatedUser.setName(binding.etName.getText().toString().trim());

        // 将显示的性别名称转换为代码
        String genderName = binding.actGender.getText().toString().trim();
        updatedUser.setGender(getCodeByName(genderItems, genderName));

        updatedUser.setBirthday(binding.etBirthday.getText().toString().trim());
        updatedUser.setIdCard(binding.etIdCard.getText().toString().trim());
        updatedUser.setPhone(binding.etPhone.getText().toString().trim());
        updatedUser.setEmployeeId(binding.etEmployeeId.getText().toString().trim());

        // 将显示的角色名称转换为代码
        String roleName = binding.actRole.getText().toString().trim();
        updatedUser.setRole(getCodeByName(roleItems, roleName));

        // 将显示的部门、职位、岗位、权限模板名称转换为代码
        String departmentName = binding.actDepartment.getText().toString().trim();
        updatedUser.setDepartment(getCodeByName(departmentItems, departmentName));

        String positionName = binding.actPosition.getText().toString().trim();
        updatedUser.setPosition(getCodeByName(positionItems, positionName));

        String jobName = binding.actJob.getText().toString().trim();
        updatedUser.setJob(getCodeByName(jobItems, jobName));

        String permissionTemplateName = binding.actPermissionTemplate.getText().toString().trim();
        updatedUser.setPermissionTemplate(getCodeByName(permissionTemplateItems, permissionTemplateName));

        updatedUser.setRemark(binding.etRemark.getText().toString().trim());

        return updatedUser;
    }

    /**
     * 设置下拉框的选中项
     *
     * @param autoCompleteTextView 下拉框控件
     * @param items                选项列表
     * @param value                要选中的值（可能是代码或名称）
     */
    private void setDropdownSelection(AutoCompleteTextView autoCompleteTextView, List<DropdownItem> items, String value) {
        if (value == null || items == null) {
            Log.d(TAG, "setDropdownSelection: value or items is null");
            return;
        }

        String trimmedValue = value.trim();
        Log.d(TAG, "setDropdownSelection: looking for value '" + trimmedValue + "' in " + items.size() + " items");

        // 首先尝试按代码匹配
        for (DropdownItem item : items) {
            Log.d(TAG, "setDropdownSelection: comparing code '" + trimmedValue + "' with '" + item.getCode() + "'");
            if (trimmedValue.equals(item.getCode()) || trimmedValue.equalsIgnoreCase(item.getCode())) {
                Log.d(TAG, "setDropdownSelection: CODE MATCH FOUND! Setting text to '" + item.getName() + "'");
                autoCompleteTextView.setText(item.getName(), false);
                return;
            }
        }

        // 如果按代码没有匹配，尝试按名称匹配
        for (DropdownItem item : items) {
            Log.d(TAG, "setDropdownSelection: comparing name '" + trimmedValue + "' with '" + item.getName() + "'");
            if (trimmedValue.equals(item.getName()) || trimmedValue.equalsIgnoreCase(item.getName())) {
                Log.d(TAG, "setDropdownSelection: NAME MATCH FOUND! Setting text to '" + item.getName() + "'");
                autoCompleteTextView.setText(item.getName(), false);
                return;
            }
        }

        Log.d(TAG, "setDropdownSelection: NO MATCH FOUND for value '" + trimmedValue + "'");
    }

    /**
     * 根据显示名称获取对应的代码
     *
     * @param items 选项列表
     * @param name  显示名称
     * @return 对应的代码
     */
    private String getCodeByName(List<DropdownItem> items, String name) {
        if (name == null || items == null) return "";

        for (DropdownItem item : items) {
            if (name.equals(item.getName())) {
                return item.getCode();
            }
        }
        return "";
    }

    private void showLoading(boolean isLoading) {
        binding.progressBar.setVisibility(isLoading ? View.VISIBLE : View.GONE);
    }

    private void showError(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }

    /**
     * 显示删除确认对话框
     */
    private void showDeleteConfirmDialog() {
        new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("删除用户")
                .setMessage("确定要删除用户 \"" + currentUser.getUsername() + "\" 吗？\n\n此操作不可撤销！")
                .setPositiveButton("删除", (dialog, which) -> deleteUser())
                .setNegativeButton("取消", null)
                .show();
    }

    /**
     * 删除用户
     */
    private void deleteUser() {
        if (currentUser == null) {
            showError("用户信息不存在");
            return;
        }

        showLoading(true);

        // 创建删除请求
        UserRequest deleteRequest = new UserRequest();
        deleteRequest.setUsername(currentUser.getUsername());

        apiService.deleteUser(deleteRequest).enqueue(new Callback<ApiResponse<Void>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<Void>> call, @NonNull Response<ApiResponse<Void>> response) {
                showLoading(false);
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<Void> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        Toast.makeText(UserEditActivity.this, "用户删除成功", Toast.LENGTH_SHORT).show();
                        setResult(RESULT_OK);
                        finish();
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "删除失败");
                    }
                } else {
                    showError("删除失败");
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<Void>> call, @NonNull Throwable t) {
                showLoading(false);
                Log.e(TAG, "删除用户失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void finish() {
        super.finish();
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right);
    }

    private void loadUserAvatar(String avatarUrl) {
        if (avatarUrl == null || avatarUrl.isEmpty()) {
            Log.d(TAG, "Avatar URL is null or empty, using default avatar");
            binding.ivAvatar.setImageResource(R.drawable.ic_default_avatar);
            return;
        }

        Log.d(TAG, "Loading user avatar from URL: " + avatarUrl);

        // 直接使用URL加载图像
        String processedImageUrl = com.opms.common.utils.ImageUtils.processImageUrl(avatarUrl);
        Glide.with(this)
                .load(processedImageUrl)
                .placeholder(R.drawable.ic_default_avatar)
                .error(R.drawable.ic_default_avatar)
                .circleCrop()
                .into(binding.ivAvatar);
    }


}
