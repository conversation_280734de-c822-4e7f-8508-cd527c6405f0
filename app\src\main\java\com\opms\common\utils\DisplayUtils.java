package com.opms.common.utils;

/**
 * 显示格式化工具类
 */
public class DisplayUtils {

    /**
     * 格式化部门显示文本
     * 如果有编码则显示：名称（编码）
     * 如果没有编码则只显示名称
     *
     * @param name 部门名称
     * @param code 部门编码
     * @return 格式化的显示文本
     */
    public static String formatDepartmentDisplay(String name, String code) {
        if (name == null || name.trim().isEmpty()) {
            return "";
        }

        if (code != null && !code.trim().isEmpty()) {
            return name + "（" + code + "）";
        } else {
            return name;
        }
    }

    /**
     * 格式化职位显示文本
     * 如果有编码则显示：名称（编码）
     * 如果没有编码则只显示名称
     *
     * @param name 职位名称
     * @param code 职位编码
     * @return 格式化的显示文本
     */
    public static String formatPositionDisplay(String name, String code) {
        if (name == null || name.trim().isEmpty()) {
            return "";
        }

        if (code != null && !code.trim().isEmpty()) {
            return name + "（" + code + "）";
        } else {
            return name;
        }
    }

    /**
     * 从部门名称中提取编码（如果存在）
     * 支持格式：部门名称（编码）
     *
     * @param departmentText 部门文本
     * @return 提取的编码，如果没有则返回null
     */
    public static String extractDepartmentCode(String departmentText) {
        if (departmentText == null || departmentText.trim().isEmpty()) {
            return null;
        }

        // 检查是否包含（）格式
        int startIndex = departmentText.lastIndexOf("（");
        int endIndex = departmentText.lastIndexOf("）");

        if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
            return departmentText.substring(startIndex + 1, endIndex);
        }

        return null;
    }

    /**
     * 从职位名称中提取编码（如果存在）
     * 支持格式：职位名称（编码）
     *
     * @param positionText 职位文本
     * @return 提取的编码，如果没有则返回null
     */
    public static String extractPositionCode(String positionText) {
        if (positionText == null || positionText.trim().isEmpty()) {
            return null;
        }

        // 检查是否包含（）格式
        int startIndex = positionText.lastIndexOf("（");
        int endIndex = positionText.lastIndexOf("）");

        if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
            return positionText.substring(startIndex + 1, endIndex);
        }

        return null;
    }

    /**
     * 从格式化文本中提取名称部分
     * 支持格式：名称（编码）
     *
     * @param formattedText 格式化的文本
     * @return 提取的名称，如果没有括号则返回原文本
     */
    public static String extractName(String formattedText) {
        if (formattedText == null || formattedText.trim().isEmpty()) {
            return "";
        }

        int startIndex = formattedText.lastIndexOf("（");
        if (startIndex != -1) {
            return formattedText.substring(0, startIndex);
        }

        return formattedText;
    }

    /**
     * 生成模拟的部门编码
     * 基于部门名称生成简单的编码
     *
     * @param departmentName 部门名称
     * @return 生成的编码
     */
    public static String generateDepartmentCode(String departmentName) {
        if (departmentName == null || departmentName.trim().isEmpty()) {
            return "DEPT000";
        }

        // 简单的编码生成逻辑
        String name = departmentName.trim();
        if (name.contains("技术")) {
            return "TECH001";
        } else if (name.contains("销售")) {
            return "SALE001";
        } else if (name.contains("人事") || name.contains("HR")) {
            return "HR001";
        } else if (name.contains("财务")) {
            return "FIN001";
        } else if (name.contains("市场")) {
            return "MKT001";
        } else if (name.contains("运营")) {
            return "OPS001";
        } else {
            return "DEPT" + String.format("%03d", name.hashCode() % 1000);
        }
    }

    /**
     * 生成模拟的职位编码
     * 基于职位名称生成简单的编码
     *
     * @param positionName 职位名称
     * @return 生成的编码
     */
    public static String generatePositionCode(String positionName) {
        if (positionName == null || positionName.trim().isEmpty()) {
            return "POS000";
        }

        // 简单的编码生成逻辑
        String name = positionName.trim();
        if (name.contains("经理")) {
            return "MGR001";
        } else if (name.contains("工程师")) {
            return "ENG001";
        } else if (name.contains("主管")) {
            return "SUP001";
        } else if (name.contains("专员")) {
            return "SPE001";
        } else if (name.contains("助理")) {
            return "ASS001";
        } else if (name.contains("总监")) {
            return "DIR001";
        } else {
            return "POS" + String.format("%03d", name.hashCode() % 1000);
        }
    }
}
