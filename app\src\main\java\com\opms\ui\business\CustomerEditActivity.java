package com.opms.ui.business;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.ArrayAdapter;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import com.bumptech.glide.Glide;
import com.google.android.material.snackbar.Snackbar;
import com.opms.R;
import com.opms.common.enums.BusinessImgType;
import com.opms.common.utils.ImageUploadUtils;
import com.opms.common.utils.ImageUtils;
import com.opms.data.model.request.CustomerRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.CustomerResponse;
import com.opms.data.repository.CustomerRepository;
import com.opms.data.repository.ImageUploadRepository;
import com.opms.databinding.ActivityCustomerEditBinding;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class CustomerEditActivity extends AppCompatActivity {

    private static final String TAG = "CustomerEdit";

    @Inject
    CustomerRepository customerRepository;

    @Inject
    ImageUploadRepository imageUploadRepository;

    private ActivityCustomerEditBinding binding;
    private int customerId = -1;
    private boolean isEditMode = false;
    private Uri selectedImageUri;
    private ActivityResultLauncher<Intent> imagePickerLauncher;
    private ActivityResultLauncher<String> permissionLauncher;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityCustomerEditBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        getIntentData();
        setupImagePicker();
        setupToolbar();
        setupStatusSpinner();
        setupButtons();
        setupImageView();

        if (isEditMode) {
            loadCustomerDetail();
        }
    }

    private void getIntentData() {
        Intent intent = getIntent();
        if (intent != null && intent.hasExtra("customer_id")) {
            customerId = intent.getIntExtra("customer_id", -1);
            isEditMode = customerId != -1;
        }
    }

    private void setupImagePicker() {
        Log.d(TAG, "setupImagePicker: 初始化图片选择器");

        // 权限请求launcher
        permissionLauncher = registerForActivityResult(
                new ActivityResultContracts.RequestPermission(),
                isGranted -> {
                    Log.d(TAG, "权限请求结果: " + isGranted);
                    if (isGranted) {
                        launchImagePicker();
                    } else {
                        showError("需要存储权限才能选择图片");
                    }
                }
        );

        // 图片选择launcher
        imagePickerLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    Log.d(TAG, "图片选择器返回结果: " + result.getResultCode());
                    if (result.getResultCode() == RESULT_OK && result.getData() != null) {
                        selectedImageUri = result.getData().getData();
                        Log.d(TAG, "选中的图片URI: " + selectedImageUri);
                        if (selectedImageUri != null) {
                            // 显示选中的图片
                            ImageUtils.loadUserAvatar(this, selectedImageUri, binding.ivCustomerImage);
                            // 如果是编辑模式，立即上传图片
                            if (isEditMode) {
                                Log.d(TAG, "编辑模式，开始上传图片");
                                uploadCustomerImage();
                            } else {
                                Log.d(TAG, "新增模式，不上传图片");
                            }
                        } else {
                            Log.w(TAG, "选中的图片URI为空");
                        }
                    } else {
                        Log.w(TAG, "图片选择被取消或失败");
                    }
                }
        );
    }

    private void setupImageView() {
        Log.d(TAG, "setupImageView: 设置图片视图，编辑模式: " + isEditMode);
        // 只有在编辑模式下才允许点击图片
        if (isEditMode) {
            // 设置覆盖层的点击事件
            binding.viewImageOverlay.setOnClickListener(v -> {
                Log.d(TAG, "图片覆盖层被点击，打开图片选择器");
                openImagePicker();
            });
            binding.viewImageOverlay.setClickable(true);
            binding.viewImageOverlay.setFocusable(true);
            binding.tvImageHint.setVisibility(View.VISIBLE);

            // 也设置CardView的点击事件作为备用
            binding.cardImage.setOnClickListener(v -> {
                Log.d(TAG, "图片卡片被点击，打开图片选择器");
                openImagePicker();
            });

            Log.d(TAG, "setupImageView: 编辑模式图片点击事件已设置");
        } else {
            // 新增模式下禁用图片点击
            binding.viewImageOverlay.setOnClickListener(null);
            binding.viewImageOverlay.setClickable(false);
            binding.viewImageOverlay.setFocusable(false);
            binding.cardImage.setOnClickListener(null);
            binding.tvImageHint.setVisibility(View.GONE);

            Log.d(TAG, "setupImageView: 新增模式图片点击事件已禁用");
        }
    }

    private void setupToolbar() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle(isEditMode ? "编辑客户" : "新增客户");
        }
    }

    private void setupStatusSpinner() {
        String[] statusOptions = {"启用", "禁用"};
        ArrayAdapter<String> adapter = new ArrayAdapter<>(this,
                android.R.layout.simple_spinner_item, statusOptions);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        binding.spinnerStatus.setAdapter(adapter);
        binding.spinnerStatus.setSelection(0); // 默认选择启用
    }

    private void setupButtons() {
        binding.btnSave.setOnClickListener(v -> saveCustomer());
        binding.btnCancel.setOnClickListener(v -> finish());
    }

    private void loadCustomerDetail() {
        showLoading(true);

        Call<ApiResponse<CustomerResponse>> call = customerRepository.getCustomerDetail(customerId);
        call.enqueue(new Callback<ApiResponse<CustomerResponse>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<CustomerResponse>> call,
                                   @NonNull Response<ApiResponse<CustomerResponse>> response) {
                showLoading(false);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<CustomerResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        fillCustomerData(apiResponse.getData());
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "获取客户详情失败");
                        finish();
                    }
                } else {
                    showError("获取客户详情失败");
                    finish();
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<CustomerResponse>> call, @NonNull Throwable t) {
                showLoading(false);
                Log.e(TAG, "获取客户详情失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
                finish();
            }
        });
    }

    private void fillCustomerData(CustomerResponse customer) {
        binding.etCustomerName.setText(customer.getName());
        binding.etCustomerCode.setText(customer.getCode());
        binding.etCompanyName.setText(customer.getCompanyName());
        binding.etAddress.setText(customer.getAddress());
        binding.etContact.setText(customer.getContact());
        binding.etPhone.setText(customer.getPhone());
        binding.etRemark.setText(customer.getRemark());

        // 设置状态
        if ("1".equals(customer.getStatus())) {
            binding.spinnerStatus.setSelection(0); // 启用
        } else {
            binding.spinnerStatus.setSelection(1); // 禁用
        }

        // 加载客户图片 - 直接使用URL显示
        if (!TextUtils.isEmpty(customer.getImage())) {
            // 如果有图片URL，处理URL并使用Glide加载
            String processedImageUrl = com.opms.common.utils.ImageUtils.processImageUrl(customer.getImage());
            Glide.with(this)
                    .load(processedImageUrl)
                    .placeholder(R.drawable.ic_customer_management)
                    .error(R.drawable.ic_customer_management)
                    .into(binding.ivCustomerImage);
        } else {
            // 没有图片时显示默认图片
            binding.ivCustomerImage.setImageResource(R.drawable.ic_customer_management);
        }
    }

    private void saveCustomer() {
        if (!validateInput()) {
            return;
        }

        CustomerRequest request = createCustomerRequest();
        showLoading(true);

        Call<ApiResponse<CustomerResponse>> call;
        if (isEditMode) {
            call = customerRepository.updateCustomer(customerId, request);
        } else {
            call = customerRepository.createCustomer(request);
        }

        call.enqueue(new Callback<ApiResponse<CustomerResponse>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<CustomerResponse>> call,
                                   @NonNull Response<ApiResponse<CustomerResponse>> response) {
                showLoading(false);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<CustomerResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        String message = isEditMode ? "客户信息更新成功" : "客户添加成功";
                        Snackbar.make(binding.getRoot(), message, Snackbar.LENGTH_SHORT).show();

                        // 返回结果
                        setResult(RESULT_OK);
                        finish();
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "保存失败");
                    }
                } else {
                    showError("保存失败");
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<CustomerResponse>> call, @NonNull Throwable t) {
                showLoading(false);
                Log.e(TAG, "保存客户失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    private boolean validateInput() {
        // 客户名称验证
        String name = binding.etCustomerName.getText().toString().trim();
        if (TextUtils.isEmpty(name)) {
            binding.etCustomerName.setError("请输入客户名称");
            binding.etCustomerName.requestFocus();
            return false;
        }

        // 客户编码验证
        String code = binding.etCustomerCode.getText().toString().trim();
        if (TextUtils.isEmpty(code)) {
            binding.etCustomerCode.setError("请输入客户编码");
            binding.etCustomerCode.requestFocus();
            return false;
        }

        // 公司名称验证
        String companyName = binding.etCompanyName.getText().toString().trim();
        if (TextUtils.isEmpty(companyName)) {
            binding.etCompanyName.setError("请输入公司名称");
            binding.etCompanyName.requestFocus();
            return false;
        }

        // 联系人验证
        String contact = binding.etContact.getText().toString().trim();
        if (TextUtils.isEmpty(contact)) {
            binding.etContact.setError("请输入联系人");
            binding.etContact.requestFocus();
            return false;
        }

        // 联系电话验证
        String phone = binding.etPhone.getText().toString().trim();
        if (TextUtils.isEmpty(phone)) {
            binding.etPhone.setError("请输入联系电话");
            binding.etPhone.requestFocus();
            return false;
        }

        return true;
    }

    private CustomerRequest createCustomerRequest() {
        CustomerRequest request = new CustomerRequest();
        request.setId(customerId);
        request.setName(binding.etCustomerName.getText().toString().trim());
        request.setCode(binding.etCustomerCode.getText().toString().trim());
        request.setCompanyName(binding.etCompanyName.getText().toString().trim());
        request.setAddress(binding.etAddress.getText().toString().trim());
        request.setContact(binding.etContact.getText().toString().trim());
        request.setPhone(binding.etPhone.getText().toString().trim());
        request.setRemark(binding.etRemark.getText().toString().trim());

        // 状态：0-禁用，1-启用
        int statusPosition = binding.spinnerStatus.getSelectedItemPosition();
        request.setStatus(statusPosition == 0 ? "1" : "0");

        // 新增时不设置image字段，编辑时也不在这里设置（单独上传）
        // request.setImage(null);

        return request;
    }

    private void openImagePicker() {
        Log.d(TAG, "openImagePicker: 开始打开图片选择器");

        // 检查权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 使用READ_MEDIA_IMAGES权限
            if (checkSelfPermission(Manifest.permission.READ_MEDIA_IMAGES) != PackageManager.PERMISSION_GRANTED) {
                Log.d(TAG, "请求READ_MEDIA_IMAGES权限");
                permissionLauncher.launch(Manifest.permission.READ_MEDIA_IMAGES);
                return;
            }
        } else {
            // Android 12及以下使用READ_EXTERNAL_STORAGE权限
            if (checkSelfPermission(Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                Log.d(TAG, "请求READ_EXTERNAL_STORAGE权限");
                permissionLauncher.launch(Manifest.permission.READ_EXTERNAL_STORAGE);
                return;
            }
        }

        launchImagePicker();
    }

    private void launchImagePicker() {
        Log.d(TAG, "launchImagePicker: 启动图片选择器");
        try {
            Intent intent = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
            intent.setType("image/*");
            Log.d(TAG, "launchImagePicker: 启动图片选择器Intent");
            imagePickerLauncher.launch(intent);
        } catch (Exception e) {
            Log.e(TAG, "launchImagePicker: 启动图片选择器失败", e);
            showError("无法打开图片选择器: " + e.getMessage());
        }
    }

    private void uploadCustomerImage() {
        if (selectedImageUri == null || customerId == -1) {
            Log.w(TAG, "uploadCustomerImage: 图片URI或客户ID无效");
            return;
        }

        Log.d(TAG, "uploadCustomerImage: 开始上传客户图片，客户ID: " + customerId);

        // 获取当前操作人（这里可以从用户会话中获取）
        String operator = ImageUploadUtils.getCurrentUser(this);

        // 使用通用图片上传方法
        imageUploadRepository.uploadImage(
                this,
                selectedImageUri,
                BusinessImgType.CUSTOMER,
                String.valueOf(customerId),
                operator,
                new ImageUploadUtils.ImageUploadCallback() {
                    @Override
                    public void onUploadStart() {
                        Log.d(TAG, "客户图片上传开始");
                        showLoading(true);
                    }

                    @Override
                    public void onUploadSuccess(String imageUrl) {
                        Log.d(TAG, "客户图片上传成功: " + imageUrl);
                        Snackbar.make(binding.getRoot(), "客户图片上传成功", Snackbar.LENGTH_SHORT).show();
                    }

                    @Override
                    public void onUploadFailure(String errorMessage) {
                        Log.e(TAG, "客户图片上传失败: " + errorMessage);
                        showError(errorMessage);
                    }

                    @Override
                    public void onUploadComplete() {
                        Log.d(TAG, "客户图片上传完成");
                        showLoading(false);
                    }
                }
        );
    }

    private void showLoading(boolean show) {
        binding.progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
        binding.btnSave.setEnabled(!show);
    }

    private void showError(String message) {
        Snackbar.make(binding.getRoot(), message, Snackbar.LENGTH_LONG).show();
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void finish() {
        super.finish();
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right);
    }
}
