package com.opms.ui.system;

import android.os.Bundle;
import android.util.Log;
import android.view.MenuItem;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import com.opms.R;
import com.opms.data.model.request.PostRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.PostResponse;
import com.opms.data.repository.PostRepository;
import com.opms.databinding.ActivityPostEditBinding;

import java.util.List;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class PostEditActivity extends AppCompatActivity {

    private static final String TAG = "PostEdit";

    @Inject
    PostRepository postRepository;

    private ActivityPostEditBinding binding;
    private boolean isEditMode = false;
    private int postId = -1;
    private List<PostResponse> allPosts;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityPostEditBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setupToolbar();
        setupButtons();
        loadExistingData();
        loadAllPosts();
    }

    private void setupToolbar() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
    }

    private void setupButtons() {
        binding.btnSave.setOnClickListener(v -> savePost());
        binding.btnCancel.setOnClickListener(v -> finish());
    }

    private void loadExistingData() {
        postId = getIntent().getIntExtra("post_id", -1);
        if (postId != -1) {
            isEditMode = true;
            if (getSupportActionBar() != null) {
                getSupportActionBar().setTitle("编辑岗位");
            }

            String name = getIntent().getStringExtra("post_name");
            String code = getIntent().getStringExtra("post_code");
            String status = getIntent().getStringExtra("post_status");

            binding.etName.setText(name);
            binding.etCode.setText(code);
            binding.etCode.setEnabled(false);

            binding.switchStatus.setChecked("1".equals(status));
        } else {
            if (getSupportActionBar() != null) {
                getSupportActionBar().setTitle("添加岗位");
            }
        }
    }

    private void loadAllPosts() {
        postRepository.getPosts().enqueue(new Callback<ApiResponse<List<PostResponse>>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<List<PostResponse>>> call,
                                   @NonNull Response<ApiResponse<List<PostResponse>>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<List<PostResponse>> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        allPosts = apiResponse.getData();
                    }
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<List<PostResponse>>> call, @NonNull Throwable t) {
                Log.e(TAG, "获取岗位列表失败: " + t.getMessage(), t);
            }
        });
    }

    private void savePost() {
        String name = binding.etName.getText().toString().trim();
        String code = binding.etCode.getText().toString().trim();
        boolean isActive = binding.switchStatus.isChecked();

        if (validateInput(name, code)) {
            PostRequest request = new PostRequest();
            request.setId(postId);
            request.setName(name);
            request.setCode(code);
            request.setStatus(isActive ? "1" : "0");

            if (isEditMode) {
                updatePost(request);
            } else {
                createPost(request);
            }
        }
    }

    private boolean validateInput(String name, String code) {
        boolean isValid = true;

        if (name.isEmpty()) {
            binding.tilName.setError("岗位名称不能为空");
            isValid = false;
        } else {
            binding.tilName.setError(null);
        }

        if (code.isEmpty()) {
            binding.tilCode.setError("岗位代码不能为空");
            isValid = false;
        } else if (!code.matches("^[A-Z0-9_]+$")) {
            binding.tilCode.setError("岗位代码只能包含大写字母、数字和下划线");
            isValid = false;
        } else {
            binding.tilCode.setError(null);
        }

        if (isValid && allPosts != null) {
            for (PostResponse post : allPosts) {
                if (isEditMode && post.getId() == postId) {
                    continue;
                }

                if (code.equals(post.getCode())) {
                    String status = post.getStatus();
                    String statusText = "1".equals(status) ? "" : "(已禁用)";
                    String msg = "岗位代码[" + code + "]已存在[" + post.getName() + "]" + statusText;
                    binding.tilCode.setError(msg);
                    isValid = false;
                    break;
                }
            }
        }

        return isValid;
    }

    private void createPost(PostRequest request) {
        binding.btnSave.setEnabled(false);

        postRepository.createPost(request).enqueue(new Callback<ApiResponse<PostResponse>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<PostResponse>> call,
                                   @NonNull Response<ApiResponse<PostResponse>> response) {
                binding.btnSave.setEnabled(true);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<PostResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        Toast.makeText(PostEditActivity.this, "岗位创建成功", Toast.LENGTH_SHORT).show();
                        finish();
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "创建失败");
                    }
                } else {
                    showError("创建失败");
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<PostResponse>> call, @NonNull Throwable t) {
                binding.btnSave.setEnabled(true);
                Log.e(TAG, "创建岗位失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    private void updatePost(PostRequest request) {
        binding.btnSave.setEnabled(false);

        postRepository.updatePost((int) postId, request)
                .enqueue(new Callback<ApiResponse<PostResponse>>() {
                    @Override
                    public void onResponse(@NonNull Call<ApiResponse<PostResponse>> call,
                                           @NonNull Response<ApiResponse<PostResponse>> response) {
                        binding.btnSave.setEnabled(true);

                        if (response.isSuccessful() && response.body() != null) {
                            ApiResponse<PostResponse> apiResponse = response.body();
                            if (apiResponse.isSuccess()) {
                                Toast.makeText(PostEditActivity.this, "岗位更新成功", Toast.LENGTH_SHORT).show();
                                finish();
                            } else {
                                showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "更新失败");
                            }
                        } else {
                            showError("更新失败");
                        }
                    }

                    @Override
                    public void onFailure(@NonNull Call<ApiResponse<PostResponse>> call, @NonNull Throwable t) {
                        binding.btnSave.setEnabled(true);
                        Log.e(TAG, "更新岗位失败: " + t.getMessage(), t);
                        showError("网络错误: " + t.getMessage());
                    }
                });
    }

    private void showError(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void finish() {
        super.finish();
        // 添加返回动画
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right);
    }
}
