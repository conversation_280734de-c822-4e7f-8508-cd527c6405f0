package com.opms.ui.profile;

import android.app.DatePickerDialog;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.AutoCompleteTextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import com.opms.R;
import com.opms.common.enums.Gender;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.UserResponse;
import com.opms.data.repository.UserRepository;
import com.opms.databinding.ActivityProfileEditBinding;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Locale;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class ProfileEditActivity extends AppCompatActivity {
    private static final String TAG = "ProfileEditActivity";
    private final Calendar calendar = Calendar.getInstance();
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
    @Inject
    UserRepository userRepository;
    private ActivityProfileEditBinding binding;
    private UserResponse currentUser;

    public static Intent createIntent(Context context) {
        return new Intent(context, ProfileEditActivity.class);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityProfileEditBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // 设置返回按钮
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("编辑个人信息");
        }

        setupGenderDropdown();
        setupDatePicker();
        setupButtons();
        loadUserProfile();
    }

    private void setupGenderDropdown() {
        // 设置性别下拉选项
        String[] genders = {Gender.MALE.getName(), Gender.FEMALE.getName()};
        ArrayAdapter<String> adapter = new ArrayAdapter<>(
                this, android.R.layout.simple_dropdown_item_1line, genders);
        ((AutoCompleteTextView) binding.actGender).setAdapter(adapter);
    }

    private void setupDatePicker() {
        // 设置日期选择器
        binding.etBirthday.setOnClickListener(v -> showDatePicker());
    }

    private void showDatePicker() {
        DatePickerDialog datePickerDialog = new DatePickerDialog(
                this,
                (view, year, month, dayOfMonth) -> {
                    calendar.set(Calendar.YEAR, year);
                    calendar.set(Calendar.MONTH, month);
                    calendar.set(Calendar.DAY_OF_MONTH, dayOfMonth);
                    binding.etBirthday.setText(dateFormat.format(calendar.getTime()));
                },
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH));
        datePickerDialog.show();
    }

    private void setupButtons() {
        // 设置保存按钮
        binding.btnSave.setOnClickListener(v -> saveUserProfile());

        // 设置取消按钮
        binding.btnCancel.setOnClickListener(v -> finish());
    }

    private void loadUserProfile() {
        binding.progressBar.setVisibility(View.VISIBLE);

        userRepository.getUserProfile().enqueue(new Callback<ApiResponse<UserResponse>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<UserResponse>> call, @NonNull Response<ApiResponse<UserResponse>> response) {
                binding.progressBar.setVisibility(View.GONE);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<UserResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        currentUser = apiResponse.getData();
                        updateUI(currentUser);
                    } else {
                        Toast.makeText(ProfileEditActivity.this,
                                apiResponse.getMessage() != null ? apiResponse.getMessage() : "获取用户信息失败",
                                Toast.LENGTH_SHORT).show();
                    }
                } else {
                    Toast.makeText(ProfileEditActivity.this, "获取用户信息失败", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<UserResponse>> call, @NonNull Throwable t) {
                binding.progressBar.setVisibility(View.GONE);
                Log.e(TAG, "获取用户信息失败: " + t.getMessage(), t);
                Toast.makeText(ProfileEditActivity.this, "网络错误: " + t.getMessage(), Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void updateUI(UserResponse user) {
        if (user == null) return;

        // 设置基本信息
        binding.etName.setText(user.getName());
        binding.actGender.setText(user.getGender(), false);
        binding.etBirthday.setText(user.getBirthday());
        binding.etIdCard.setText(user.getIdCard());
        binding.etPhone.setText(user.getPhone());

        // 设置日历初始日期
        try {
            if (!TextUtils.isEmpty(user.getBirthday())) {
                calendar.setTime(dateFormat.parse(user.getBirthday()));
            }
        } catch (Exception e) {
            Log.e(TAG, "解析日期失败: " + e.getMessage(), e);
        }
    }

    private void saveUserProfile() {
        // 验证输入
        if (!validateInput()) {
            return;
        }

        // 显示进度条
        binding.progressBar.setVisibility(View.VISIBLE);

        // 准备更新的用户数据
        UserResponse updatedUser = prepareUpdatedUser();

        // 发送更新请求
        userRepository.updateUserProfile(updatedUser).enqueue(new Callback<ApiResponse<UserResponse>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<UserResponse>> call, @NonNull Response<ApiResponse<UserResponse>> response) {
                binding.progressBar.setVisibility(View.GONE);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<UserResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        Toast.makeText(ProfileEditActivity.this, "个人信息更新成功", Toast.LENGTH_SHORT).show();
                        setResult(RESULT_OK);
                        finish();
                    } else {
                        Toast.makeText(ProfileEditActivity.this,
                                apiResponse.getMessage() != null ? apiResponse.getMessage() : "更新失败",
                                Toast.LENGTH_SHORT).show();
                    }
                } else {
                    Toast.makeText(ProfileEditActivity.this, "更新失败", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<UserResponse>> call, @NonNull Throwable t) {
                binding.progressBar.setVisibility(View.GONE);
                Log.e(TAG, "更新用户信息失败: " + t.getMessage(), t);
                Toast.makeText(ProfileEditActivity.this, "网络错误: " + t.getMessage(), Toast.LENGTH_SHORT).show();
            }
        });
    }

    private boolean validateInput() {
        boolean isValid = true;

        // 验证姓名
        String name = binding.etName.getText().toString().trim();
        if (TextUtils.isEmpty(name)) {
            binding.tilName.setError("姓名不能为空");
            isValid = false;
        } else {
            binding.tilName.setError(null);
        }

        // 验证性别
        String gender = binding.actGender.getText().toString().trim();
        if (TextUtils.isEmpty(gender)) {
            binding.tilGender.setError("性别不能为空");
            isValid = false;
        } else {
            binding.tilGender.setError(null);
        }

        // 验证出生年月日
        String birthday = binding.etBirthday.getText().toString().trim();
        if (TextUtils.isEmpty(birthday)) {
            binding.tilBirthday.setError("出生年月日不能为空");
            isValid = false;
        } else {
            binding.tilBirthday.setError(null);
        }

        // 验证身份证号码
        String idCard = binding.etIdCard.getText().toString().trim();
        if (TextUtils.isEmpty(idCard)) {
            binding.tilIdCard.setError("身份证号码不能为空");
            isValid = false;
        } else {
            binding.tilIdCard.setError(null);
        }

        // 验证电话
        String phone = binding.etPhone.getText().toString().trim();
        if (TextUtils.isEmpty(phone)) {
            binding.tilPhone.setError("电话不能为空");
            isValid = false;
        } else {
            binding.tilPhone.setError(null);
        }

        return isValid;
    }

    private UserResponse prepareUpdatedUser() {
        // 创建一个新的用户对象，只包含需要更新的字段
        UserResponse updatedUser = new UserResponse();

        // 设置不可修改的字段（保持原值）
        updatedUser.setUsername(currentUser.getUsername());
        updatedUser.setEmployeeId(currentUser.getEmployeeId());
        updatedUser.setRole(currentUser.getRole());
        updatedUser.setDepartment(currentUser.getDepartment());
        updatedUser.setPosition(currentUser.getPosition());
        updatedUser.setJob(currentUser.getJob());
        updatedUser.setPermissionTemplate(currentUser.getPermissionTemplate());
        updatedUser.setRemark(currentUser.getRemark());
        updatedUser.setRegisterTime(currentUser.getRegisterTime());
        updatedUser.setAvatarUrl(currentUser.getAvatarUrl());

        // 设置可修改的字段（从表单获取）
        updatedUser.setName(binding.etName.getText().toString().trim());

        // 将显示的性别名称转换为代码
        String genderName = binding.actGender.getText().toString().trim();
        if (Gender.MALE.getName().equals(genderName)) {
            updatedUser.setGender(Gender.MALE.getCode());
        } else if (Gender.FEMALE.getName().equals(genderName)) {
            updatedUser.setGender(Gender.FEMALE.getCode());
        }

        updatedUser.setBirthday(binding.etBirthday.getText().toString().trim());
        updatedUser.setIdCard(binding.etIdCard.getText().toString().trim());
        updatedUser.setPhone(binding.etPhone.getText().toString().trim());

        return updatedUser;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void finish() {
        super.finish();
        // 添加返回动画
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right);
    }
}
