package com.opms.common.enums;

/**
 * 权限管理》业务模块
 */
public enum BusinessModule {
    CUSTOMER("customer", "客户管理"),
    PRODUCT("product", "商品管理"),
    ORDER("order", "订单管理"),
    ORDER_TRACKING("order_tracking", "订单跟进"),
    PROCESS("process", "流程管理"),
    USER("user", "用户管理"),
    DEPARTMENT("department", "部门管理"),
    POSITION("position", "职位管理"),
    POST("post", "岗位管理"),
    PERMISSION("permission", "权限管理"),
    SYSTEM("system", "系统管理");

    private final String code;
    private final String name;

    BusinessModule(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        for (BusinessModule sde : BusinessModule.values()) {
            if (code.equalsIgnoreCase(sde.getCode())) {
                return sde.getName();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
