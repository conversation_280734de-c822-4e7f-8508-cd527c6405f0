package com.opms.data.repository;

import com.opms.data.local.entity.Customer;
import com.opms.data.model.request.CustomerRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.CustomerListResponse;
import com.opms.data.model.response.CustomerResponse;

import java.util.List;

import retrofit2.Call;

public interface CustomerRepository {
    Call<ApiResponse<CustomerListResponse>> getCustomerList(int page, int size, String keyword);

    Call<ApiResponse<CustomerResponse>> getCustomerDetail(int id);
    Call<ApiResponse<CustomerResponse>> createCustomer(CustomerRequest request);
    Call<ApiResponse<CustomerResponse>> updateCustomer(int id, CustomerRequest request);
    Call<ApiResponse<Void>> deleteCustomer(CustomerRequest request);

    // Local database operations
    void insert(Customer customer);
    void update(Customer customer);
    void delete(Customer customer);
    Customer findByCode(String code);
    Customer findById(int id);
    List<Customer> getAllCustomers();
    List<Customer> searchCustomers(String keyword);
}