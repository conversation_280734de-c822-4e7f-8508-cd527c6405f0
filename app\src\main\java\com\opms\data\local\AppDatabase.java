package com.opms.data.local;

import android.content.Context;

import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;

import com.opms.data.local.dao.UserDao;
import com.opms.data.local.dao.DepartmentDao;
import com.opms.data.local.dao.PositionDao;
import com.opms.data.local.dao.PostDao;
import com.opms.data.local.dao.ProductDao;
import com.opms.data.local.dao.CustomerDao;
import com.opms.data.local.dao.OrderDao;
import com.opms.data.local.dao.OrderItemDao;
import com.opms.data.local.dao.OrderProcessDao;
import com.opms.data.local.entity.User;
import com.opms.data.local.entity.Department;
import com.opms.data.local.entity.Position;
import com.opms.data.local.entity.Post;
import com.opms.data.local.entity.Product;
import com.opms.data.local.entity.Customer;
import com.opms.data.local.entity.Order;
import com.opms.data.local.entity.OrderItem;
import com.opms.data.local.entity.OrderProcess;

@Database(
    entities = {
        User.class,
        Department.class,
        Position.class,
        Post.class,
        Product.class,
        Customer.class,
        Order.class,
        OrderItem.class,
        OrderProcess.class
    },
    version = 1,
    exportSchema = false
)
public abstract class AppDatabase extends RoomDatabase {
    private static final String DATABASE_NAME = "opms.db";
    private static volatile AppDatabase instance;

    public abstract UserDao userDao();
    public abstract DepartmentDao departmentDao();
    public abstract PositionDao positionDao();
    public abstract PostDao postDao();
    public abstract ProductDao productDao();
    public abstract CustomerDao customerDao();
    public abstract OrderDao orderDao();
    public abstract OrderItemDao orderItemDao();
    public abstract OrderProcessDao orderProcessDao();

    public static synchronized AppDatabase getInstance(Context context) {
        if (instance == null) {
            instance = Room.databaseBuilder(
                context.getApplicationContext(),
                AppDatabase.class,
                DATABASE_NAME
            ).build();
        }
        return instance;
    }
} 