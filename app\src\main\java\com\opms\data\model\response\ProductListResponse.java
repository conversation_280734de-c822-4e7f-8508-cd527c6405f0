package com.opms.data.model.response;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * 产品列表响应模型
 */
public class ProductListResponse {
    @SerializedName("list")
    private List<ProductResponse> list;

    @SerializedName("total")
    private int total;

    @SerializedName("page")
    private int page;

    @SerializedName("size")
    private int size;

    @SerializedName("pages")
    private int pages;

    public List<ProductResponse> getList() {
        return list;
    }

    public void setList(List<ProductResponse> list) {
        this.list = list;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public int getPages() {
        return pages;
    }

    public void setPages(int pages) {
        this.pages = pages;
    }
}
