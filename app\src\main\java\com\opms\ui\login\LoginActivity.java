package com.opms.ui.login;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.appcompat.app.AppCompatActivity;

import com.google.android.material.snackbar.Snackbar;
import com.opms.MainActivity;
import com.opms.R;
import com.opms.data.local.PreferencesManager;
import com.opms.data.model.request.LoginRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.LoginResponse;
import com.opms.data.repository.UserRepository;
import com.opms.databinding.ActivityLoginBinding;
import com.opms.ui.register.RegisterActivity;
import com.opms.utils.MD5Utils;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class LoginActivity extends AppCompatActivity {
    private ActivityLoginBinding binding;

    @Inject
    UserRepository userRepository;

    @Inject
    PreferencesManager preferencesManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityLoginBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setupViews();
        checkRegistrationSuccess();
    }

    private void setupViews() {
        binding.btnLogin.setOnClickListener(v -> attemptLogin());
        binding.btnRegister.setOnClickListener(v -> {
            Intent intent = new Intent(this, RegisterActivity.class);
            startActivity(intent);
            // 添加滑动动画效果
            overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
        });
    }

    /**
     * 检查是否从注册页面跳转过来，如果是则显示注册成功消息
     */
    private void checkRegistrationSuccess() {
        Intent intent = getIntent();
        if (intent != null && intent.getBooleanExtra("registration_success", false)) {
            String message = intent.getStringExtra("message");
            if (message != null) {
                // 显示注册成功的提示
                Snackbar successSnackbar = Snackbar.make(binding.getRoot(), message, Snackbar.LENGTH_LONG);
                successSnackbar.setBackgroundTint(getResources().getColor(android.R.color.holo_green_dark, null));
                successSnackbar.setTextColor(getResources().getColor(android.R.color.white, null));
                successSnackbar.show();
            }
        }
    }

    private void attemptLogin() {
        String username = binding.etUsername.getText().toString().trim();
        String password = binding.etPassword.getText().toString().trim();

        if (TextUtils.isEmpty(username)) {
            binding.etUsername.setError(getString(R.string.error_username_empty));
            return;
        }

        if (TextUtils.isEmpty(password)) {
            binding.etPassword.setError(getString(R.string.error_password_empty));
            return;
        }

        binding.btnLogin.setEnabled(false);
        binding.progressBar.setVisibility(View.VISIBLE);

        LoginRequest request = new LoginRequest(username, MD5Utils.md5(password));
        userRepository.login(request).enqueue(new Callback<ApiResponse<LoginResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<LoginResponse>> call, Response<ApiResponse<LoginResponse>> response) {
                binding.btnLogin.setEnabled(true);
                binding.progressBar.setVisibility(View.GONE);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<LoginResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        LoginResponse loginResponse = apiResponse.getData();
                        if (loginResponse.getToken() != null && loginResponse.getUsername() != null && loginResponse.getRole() != null) {
                            preferencesManager.saveToken(loginResponse.getToken());
                            preferencesManager.saveUsername(loginResponse.getUsername());
                            preferencesManager.saveUserRole(loginResponse.getRole());
                            Log.d("LoginActivity", "保存的token: " + loginResponse.getToken());
                            Intent intent = new Intent(LoginActivity.this, MainActivity.class);
                            startActivity(intent);
                            // 添加滑动动画效果
                            overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
                            finish();
                        } else {
                            Snackbar.make(binding.getRoot(), R.string.error_login_failed, Snackbar.LENGTH_SHORT).show();
                        }
                    } else {
                        Snackbar.make(binding.getRoot(), apiResponse.getMessage(), Snackbar.LENGTH_SHORT).show();
                    }
                } else {
                    Snackbar.make(binding.getRoot(), R.string.error_login_failed, Snackbar.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<LoginResponse>> call, Throwable t) {
                binding.btnLogin.setEnabled(true);
                binding.progressBar.setVisibility(View.GONE);
                Snackbar.make(binding.getRoot(), R.string.error_network, Snackbar.LENGTH_SHORT).show();
            }
        });
    }
}