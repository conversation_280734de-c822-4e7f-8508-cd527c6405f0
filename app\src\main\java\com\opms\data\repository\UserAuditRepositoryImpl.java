package com.opms.data.repository;

import com.opms.data.model.request.UserAuditRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.UserResponse;
import com.opms.data.remote.ApiService;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Singleton;

import retrofit2.Call;

/**
 * 用户审核Repository实现
 */
@Singleton
public class UserAuditRepositoryImpl implements UserAuditRepository {

    private final ApiService apiService;

    @Inject
    public UserAuditRepositoryImpl(ApiService apiService) {
        this.apiService = apiService;
    }

    @Override
    public Call<ApiResponse<List<UserResponse>>> getAllAuditUsers() {
        return apiService.getAllAuditUsers();
    }

    @Override
    public Call<ApiResponse<List<UserResponse>>> getPendingUsers() {
        return apiService.getPendingUsers();
    }

    @Override
    public Call<ApiResponse<UserResponse>> getUserById(int id) {
        return apiService.getUserById(id);
    }

    @Override
    public Call<ApiResponse<UserResponse>> getPendingUserById(int id) {
        return apiService.getPendingUserById(id);
    }

    @Override
    public Call<ApiResponse<UserResponse>> auditUser(UserAuditRequest request) {
        return apiService.auditUser(request);
    }
}
