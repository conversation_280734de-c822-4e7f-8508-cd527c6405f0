// Generated by view binder compiler. Do not edit!
package com.opms.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.ProgressBar;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.opms.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemMultiImageBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final FloatingActionButton btnDelete;

  @NonNull
  public final MaterialCardView cardImage;

  @NonNull
  public final ImageView ivImage;

  @NonNull
  public final ProgressBar progressUploading;

  @NonNull
  public final View viewUploadingOverlay;

  private ItemMultiImageBinding(@NonNull FrameLayout rootView,
      @NonNull FloatingActionButton btnDelete, @NonNull MaterialCardView cardImage,
      @NonNull ImageView ivImage, @NonNull ProgressBar progressUploading,
      @NonNull View viewUploadingOverlay) {
    this.rootView = rootView;
    this.btnDelete = btnDelete;
    this.cardImage = cardImage;
    this.ivImage = ivImage;
    this.progressUploading = progressUploading;
    this.viewUploadingOverlay = viewUploadingOverlay;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemMultiImageBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemMultiImageBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_multi_image, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemMultiImageBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_delete;
      FloatingActionButton btnDelete = ViewBindings.findChildViewById(rootView, id);
      if (btnDelete == null) {
        break missingId;
      }

      id = R.id.card_image;
      MaterialCardView cardImage = ViewBindings.findChildViewById(rootView, id);
      if (cardImage == null) {
        break missingId;
      }

      id = R.id.iv_image;
      ImageView ivImage = ViewBindings.findChildViewById(rootView, id);
      if (ivImage == null) {
        break missingId;
      }

      id = R.id.progress_uploading;
      ProgressBar progressUploading = ViewBindings.findChildViewById(rootView, id);
      if (progressUploading == null) {
        break missingId;
      }

      id = R.id.view_uploading_overlay;
      View viewUploadingOverlay = ViewBindings.findChildViewById(rootView, id);
      if (viewUploadingOverlay == null) {
        break missingId;
      }

      return new ItemMultiImageBinding((FrameLayout) rootView, btnDelete, cardImage, ivImage,
          progressUploading, viewUploadingOverlay);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
