package com.opms.data.model.request;

import com.google.gson.annotations.SerializedName;

/**
 * 头像更新请求
 */
public class AvatarUpdateRequest {

    @SerializedName("base64Image")
    private String base64Image;

    @SerializedName("userId")
    private Integer userId;

    @SerializedName("filename")
    private String filename;

    public AvatarUpdateRequest() {
    }

    public AvatarUpdateRequest(String base64Image) {
        this.base64Image = base64Image;
    }

    public AvatarUpdateRequest(String base64Image, Integer userId, String filename) {
        this.base64Image = base64Image;
        this.userId = userId;
        this.filename = filename;
    }

    public String getBase64Image() {
        return base64Image;
    }

    public void setBase64Image(String base64Image) {
        this.base64Image = base64Image;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }
}
