package com.opms.data.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.opms.R;
import com.opms.data.local.entity.Registration;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Locale;

public class RegistrationAdapter extends RecyclerView.Adapter<RegistrationAdapter.ViewHolder> {
    private final List<Registration> registrations;
    private final OnRegistrationClickListener listener;
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());

    public interface OnRegistrationClickListener {
        void onRegistrationClick(Registration registration);
    }

    public RegistrationAdapter(List<Registration> registrations, OnRegistrationClickListener listener) {
        this.registrations = registrations;
        this.listener = listener;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_registration, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        Registration registration = registrations.get(position);
        holder.bind(registration);
    }

    @Override
    public int getItemCount() {
        return registrations.size();
    }

    class ViewHolder extends RecyclerView.ViewHolder {
        private final TextView tvUsername;
        private final TextView tvName;
        private final TextView tvIdCard;
        private final TextView tvPhone;
        private final TextView tvRegisterTime;

        ViewHolder(View itemView) {
            super(itemView);
            tvUsername = itemView.findViewById(R.id.tv_username);
            tvName = itemView.findViewById(R.id.tv_name);
            tvIdCard = itemView.findViewById(R.id.tv_id_card);
            tvPhone = itemView.findViewById(R.id.tv_phone);
            tvRegisterTime = itemView.findViewById(R.id.tv_register_time);

            itemView.setOnClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION) {
                    listener.onRegistrationClick(registrations.get(position));
                }
            });
        }

        void bind(Registration registration) {
            tvUsername.setText(registration.getUsername());
            tvName.setText(registration.getName());
            tvIdCard.setText(registration.getIdCard());
            tvPhone.setText(registration.getPhone());
            tvRegisterTime.setText(dateFormat.format(registration.getRegisterTime()));
        }
    }
} 