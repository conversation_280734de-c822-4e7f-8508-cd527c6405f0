package com.opms.data.model.request;

import com.google.gson.annotations.SerializedName;

public class PermissionItemRequest {
    @SerializedName("id")
    private int id;

    @SerializedName("code")
    private String code;

    @SerializedName("parentCode")
    private String parentCode;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    @Override
    public String toString() {
        return "PermissionItemRequest{" +
                "id=" + id +
                ", code='" + code + '\'' +
                ", parentCode='" + parentCode + '\'' +
                '}';
    }
}
