package com.opms.data.remote.exception;

public class NetworkException extends Exception {
    private int code;
    private String message;

    public NetworkException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
} 