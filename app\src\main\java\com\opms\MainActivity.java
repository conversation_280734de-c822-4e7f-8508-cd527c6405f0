package com.opms;

import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.opms.common.enums.RoleType;
import com.opms.data.local.PreferencesManager;
import com.opms.ui.business.BusinessFragment;
import com.opms.ui.login.LoginActivity;
import com.opms.ui.profile.ProfileFragment;
import com.opms.ui.system.SystemManagementFragment;
import com.opms.ui.todo.TodoFragment;
import com.opms.ui.user.UserAuditFragment;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;

@AndroidEntryPoint
public class MainActivity extends AppCompatActivity implements BottomNavigationView.OnNavigationItemSelectedListener {
    private BottomNavigationView bottomNavigationView;
    private String userRole;

    @Inject
    PreferencesManager preferencesManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // 获取用户角色
        userRole = preferencesManager.getUserRole();
        if (userRole == null || userRole.isEmpty()) {
            // 如果没有角色信息，跳转到登录页面
            startActivity(new Intent(this, LoginActivity.class));
            finish();
            return;
        }

        // 初始化底部导航
        bottomNavigationView = findViewById(R.id.bottom_navigation);

        // 使用新的监听器接口
        bottomNavigationView.setOnNavigationItemSelectedListener(this);

        // 添加动画效果
        bottomNavigationView.setOnNavigationItemReselectedListener(item -> {
            // 当重复点击同一个菜单项时，不做任何操作
        });

        // 根据用户角色设置可见的菜单项
        setupNavigationMenu();

        // 默认显示第一个菜单项对应的Fragment
        if (RoleType.ADMIN.getCode().equals(userRole)) {
            // 管理员默认显示用户审核页面
            loadFragment(new UserAuditFragment());
            // 设置用户审核为选中状态
            bottomNavigationView.setSelectedItemId(R.id.navigation_user_audit);
        } else {
            loadFragment(new TodoFragment());
            // 设置待办事项为选中状态
            bottomNavigationView.setSelectedItemId(R.id.navigation_todo);
        }
    }

    private void setupNavigationMenu() {
        // 设置所有菜单项不可见
        bottomNavigationView.getMenu().findItem(R.id.navigation_user_audit).setVisible(false);
        bottomNavigationView.getMenu().findItem(R.id.navigation_system_management).setVisible(false);
        bottomNavigationView.getMenu().findItem(R.id.navigation_todo).setVisible(false);
        bottomNavigationView.getMenu().findItem(R.id.navigation_business).setVisible(false);
        bottomNavigationView.getMenu().findItem(R.id.navigation_profile).setVisible(true);

        // 根据用户角色显示对应的菜单项
        if (RoleType.ADMIN.getCode().equals(userRole)) {
            bottomNavigationView.getMenu().findItem(R.id.navigation_user_audit).setVisible(true);
            bottomNavigationView.getMenu().findItem(R.id.navigation_system_management).setVisible(true);
        } else {
            bottomNavigationView.getMenu().findItem(R.id.navigation_todo).setVisible(true);
            bottomNavigationView.getMenu().findItem(R.id.navigation_business).setVisible(true);
        }
    }

    @Override
    public boolean onNavigationItemSelected(@NonNull MenuItem item) {
        Fragment fragment = null;
        int itemId = item.getItemId();

        if (itemId == R.id.navigation_user_audit) {
            fragment = new UserAuditFragment();
        } else if (itemId == R.id.navigation_system_management) {
            fragment = new SystemManagementFragment();
        } else if (itemId == R.id.navigation_todo) {
            fragment = new TodoFragment();
        } else if (itemId == R.id.navigation_business) {
            fragment = new BusinessFragment();
        } else if (itemId == R.id.navigation_profile) {
            fragment = new ProfileFragment();
        }

        return loadFragment(fragment);
    }

    private boolean loadFragment(Fragment fragment) {
        if (fragment != null) {
            // 获取当前显示的Fragment
            Fragment currentFragment = getSupportFragmentManager().findFragmentById(R.id.fragment_container);

            // 创建事务
            FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();

            // 如果当前没有Fragment或者是第一次加载，使用淡入动画
            if (currentFragment == null) {
                transaction.setCustomAnimations(R.anim.fade_in, R.anim.fade_out);
            } else {
                // 否则使用滑动动画，确保动画流畅
                transaction.setCustomAnimations(
                        R.anim.slide_in_right,  // 新Fragment进入动画
                        R.anim.slide_out_left,  // 当前Fragment退出动画
                        R.anim.slide_in_left,   // 返回时新Fragment进入动画
                        R.anim.slide_out_right  // 返回时当前Fragment退出动画
                );
            }

            // 替换Fragment并提交事务
            transaction.replace(R.id.fragment_container, fragment)
                    .setTransition(FragmentTransaction.TRANSIT_FRAGMENT_FADE)
                    .commit();

            return true;
        }
        return false;
    }
}