package com.opms.ui.system;

import android.graphics.Bitmap;
import android.os.Bundle;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.AutoCompleteTextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.bumptech.glide.Glide;
import com.google.android.material.snackbar.Snackbar;
import com.opms.R;
import com.opms.common.enums.RoleType;
import com.opms.common.utils.AvatarCacheUtils;
import com.opms.common.utils.AvatarDebugUtils;
import com.opms.data.model.request.UserAuditRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.DepartmentResponse;
import com.opms.data.model.response.PermissionResponse;
import com.opms.data.model.response.PositionResponse;
import com.opms.data.model.response.PostResponse;
import com.opms.data.model.response.UserResponse;
import com.opms.data.remote.ApiService;
import com.opms.data.repository.UserAuditRepository;
import com.opms.databinding.ActivityUserAuditBinding;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class UserAuditActivity extends AppCompatActivity {

    private static final String TAG = "UserAudit";

    @Inject
    UserAuditRepository userAuditRepository;

    @Inject
    ApiService apiService;

    private ActivityUserAuditBinding binding;
    private int userId;
    private UserResponse currentUser;
    private boolean isApproved = false;

    // 下拉选项数据
    private List<DepartmentResponse> departments;
    private List<PositionResponse> positions;
    private List<PostResponse> posts;
    private List<PermissionResponse> permissions;

    // 数据加载状态管理
    private boolean isDepartmentsLoaded = false;
    private boolean isPositionsLoaded = false;
    private boolean isPostsLoaded = false;
    private boolean isPermissionsLoaded = false;
    private boolean isUserDataLoaded = false;
    private UserResponse pendingUserData = null;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityUserAuditBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        getUserIdFromIntent();
        setupToolbar();
        setupAuditResultSelection();
        setupSubmitButton();
        loadUserDetails();
        loadDropdownData();
    }

    private void getUserIdFromIntent() {
        userId = getIntent().getIntExtra("user_id", -1);
        if (userId == -1) {
            showError("用户ID无效");
            finish();
        }
    }

    private void setupToolbar() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
    }

    private void setupAuditResultSelection() {
        binding.chipGroupAuditResult.setOnCheckedStateChangeListener((group, checkedIds) -> {
            if (!checkedIds.isEmpty()) {
                int checkedId = checkedIds.get(0);
                isApproved = checkedId == R.id.chip_approve;
                updateAssignmentCardVisibility();
                updateRemarkRequirement();
                Log.d(TAG, "Audit result selection changed: " + (isApproved ? "APPROVED" : "REJECTED"));
            }
        });
    }

    private void updateAssignmentCardVisibility() {
        View assignmentCard = binding.includeUserAssignment.getRoot();
        if (isApproved) {
            assignmentCard.setVisibility(View.VISIBLE);
        } else {
            assignmentCard.setVisibility(View.GONE);
        }
    }

    private void updateRemarkRequirement() {
        if (isApproved) {
            binding.tilRemark.setHint("备注信息（可选）");
        } else {
            binding.tilRemark.setHint("拒绝原因（必填）");
        }
    }

    private void setupSubmitButton() {
        binding.btnSubmit.setOnClickListener(v -> submitAudit());
    }

    private void loadUserDetails() {
        showLoading(true);

        // 使用新的API获取用户详情（包括已审核的用户）
        userAuditRepository.getUserById(userId).enqueue(new Callback<ApiResponse<UserResponse>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<UserResponse>> call,
                                   @NonNull Response<ApiResponse<UserResponse>> response) {
                showLoading(false);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<UserResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        currentUser = apiResponse.getData();
                        fillUserData(currentUser);
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "获取用户详情失败");
                    }
                } else {
                    showError("获取用户详情失败");
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<UserResponse>> call, @NonNull Throwable t) {
                showLoading(false);
                Log.e(TAG, "获取用户详情失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    private void fillUserData(UserResponse user) {
        Log.d(TAG, "Filling user data for user: " + user.getUsername() + ", status: " + user.getStatus());

        // 设置用户头像
        loadUserAvatar(user.getAvatarUrl());

        // 设置基本信息
        binding.tvUsername.setText(user.getUsername());
        binding.tvName.setText(user.getName());
        binding.tvGender.setText(formatGender(user.getGender()));
        binding.tvBirthDate.setText(user.getBirthday());
        binding.tvIdCard.setText(user.getIdCard());
        binding.tvPhone.setText(user.getPhone());
        binding.tvRegisterTime.setText(user.getRegisterTime());

        // 记录用户的分配信息
        Log.d(TAG, "User assignment info - EmployeeId: " + user.getEmployeeId() +
                ", Role: " + user.getRole() +
                ", Department: " + user.getDepartment() +
                ", DepartmentName: " + user.getDepartmentName() +
                ", Position: " + user.getPosition() +
                ", PositionName: " + user.getPositionName() +
                ", Job: " + user.getJob() +
                ", JobName: " + user.getJobName() +
                ", PermissionTemplate: " + user.getPermissionTemplate());

        // 记录权限列表加载状态
        Log.d(TAG, "Permissions list loaded: " + (permissions != null ? permissions.size() + " items" : "null"));

        // 标记用户数据已加载
        isUserDataLoaded = true;
        currentUser = user;

        // 如果用户已经审核过，需要等待所有下拉框数据加载完成后再显示
        if (!"0".equals(user.getStatus())) {
            Log.d(TAG, "User has been processed, checking if all dropdown data is loaded");
            pendingUserData = user;
            checkAndShowProcessedUserInfo();
        } else {
            Log.d(TAG, "User is pending, showing normal audit form");
        }
    }

    /**
     * 检查所有必要的数据是否已加载，如果是则显示已审核用户信息
     */
    private void checkAndShowProcessedUserInfo() {
        Log.d(TAG, "Checking data loading status - Departments: " + isDepartmentsLoaded +
                   ", Positions: " + isPositionsLoaded +
                   ", Posts: " + isPostsLoaded +
                   ", Permissions: " + isPermissionsLoaded +
                   ", UserData: " + isUserDataLoaded);

        // 检查是否所有必要的数据都已加载
        if (isDepartmentsLoaded && isPositionsLoaded && isPostsLoaded &&
            isPermissionsLoaded && isUserDataLoaded && pendingUserData != null) {

            Log.d(TAG, "All data loaded, showing processed user info");
            updateUIForProcessedUser(pendingUserData);
            pendingUserData = null; // 清除待处理的用户数据
        } else {
            Log.d(TAG, "Still waiting for data to load");
        }
    }

    private void loadUserAvatar(String avatarData) {
        if (avatarData == null || avatarData.isEmpty()) {
            Log.d(TAG, "Avatar data is null or empty, using default avatar");
            binding.ivUserAvatar.setImageResource(R.drawable.ic_person);
            return;
        }

        // 添加详细的调试信息
        AvatarDebugUtils.analyzeAvatarData(avatarData, "UserAuditActivity");
        AvatarDebugUtils.testBase64Decode(avatarData, "UserAuditActivity");

        Log.d(TAG, "Loading user avatar, data length: " + avatarData.length());
        Log.d(TAG, "Avatar data preview: " + (avatarData.length() > 50 ? avatarData.substring(0, 50) + "..." : avatarData));

        // 使用改进的Base64检测逻辑
        if (AvatarCacheUtils.isBase64Image(avatarData)) {
            Log.d(TAG, "Detected Base64 avatar, attempting to decode");
            // 直接解码并显示Base64图像
            Bitmap bitmap = AvatarCacheUtils.decodeBase64ToBitmap(avatarData);
            if (bitmap != null) {
                Log.d(TAG, "Successfully decoded Base64 avatar, applying to ImageView");
                try {
                    // 确保在主线程中设置ImageView
                    final Bitmap finalBitmap = bitmap;
                    if (android.os.Looper.myLooper() == android.os.Looper.getMainLooper()) {
                        // 已经在主线程中
                        setBitmapToImageView(finalBitmap);
                    } else {
                        // 切换到主线程
                        binding.ivUserAvatar.post(new Runnable() {
                            @Override
                            public void run() {
                                setBitmapToImageView(finalBitmap);
                            }
                        });
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error setting bitmap to ImageView: " + e.getMessage(), e);
                    // 如果直接设置失败，尝试使用Glide
                    try {
                        Glide.with(this)
                                .load(bitmap)
                                .placeholder(R.drawable.ic_person)
                                .error(R.drawable.ic_person)
                                .circleCrop()
                                .into(binding.ivUserAvatar);
                        Log.d(TAG, "Successfully loaded bitmap with Glide");
                    } catch (Exception e2) {
                        Log.e(TAG, "Error loading bitmap with Glide: " + e2.getMessage(), e2);
                        binding.ivUserAvatar.setImageResource(R.drawable.ic_person);
                    }
                }
            } else {
                Log.e(TAG, "Failed to decode Base64 avatar, using default");
                binding.ivUserAvatar.setImageResource(R.drawable.ic_person);
            }
        } else {
            Log.d(TAG, "Not Base64 format, treating as URL: " + avatarData);

            // 检查是否是服务器文件路径
            if (avatarData.contains("\\") || avatarData.startsWith("image/") || avatarData.startsWith("image\\")) {
                Log.d(TAG, "Detected server file path, using default avatar");
                binding.ivUserAvatar.setImageResource(R.drawable.ic_person);
            } else {
                Log.d(TAG, "Attempting to load as URL");
                // 使用Glide加载URL图像
                Glide.with(this)
                        .load(avatarData)
                        .placeholder(R.drawable.ic_person)
                        .error(R.drawable.ic_person)
                        .circleCrop()
                        .listener(new com.bumptech.glide.request.RequestListener<android.graphics.drawable.Drawable>() {
                            @Override
                            public boolean onLoadFailed(@Nullable com.bumptech.glide.load.engine.GlideException e, Object model, com.bumptech.glide.request.target.Target<android.graphics.drawable.Drawable> target, boolean isFirstResource) {
                                Log.e(TAG, "Failed to load avatar from URL: " + model, e);
                                return false;
                            }

                            @Override
                            public boolean onResourceReady(android.graphics.drawable.Drawable resource, Object model, com.bumptech.glide.request.target.Target<android.graphics.drawable.Drawable> target, com.bumptech.glide.load.DataSource dataSource, boolean isFirstResource) {
                                Log.d(TAG, "Successfully loaded avatar from URL: " + model);
                                return false;
                            }
                        })
                        .into(binding.ivUserAvatar);
            }
        }
    }

    private void setBitmapToImageView(Bitmap bitmap) {
        try {
            // 检查ImageView是否已经完成布局
            if (binding.ivUserAvatar.getWidth() == 0 || binding.ivUserAvatar.getHeight() == 0) {
                Log.d(TAG, "ImageView not yet laid out, waiting for layout...");
                // 等待ImageView完成布局后再设置图像
                binding.ivUserAvatar.getViewTreeObserver().addOnGlobalLayoutListener(new android.view.ViewTreeObserver.OnGlobalLayoutListener() {
                    @Override
                    public void onGlobalLayout() {
                        binding.ivUserAvatar.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                        Log.d(TAG, "ImageView layout completed, setting bitmap");
                        setBitmapToImageViewInternal(bitmap);
                    }
                });
            } else {
                // ImageView已经完成布局，直接设置
                setBitmapToImageViewInternal(bitmap);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in setBitmapToImageView: " + e.getMessage(), e);
            binding.ivUserAvatar.setImageResource(R.drawable.ic_person);
        }
    }

    private void setBitmapToImageViewInternal(Bitmap bitmap) {
        try {
            // 获取ImageView的实际尺寸
            int viewWidth = binding.ivUserAvatar.getWidth();
            int viewHeight = binding.ivUserAvatar.getHeight();
            Log.d(TAG, "ImageView size: " + viewWidth + "x" + viewHeight);

            // 根据布局文件，ImageView在80dp的MaterialCardView中
            // 转换dp到px
            float density = getResources().getDisplayMetrics().density;
            int targetSizePx = (int) (80 * density); // 80dp转换为px

            // 如果ImageView尺寸仍然为0，使用布局定义的尺寸
            if (viewWidth == 0 || viewHeight == 0) {
                viewWidth = viewHeight = targetSizePx;
                Log.d(TAG, "Using layout defined size: " + viewWidth + "x" + viewHeight + " (80dp)");
            } else {
                // 使用实际测量的尺寸，但不超过目标尺寸
                targetSizePx = Math.min(Math.min(viewWidth, viewHeight), targetSizePx);
                Log.d(TAG, "Using measured size: " + targetSizePx + "x" + targetSizePx);
            }

            // 缩放Bitmap到合适的尺寸
            Bitmap scaledBitmap = Bitmap.createScaledBitmap(bitmap, targetSizePx, targetSizePx, true);

            // 清除可能的tint滤镜（布局文件中设置的）
            Log.d(TAG, "Clearing tint and color filters");
            binding.ivUserAvatar.setImageTintList(null);
            binding.ivUserAvatar.setColorFilter(null);

            // 设置正确的scaleType - 由于我们已经缩放到正确尺寸，使用FIT_XY确保完全显示
            binding.ivUserAvatar.setScaleType(android.widget.ImageView.ScaleType.FIT_XY);
            Log.d(TAG, "Set scaleType to FIT_XY");

            // 创建圆形Bitmap
            Bitmap circleBitmap = AvatarCacheUtils.createCircleBitmap(scaledBitmap);
            if (circleBitmap != null) {
                binding.ivUserAvatar.setImageBitmap(circleBitmap);
                Log.d(TAG, "Successfully set circle bitmap to ImageView");

                // 强制刷新ImageView
                binding.ivUserAvatar.invalidate();
                binding.ivUserAvatar.requestLayout();

                // 验证ImageView状态
                AvatarDebugUtils.verifyImageViewState(binding.ivUserAvatar, "UserAuditActivity");
            } else {
                // 如果圆形裁剪失败，直接设置缩放后的图
                binding.ivUserAvatar.setImageBitmap(scaledBitmap);
                Log.d(TAG, "Successfully set scaled bitmap to ImageView");

                // 强制刷新ImageView
                binding.ivUserAvatar.invalidate();
                binding.ivUserAvatar.requestLayout();

                // 验证ImageView状态
                AvatarDebugUtils.verifyImageViewState(binding.ivUserAvatar, "UserAuditActivity");
            }

            // 释放临时Bitmap
            if (scaledBitmap != bitmap && scaledBitmap != circleBitmap) {
                scaledBitmap.recycle();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in setBitmapToImageViewInternal: " + e.getMessage(), e);
            binding.ivUserAvatar.setImageResource(R.drawable.ic_person);
        }
    }

    private String formatGender(String gender) {
        if ("MALE".equals(gender) || "男".equals(gender)) {
            return "男";
        } else if ("FEMALE".equals(gender) || "女".equals(gender)) {
            return "女";
        } else {
            return "未知";
        }
    }

    private void updateUIForProcessedUser(UserResponse user) {
        // 根据状态选中对应的chip并设置审核结果
        if ("1".equals(user.getStatus())) { // 已通过
            binding.chipApprove.setChecked(true);
            isApproved = true;
            Log.d(TAG, "User is approved, showing audit result info");
            // 显示审核结果信息
            showAuditResultInfo(user);
        } else if ("-1".equals(user.getStatus())) { // 已拒绝
            binding.chipReject.setChecked(true);
            isApproved = false;
            Log.d(TAG, "User is rejected");
        }

        // 显示审核备注
        if (user.getRemark() != null && !user.getRemark().isEmpty()) {
            binding.etRemark.setText(user.getRemark());
        }

        // 更新分配信息卡片的可见性（必须在设置isApproved之后调用）
        updateAssignmentCardVisibility();

        // 禁用审核选项的交互，但保持选中状态的视觉效果
        disableAuditResultInteraction();

        // 禁用表单
        binding.etRemark.setEnabled(false);
        binding.btnSubmit.setEnabled(false);
        binding.btnSubmit.setText("已审核");

        // 更新标题
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle("查看审核结果");
        }
    }

    /**
     * 禁用审核结果选择的交互，但保持选中状态的视觉效果
     */
    private void disableAuditResultInteraction() {
        // 移除ChipGroup的监听器，防止用户改变选择
        binding.chipGroupAuditResult.setOnCheckedStateChangeListener(null);

        // 设置ChipGroup为不可点击，但不设置为disabled（这样可以保持选中状态的视觉效果）
        binding.chipGroupAuditResult.setClickable(false);

        // 单独禁用每个chip的点击，但不设置为disabled
        binding.chipApprove.setClickable(false);
        binding.chipReject.setClickable(false);

        // 设置chip为不可聚焦，防止通过键盘导航改变状态
        binding.chipApprove.setFocusable(false);
        binding.chipReject.setFocusable(false);

        // 强制刷新chip的视觉状态，确保选中状态正确显示
        binding.chipApprove.refreshDrawableState();
        binding.chipReject.refreshDrawableState();

        // 确保选中的chip保持正确的视觉状态
        if (binding.chipApprove.isChecked()) {
            Log.d(TAG, "Approve chip is checked, ensuring visual state");
            binding.chipApprove.invalidate();
        }
        if (binding.chipReject.isChecked()) {
            Log.d(TAG, "Reject chip is checked, ensuring visual state");
            binding.chipReject.invalidate();
        }

        Log.d(TAG, "Disabled audit result interaction while preserving visual state");
    }

    /**
     * 显示审核结果信息（仅对已通过的用户）
     */
    private void showAuditResultInfo(UserResponse user) {
        View assignmentView = binding.includeUserAssignment.getRoot();
        Log.d(TAG, "Showing audit result info for user: " + user.getUsername());

        // 显示工号
        if (user.getEmployeeId() != null && !user.getEmployeeId().isEmpty()) {
            ((com.google.android.material.textfield.TextInputEditText)
                    assignmentView.findViewById(R.id.et_employee_number)).setText(user.getEmployeeId());
            Log.d(TAG, "Set employee ID: " + user.getEmployeeId());
        }

        // 显示角色类型 - 使用格式化显示文本
        String roleDisplay = getRoleTypeDisplayText(user);
        if (roleDisplay != null && !roleDisplay.isEmpty()) {
            ((AutoCompleteTextView)
                    assignmentView.findViewById(R.id.act_role_type)).setText(roleDisplay);
            Log.d(TAG, "Set role: " + roleDisplay);
        } else {
            Log.w(TAG, "Role display is empty for user: " + user.getUsername());
        }

        // 显示部门信息 - 优先使用格式化显示，如果为空则尝试使用部门名称
        String departmentDisplay = getDepartmentDisplayText(user);
        if (departmentDisplay != null && !departmentDisplay.isEmpty()) {
            ((AutoCompleteTextView)
                    assignmentView.findViewById(R.id.act_department)).setText(departmentDisplay);
            Log.d(TAG, "Set department: " + departmentDisplay);
        } else {
            Log.w(TAG, "Department display is empty for user: " + user.getUsername());
        }

        // 显示职位信息 - 优先使用格式化显示，如果为空则尝试使用职位名称
        String positionDisplay = getPositionDisplayText(user);
        if (positionDisplay != null && !positionDisplay.isEmpty()) {
            ((AutoCompleteTextView)
                    assignmentView.findViewById(R.id.act_position)).setText(positionDisplay);
            Log.d(TAG, "Set position: " + positionDisplay);
        } else {
            Log.w(TAG, "Position display is empty for user: " + user.getUsername());
        }

        // 显示岗位信息 - 优先使用格式化显示，如果为空则尝试使用岗位名称
        String jobDisplay = getJobDisplayText(user);
        if (jobDisplay != null && !jobDisplay.isEmpty()) {
            ((AutoCompleteTextView)
                    assignmentView.findViewById(R.id.act_post)).setText(jobDisplay);
            Log.d(TAG, "Set job: " + jobDisplay);
        } else {
            Log.w(TAG, "Job display is empty for user: " + user.getUsername());
        }

        // 显示权限模板 - 使用格式化显示文本
        String permissionDisplay = getPermissionTemplateDisplayText(user);
        if (permissionDisplay != null && !permissionDisplay.isEmpty()) {
            ((AutoCompleteTextView)
                    assignmentView.findViewById(R.id.act_permission_template)).setText(permissionDisplay);
            Log.d(TAG, "Set permission template: " + permissionDisplay);
        } else {
            Log.w(TAG, "Permission template display is empty for user: " + user.getUsername());
        }

        // 禁用所有分配信息的输入控件
        disableAssignmentInputs(assignmentView);
    }

    /**
     * 获取角色类型显示文本
     */
    private String getRoleTypeDisplayText(UserResponse user) {
        // 如果用户的角色字段为空，直接返回
        if (user.getRole() == null || user.getRole().isEmpty()) {
            return null;
        }

        // 尝试从RoleType枚举中查找匹配的角色类型
        for (RoleType roleType : RoleType.values()) {
            // 先尝试按code匹配
            if (roleType.getCode().equals(user.getRole())) {
                return roleType.getName() + "(" + roleType.getCode() + ")";
            }
            // 再尝试按name匹配（以防服务器返回的是name）
            if (roleType.getName().equals(user.getRole())) {
                return roleType.getName() + "(" + roleType.getCode() + ")";
            }
        }

        // 如果没有找到匹配的角色类型，返回原始值
        Log.w(TAG, "No matching role type found for: " + user.getRole());
        return user.getRole();
    }

    /**
     * 获取部门显示文本
     */
    private String getDepartmentDisplayText(UserResponse user) {
        // 优先使用格式化显示方法
        String display = user.getDepartmentDisplay();
        if (display != null && !display.isEmpty()) {
            return display;
        }

        // 如果格式化显示为空，尝试从已加载的部门列表中查找
        if (user.getDepartment() != null && departments != null) {
            for (DepartmentResponse dept : departments) {
                if (dept.getCode().equals(user.getDepartment())) {
                    return dept.getName() + "(" + dept.getCode() + ")";
                }
            }
        }

        // 最后尝试使用部门名称
        return user.getDepartmentName();
    }

    /**
     * 获取职位显示文本
     */
    private String getPositionDisplayText(UserResponse user) {
        // 优先使用格式化显示方法
        String display = user.getPositionDisplay();
        if (display != null && !display.isEmpty()) {
            return display;
        }

        // 如果格式化显示为空，尝试从已加载的职位列表中查找
        if (user.getPosition() != null && positions != null) {
            for (PositionResponse position : positions) {
                if (position.getCode().equals(user.getPosition())) {
                    return position.getName() + "(" + position.getCode() + ")";
                }
            }
        }

        // 最后尝试使用职位名称
        return user.getPositionName();
    }

    /**
     * 获取岗位显示文本
     */
    private String getJobDisplayText(UserResponse user) {
        // 优先使用格式化显示方法
        String display = user.getJobDisplay();
        if (display != null && !display.isEmpty()) {
            return display;
        }

        // 如果格式化显示为空，尝试从已加载的岗位列表中查找
        if (user.getJob() != null && posts != null) {
            for (PostResponse post : posts) {
                if (post.getCode().equals(user.getJob())) {
                    return post.getName() + "(" + post.getCode() + ")";
                }
            }
        }

        // 最后尝试使用岗位名称
        return user.getJobName();
    }

    /**
     * 获取权限模板显示文本
     */
    private String getPermissionTemplateDisplayText(UserResponse user) {
        // 如果用户的权限模板字段为空，直接返回
        if (user.getPermissionTemplate() == null || user.getPermissionTemplate().isEmpty()) {
            return null;
        }

        // 如果已加载的权限列表为空，直接返回原始值
        if (permissions == null || permissions.isEmpty()) {
            Log.w(TAG, "Permissions list is not loaded, returning raw permission template: " + user.getPermissionTemplate());
            return user.getPermissionTemplate();
        }

        // 尝试从已加载的权限列表中查找匹配的权限模板
        for (PermissionResponse permission : permissions) {
            // 先尝试按code匹配
            if (permission.getCode().equals(user.getPermissionTemplate())) {
                return permission.getName() + "(" + permission.getCode() + ")";
            }
            // 再尝试按name匹配（以防服务器返回的是name）
            if (permission.getName().equals(user.getPermissionTemplate())) {
                return permission.getName() + "(" + permission.getCode() + ")";
            }
        }

        // 如果没有找到匹配的权限模板，返回原始值
        Log.w(TAG, "No matching permission template found for: " + user.getPermissionTemplate());
        return user.getPermissionTemplate();
    }

    /**
     * 禁用分配信息的所有输入控件
     */
    private void disableAssignmentInputs(View assignmentView) {
        // 禁用工号输入
        ((com.google.android.material.textfield.TextInputEditText)
                assignmentView.findViewById(R.id.et_employee_number)).setEnabled(false);

        // 禁用角色类型选择
        ((AutoCompleteTextView)
                assignmentView.findViewById(R.id.act_role_type)).setEnabled(false);

        // 禁用部门选择
        ((AutoCompleteTextView)
                assignmentView.findViewById(R.id.act_department)).setEnabled(false);

        // 禁用职位选择
        ((AutoCompleteTextView)
                assignmentView.findViewById(R.id.act_position)).setEnabled(false);

        // 禁用岗位选择
        ((AutoCompleteTextView)
                assignmentView.findViewById(R.id.act_post)).setEnabled(false);

        // 禁用权限模板选择
        ((AutoCompleteTextView)
                assignmentView.findViewById(R.id.act_permission_template)).setEnabled(false);
    }

    private void loadDropdownData() {
        loadDepartments();
        loadPositions();
        loadPosts();
        loadPermissions();
    }

    private void loadDepartments() {
        apiService.getDepartments().enqueue(new Callback<ApiResponse<List<DepartmentResponse>>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<List<DepartmentResponse>>> call,
                                   @NonNull Response<ApiResponse<List<DepartmentResponse>>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<List<DepartmentResponse>> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        departments = apiResponse.getData();
                        isDepartmentsLoaded = true;
                        Log.d(TAG, "Departments loaded: " + departments.size() + " items");
                        setupDepartmentDropdown();
                        checkAndShowProcessedUserInfo();
                    }
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<List<DepartmentResponse>>> call, @NonNull Throwable t) {
                Log.e(TAG, "获取部门列表失败: " + t.getMessage(), t);
            }
        });
    }

    private void loadPositions() {
        apiService.getPositions().enqueue(new Callback<ApiResponse<List<PositionResponse>>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<List<PositionResponse>>> call,
                                   @NonNull Response<ApiResponse<List<PositionResponse>>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<List<PositionResponse>> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        positions = apiResponse.getData();
                        isPositionsLoaded = true;
                        Log.d(TAG, "Positions loaded: " + positions.size() + " items");
                        setupPositionDropdown();
                        checkAndShowProcessedUserInfo();
                    }
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<List<PositionResponse>>> call, @NonNull Throwable t) {
                Log.e(TAG, "获取职位列表失败: " + t.getMessage(), t);
            }
        });
    }

    private void loadPosts() {
        apiService.getPosts().enqueue(new Callback<ApiResponse<List<PostResponse>>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<List<PostResponse>>> call,
                                   @NonNull Response<ApiResponse<List<PostResponse>>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<List<PostResponse>> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        posts = apiResponse.getData();
                        isPostsLoaded = true;
                        Log.d(TAG, "Posts loaded: " + posts.size() + " items");
                        setupPostDropdown();
                        checkAndShowProcessedUserInfo();
                    }
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<List<PostResponse>>> call, @NonNull Throwable t) {
                Log.e(TAG, "获取岗位列表失败: " + t.getMessage(), t);
            }
        });
    }

    private void loadPermissions() {
        apiService.getPermissions().enqueue(new Callback<ApiResponse<List<PermissionResponse>>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<List<PermissionResponse>>> call,
                                   @NonNull Response<ApiResponse<List<PermissionResponse>>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<List<PermissionResponse>> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        permissions = apiResponse.getData();
                        isPermissionsLoaded = true;
                        Log.d(TAG, "Permissions loaded: " + permissions.size() + " items");
                        for (PermissionResponse permission : permissions) {
                            Log.d(TAG, "Permission: " + permission.getName() + " (" + permission.getCode() + ")");
                        }
                        setupPermissionDropdown();
                        checkAndShowProcessedUserInfo();
                    }
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<List<PermissionResponse>>> call, @NonNull Throwable t) {
                Log.e(TAG, "获取权限列表失败: " + t.getMessage(), t);
            }
        });
    }

    private void showLoading(boolean isLoading) {
        if (isLoading) {
            binding.progressLoading.setVisibility(View.VISIBLE);
            binding.scrollContent.setVisibility(View.GONE);
        } else {
            binding.progressLoading.setVisibility(View.GONE);
            binding.scrollContent.setVisibility(View.VISIBLE);
        }
    }

    private void setupDepartmentDropdown() {
        if (departments == null) return;

        List<String> departmentNames = new ArrayList<>();
        for (DepartmentResponse dept : departments) {
            departmentNames.add(dept.getName() + "(" + dept.getCode() + ")");
        }

        ArrayAdapter<String> adapter = new ArrayAdapter<>(this,
                android.R.layout.simple_dropdown_item_1line, departmentNames);
        AutoCompleteTextView actDepartment = binding.includeUserAssignment.getRoot()
                .findViewById(R.id.act_department);
        actDepartment.setAdapter(adapter);
    }

    private void setupPositionDropdown() {
        if (positions == null) return;

        List<String> positionNames = new ArrayList<>();
        for (PositionResponse position : positions) {
            positionNames.add(position.getName() + "(" + position.getCode() + ")");
        }

        ArrayAdapter<String> adapter = new ArrayAdapter<>(this,
                android.R.layout.simple_dropdown_item_1line, positionNames);
        AutoCompleteTextView actPosition = binding.includeUserAssignment.getRoot()
                .findViewById(R.id.act_position);
        actPosition.setAdapter(adapter);
    }

    private void setupPostDropdown() {
        if (posts == null) return;

        List<String> postNames = new ArrayList<>();
        for (PostResponse post : posts) {
            postNames.add(post.getName() + "(" + post.getCode() + ")");
        }

        ArrayAdapter<String> adapter = new ArrayAdapter<>(this,
                android.R.layout.simple_dropdown_item_1line, postNames);
        AutoCompleteTextView actPost = binding.includeUserAssignment.getRoot()
                .findViewById(R.id.act_post);
        actPost.setAdapter(adapter);
    }

    private void setupPermissionDropdown() {
        if (permissions == null) return;

        List<String> permissionNames = new ArrayList<>();
        for (PermissionResponse permission : permissions) {
            permissionNames.add(permission.getName() + "(" + permission.getCode() + ")");
        }

        ArrayAdapter<String> adapter = new ArrayAdapter<>(this,
                android.R.layout.simple_dropdown_item_1line, permissionNames);
        AutoCompleteTextView actPermission = binding.includeUserAssignment.getRoot()
                .findViewById(R.id.act_permission_template);
        actPermission.setAdapter(adapter);

        // 设置角色类型下拉框
        setupRoleTypeDropdown();
    }

    /**
     * 设置角色类型下拉框，从RoleType枚举加载数据
     */
    private void setupRoleTypeDropdown() {
        List<String> roleTypeNames = new ArrayList<>();

        // 从RoleType枚举获取所有角色类型
        for (RoleType roleType : RoleType.values()) {
            roleTypeNames.add(roleType.getName() + "(" + roleType.getCode() + ")");
        }

        Log.d(TAG, "Loaded " + roleTypeNames.size() + " role types from enum");
        for (String roleTypeName : roleTypeNames) {
            Log.d(TAG, "Role type: " + roleTypeName);
        }

        ArrayAdapter<String> roleAdapter = new ArrayAdapter<>(this,
                android.R.layout.simple_dropdown_item_1line, roleTypeNames);
        AutoCompleteTextView actRoleType = binding.includeUserAssignment.getRoot()
                .findViewById(R.id.act_role_type);
        actRoleType.setAdapter(roleAdapter);
    }

    private void submitAudit() {
        if (!validateInput()) {
            return;
        }

        UserAuditRequest request = buildAuditRequest();

        binding.btnSubmit.setEnabled(false);
        binding.btnSubmit.setText("提交中...");

        userAuditRepository.auditUser(request).enqueue(new Callback<ApiResponse<UserResponse>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<UserResponse>> call,
                                   @NonNull Response<ApiResponse<UserResponse>> response) {
                binding.btnSubmit.setEnabled(true);
                binding.btnSubmit.setText("提交审核");

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<UserResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        UserResponse userResponse = apiResponse.getData();
                        Snackbar.make(binding.getRoot(), "审核提交成功", Snackbar.LENGTH_SHORT).show();
                        setResult(RESULT_OK);
                        finish();
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "审核提交失败");
                    }
                } else {
                    showError("审核提交失败");
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<UserResponse>> call, @NonNull Throwable t) {
                binding.btnSubmit.setEnabled(true);
                binding.btnSubmit.setText("提交审核");
                Log.e(TAG, "审核提交失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    private boolean validateInput() {
        // 检查是否选择了审核结果
        if (binding.chipGroupAuditResult.getCheckedChipIds().isEmpty()) {
            showError("请选择审核结果");
            return false;
        }

        // 如果是拒绝，必须填写备注
        if (!isApproved) {
            String remark = binding.etRemark.getText().toString().trim();
            if (remark.isEmpty()) {
                binding.tilRemark.setError("拒绝时必须填写拒绝原因");
                return false;
            } else {
                binding.tilRemark.setError(null);
            }
        }

        // 如果是通过，验证分配信息
        if (isApproved) {
            return validateAssignmentInfo();
        }

        return true;
    }

    private boolean validateAssignmentInfo() {
        View assignmentView = binding.includeUserAssignment.getRoot();
        boolean isValid = true;

        // 清除之前的错误信息
        clearAssignmentErrors(assignmentView);

        // 验证工号
        String employeeNumber = ((com.google.android.material.textfield.TextInputEditText)
                assignmentView.findViewById(R.id.et_employee_number)).getText().toString().trim();
        if (employeeNumber.isEmpty()) {
            ((com.google.android.material.textfield.TextInputLayout)
                    assignmentView.findViewById(R.id.til_employee_number)).setError("工号不能为空");
            isValid = false;
        }

        // 验证角色类型
        String roleType = ((AutoCompleteTextView)
                assignmentView.findViewById(R.id.act_role_type)).getText().toString().trim();
        if (roleType.isEmpty()) {
            ((com.google.android.material.textfield.TextInputLayout)
                    assignmentView.findViewById(R.id.til_role_type)).setError("请选择角色类型");
            isValid = false;
        }

        // 验证部门
        String department = ((AutoCompleteTextView)
                assignmentView.findViewById(R.id.act_department)).getText().toString().trim();
        if (department.isEmpty()) {
            ((com.google.android.material.textfield.TextInputLayout)
                    assignmentView.findViewById(R.id.til_department)).setError("请选择部门");
            isValid = false;
        }

        // 验证职务
        String position = ((AutoCompleteTextView)
                assignmentView.findViewById(R.id.act_position)).getText().toString().trim();
        if (position.isEmpty()) {
            ((com.google.android.material.textfield.TextInputLayout)
                    assignmentView.findViewById(R.id.til_position)).setError("请选择职务");
            isValid = false;
        }

        // 验证岗位
        String post = ((AutoCompleteTextView)
                assignmentView.findViewById(R.id.act_post)).getText().toString().trim();
        if (post.isEmpty()) {
            ((com.google.android.material.textfield.TextInputLayout)
                    assignmentView.findViewById(R.id.til_post)).setError("请选择岗位");
            isValid = false;
        }

        // 验证权限模板
        String permissionTemplate = ((AutoCompleteTextView)
                assignmentView.findViewById(R.id.act_permission_template)).getText().toString().trim();
        if (permissionTemplate.isEmpty()) {
            ((com.google.android.material.textfield.TextInputLayout)
                    assignmentView.findViewById(R.id.til_permission_template)).setError("请选择权限模板");
            isValid = false;
        }

        Log.d(TAG, "Assignment validation result: " + isValid);
        if (!isValid) {
            Log.d(TAG, "Validation failed - EmployeeNumber: '" + employeeNumber +
                    "', RoleType: '" + roleType +
                    "', Department: '" + department +
                    "', Position: '" + position +
                    "', Post: '" + post +
                    "', PermissionTemplate: '" + permissionTemplate + "'");
        }

        return isValid;
    }

    /**
     * 清除分配信息表单的所有错误信息
     */
    private void clearAssignmentErrors(View assignmentView) {
        // 清除工号错误
        ((com.google.android.material.textfield.TextInputLayout)
                assignmentView.findViewById(R.id.til_employee_number)).setError(null);

        // 清除角色类型错误
        ((com.google.android.material.textfield.TextInputLayout)
                assignmentView.findViewById(R.id.til_role_type)).setError(null);

        // 清除部门错误
        ((com.google.android.material.textfield.TextInputLayout)
                assignmentView.findViewById(R.id.til_department)).setError(null);

        // 清除职务错误
        ((com.google.android.material.textfield.TextInputLayout)
                assignmentView.findViewById(R.id.til_position)).setError(null);

        // 清除岗位错误
        ((com.google.android.material.textfield.TextInputLayout)
                assignmentView.findViewById(R.id.til_post)).setError(null);

        // 清除权限模板错误
        ((com.google.android.material.textfield.TextInputLayout)
                assignmentView.findViewById(R.id.til_permission_template)).setError(null);
    }

    private UserAuditRequest buildAuditRequest() {
        UserAuditRequest request = new UserAuditRequest();
        request.setId(userId);
        // 使用数字状态值：1=通过，-1=拒绝
        request.setStatus(isApproved ? "1" : "-1");
        request.setRemark(binding.etRemark.getText().toString().trim());

        if (isApproved) {
            View assignmentView = binding.includeUserAssignment.getRoot();

            // 设置工号
            String employeeNumber = ((com.google.android.material.textfield.TextInputEditText)
                    assignmentView.findViewById(R.id.et_employee_number)).getText().toString().trim();
            request.setEmployeeId(employeeNumber);

            // 设置角色类型 - 从显示文本中提取code
            String roleTypeText = ((AutoCompleteTextView)
                    assignmentView.findViewById(R.id.act_role_type)).getText().toString().trim();
            String roleTypeCode = getRoleTypeCodeByText(roleTypeText);
            request.setRole(roleTypeCode);

            // 设置部门ID
            String departmentText = ((AutoCompleteTextView)
                    assignmentView.findViewById(R.id.act_department)).getText().toString().trim();
            request.setDepartment(getDepartmentIdByText(departmentText));

            // 设置职位ID
            String positionText = ((AutoCompleteTextView)
                    assignmentView.findViewById(R.id.act_position)).getText().toString().trim();
            request.setPosition(getPositionIdByText(positionText));

            // 设置岗位ID
            String postText = ((AutoCompleteTextView)
                    assignmentView.findViewById(R.id.act_post)).getText().toString().trim();
            request.setJob(getPostIdByText(postText));

            // 设置权限模板ID
            String permissionText = ((AutoCompleteTextView)
                    assignmentView.findViewById(R.id.act_permission_template)).getText().toString().trim();
            request.setPermissionTemplate(getPermissionIdByText(permissionText));
        }

        return request;
    }

    private String getDepartmentIdByText(String text) {
        if (departments != null && !text.isEmpty()) {
            for (DepartmentResponse dept : departments) {
                String displayText = dept.getName() + "(" + dept.getCode() + ")";
                if (displayText.equals(text)) {
                    return dept.getCode();
                }
            }
        }
        return "";
    }

    private String getPositionIdByText(String text) {
        if (positions != null && !text.isEmpty()) {
            for (PositionResponse position : positions) {
                String displayText = position.getName() + "(" + position.getCode() + ")";
                if (displayText.equals(text)) {
                    return position.getCode();
                }
            }
        }
        return "";
    }

    private String getPostIdByText(String text) {
        if (posts != null && !text.isEmpty()) {
            for (PostResponse post : posts) {
                String displayText = post.getName() + "(" + post.getCode() + ")";
                if (displayText.equals(text)) {
                    return post.getCode();
                }
            }
        }
        return "";
    }

    private String getPermissionIdByText(String text) {
        if (permissions != null && !text.isEmpty()) {
            for (PermissionResponse permission : permissions) {
                String displayText = permission.getName() + "(" + permission.getCode() + ")";
                if (displayText.equals(text)) {
                    return permission.getCode();
                }
            }
        }
        return "";
    }

    /**
     * 从角色类型显示文本中提取code
     * 例如："管理员(admin)" -> "admin"
     */
    private String getRoleTypeCodeByText(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }

        // 遍历所有角色类型枚举值
        for (RoleType roleType : RoleType.values()) {
            String displayText = roleType.getName() + "(" + roleType.getCode() + ")";
            if (displayText.equals(text)) {
                Log.d(TAG, "Found role type code: " + roleType.getCode() + " for text: " + text);
                return roleType.getCode();
            }
        }

        // 如果没有找到匹配的格式化文本，可能用户直接输入了名称或code
        for (RoleType roleType : RoleType.values()) {
            if (roleType.getName().equals(text) || roleType.getCode().equals(text)) {
                Log.d(TAG, "Found role type code by direct match: " + roleType.getCode() + " for text: " + text);
                return roleType.getCode();
            }
        }

        Log.w(TAG, "No matching role type found for text: " + text + ", returning original text");
        return text; // 如果没有找到匹配的，返回原始文本
    }

    private void showError(String message) {
        Snackbar.make(binding.getRoot(), message, Snackbar.LENGTH_LONG).show();
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void finish() {
        super.finish();
        // 添加返回动画
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right);
    }
}
