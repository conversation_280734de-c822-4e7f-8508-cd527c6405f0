package com.opms.common.enums;

public enum Statuses {
    PENDING("0", "待审"),
    APPROVED("1", "通过"),
    REJECTED("-1", "拒绝");

    private final String code;
    private final String name;

    Statuses(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


    public static String getNameByCode(String code) {
        for (Statuses sde : Statuses.values()) {
            if (code.equalsIgnoreCase(sde.getCode())) {
                return sde.getName();
            }
        }
        return null;
    }
}
