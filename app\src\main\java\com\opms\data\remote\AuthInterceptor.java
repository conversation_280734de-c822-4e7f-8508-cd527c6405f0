package com.opms.data.remote;

import android.util.Log;

import com.opms.data.local.PreferencesManager;

import java.io.IOException;

import javax.inject.Inject;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

public class AuthInterceptor implements Interceptor {
    private final PreferencesManager preferencesManager;

    @Inject
    public AuthInterceptor(PreferencesManager preferencesManager) {
        this.preferencesManager = preferencesManager;
    }

    @Override
    public Response intercept(Chain chain) throws IOException {
        Request originalRequest = chain.request();
        String token = preferencesManager.getToken();
        String userName = preferencesManager.getUsername();
        Log.d("AuthInterceptor", "Token: " + token + ", UserName: " + userName); // 添加日志

        if (token.isEmpty() || userName.isEmpty()) {
            return chain.proceed(originalRequest);
        }

        Request newRequest = originalRequest.newBuilder()
                .header("Authorization", "Bearer " + token)
                .header("userName", userName)
                .build();

        return chain.proceed(newRequest);
    }
} 