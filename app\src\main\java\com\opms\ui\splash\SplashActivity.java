package com.opms.ui.splash;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;

import androidx.appcompat.app.AppCompatActivity;

import com.opms.MainActivity;
import com.opms.R;
import com.opms.common.constants.ApiConstants;
import com.opms.data.local.PreferencesManager;
import com.opms.ui.login.LoginActivity;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;

@AndroidEntryPoint
public class SplashActivity extends AppCompatActivity {

    @Inject
    PreferencesManager preferencesManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_splash);

        new Handler(Looper.getMainLooper()).postDelayed(this::checkLoginStatus, ApiConstants.SPLASH_DELAY);
    }

    private void checkLoginStatus() {
        if (preferencesManager.isLoggedIn()) {
            Intent intent = new Intent(this, MainActivity.class);
            startActivity(intent);
            // 添加淡入动画效果
            overridePendingTransition(R.anim.fade_in, R.anim.fade_out);
        } else {
            Intent intent = new Intent(this, LoginActivity.class);
            startActivity(intent);
            // 添加淡入动画效果
            overridePendingTransition(R.anim.fade_in, R.anim.fade_out);
        }
        finish();
    }
}