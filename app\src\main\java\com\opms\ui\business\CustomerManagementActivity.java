package com.opms.ui.business;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.google.android.material.snackbar.Snackbar;
import com.opms.R;
import com.opms.data.model.request.CustomerRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.CustomerListResponse;
import com.opms.data.model.response.CustomerResponse;
import com.opms.data.repository.CustomerRepository;
import com.opms.databinding.ActivityCustomerManagementBinding;
import com.opms.ui.business.adapter.CustomerAdapter;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class CustomerManagementActivity extends AppCompatActivity
        implements CustomerAdapter.OnCustomerClickListener {

    private static final String TAG = "CustomerManagement";

    @Inject
    CustomerRepository customerRepository;

    private ActivityCustomerManagementBinding binding;
    private CustomerAdapter adapter;
    private List<CustomerResponse> allCustomers;
    private List<CustomerResponse> filteredCustomers;
    private boolean isToolbarExpanded = false;

    // 分页相关变量
    private int currentPage = 1;
    private int pageSize = 10;
    private boolean isLoading = false;
    private boolean hasMoreData = true;
    private String currentKeyword = "";

    private ActivityResultLauncher<Intent> editActivityLauncher;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityCustomerManagementBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // 初始化列表
        allCustomers = new ArrayList<>();
        filteredCustomers = new ArrayList<>();

        setupActivityResultLauncher();
        setupToolbar();
        setupRecyclerView();
        setupButtons();
        loadCustomers();
    }

    private void setupActivityResultLauncher() {
        editActivityLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (result.getResultCode() == RESULT_OK) {
                        // 刷新列表
                        refreshCustomerList();
                    }
                }
        );
    }

    private void startAddActivity() {
        Intent intent = new Intent(this, CustomerEditActivity.class);
        editActivityLauncher.launch(intent);
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }

    private void setupToolbar() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("客户管理");
        }
    }

    private void setupRecyclerView() {
        adapter = new CustomerAdapter(this);
        adapter.setOnCustomerClickListener(this);

        LinearLayoutManager layoutManager = new LinearLayoutManager(this);
        binding.rvCustomers.setLayoutManager(layoutManager);
        binding.rvCustomers.setAdapter(adapter);

        // 添加滚动监听器实现加载更多
        binding.rvCustomers.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);

                if (dy > 0) { // 向下滚动
                    int visibleItemCount = layoutManager.getChildCount();
                    int totalItemCount = layoutManager.getItemCount();
                    int pastVisibleItems = layoutManager.findFirstVisibleItemPosition();

                    if (!isLoading && hasMoreData &&
                            (visibleItemCount + pastVisibleItems) >= totalItemCount - 3) {
                        // 距离底部还有3个item时开始加载
                        loadCustomers();
                    }
                }
            }
        });

        binding.swipeRefresh.setOnRefreshListener(() -> {
            refreshCustomerList();
        });
        binding.swipeRefresh.setColorSchemeResources(R.color.primary);
    }

    private void setupButtons() {
        // 添加按钮
        binding.fabAdd.setOnClickListener(v -> startAddActivity());

        // 工具栏展开/收起
        binding.btnToggleToolbar.setOnClickListener(v -> toggleToolbar());

        // 搜索功能
        binding.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                currentKeyword = s.toString().trim();
                resetPagination();
                loadCustomers();
            }

            @Override
            public void afterTextChanged(Editable s) {
            }
        });

        // 清除搜索
        binding.btnClearSearch.setOnClickListener(v -> {
            binding.etSearch.setText("");
            currentKeyword = "";
            resetPagination();
            loadCustomers();
        });
    }

    private void toggleToolbar() {
        isToolbarExpanded = !isToolbarExpanded;
        binding.cardExpandableTools.setVisibility(isToolbarExpanded ? View.VISIBLE : View.GONE);
        binding.btnToggleToolbar.setIconResource(
                isToolbarExpanded ? R.drawable.ic_expand_less : R.drawable.ic_expand_more
        );
        binding.btnToggleToolbar.setText(isToolbarExpanded ? "收起" : "工具");
    }

    private void resetPagination() {
        currentPage = 1;
        hasMoreData = true;
        isLoading = false; // 确保重置加载状态
        if (allCustomers == null) {
            allCustomers = new ArrayList<>();
        } else {
            allCustomers.clear();
        }
        if (filteredCustomers == null) {
            filteredCustomers = new ArrayList<>();
        } else {
            filteredCustomers.clear();
        }
        adapter.setCustomers(filteredCustomers);
    }

    private void refreshCustomerList() {
        Log.d(TAG, "refreshCustomerList: 开始刷新客户列表");

        // 强制刷新列表
        isLoading = false;
        hasMoreData = true;
        currentPage = 1;

        // 清空现有数据
        if (allCustomers != null) {
            allCustomers.clear();
        }
        if (filteredCustomers != null) {
            filteredCustomers.clear();
        }

        // 通知适配器数据已清空
        adapter.setCustomers(filteredCustomers);
        adapter.notifyDataSetChanged();

        Log.d(TAG, "refreshCustomerList: 数据已清空，开始重新加载");

        // 重新加载数据
        loadCustomers();
    }

    private void loadCustomers() {
        if (isLoading || !hasMoreData) {
            return;
        }

        isLoading = true;
        showLoading(true);

        Call<ApiResponse<CustomerListResponse>> call = customerRepository.getCustomerList(
                currentPage, pageSize, currentKeyword);
        call.enqueue(new Callback<ApiResponse<CustomerListResponse>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<CustomerListResponse>> call,
                                   @NonNull Response<ApiResponse<CustomerListResponse>> response) {
                isLoading = false;
                showLoading(false);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<CustomerListResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        CustomerListResponse customerListResponse = apiResponse.getData();
                        List<CustomerResponse> newCustomers = customerListResponse.getList();

                        if (currentPage == 1) {
                            // 第一页，清空现有数据
                            allCustomers.clear();
                            filteredCustomers.clear();
                        }

                        // 添加新数据
                        if (newCustomers != null) {
                            allCustomers.addAll(newCustomers);
                            filteredCustomers.addAll(newCustomers);
                        }

                        // 检查是否还有更多数据
                        hasMoreData = (customerListResponse.getPage() * customerListResponse.getSize() < customerListResponse.getTotal());

                        // 更新UI
                        adapter.setCustomers(filteredCustomers);
                        updateCustomerCount();

                        if (filteredCustomers.isEmpty()) {
                            showEmptyView();
                        } else {
                            hideEmptyView();
                        }

                        // 准备下一页
                        currentPage++;
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "获取客户列表失败");
                        if (currentPage == 1) {
                            showEmptyView();
                        }
                    }
                } else {
                    showError("获取客户列表失败");
                    if (currentPage == 1) {
                        showEmptyView();
                    }
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<CustomerListResponse>> call, @NonNull Throwable t) {
                isLoading = false;
                showLoading(false);
                Log.e(TAG, "获取客户列表失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
                if (currentPage == 1) {
                    showEmptyView();
                }
            }
        });
    }

    private void updateCustomerCount() {
        int count = filteredCustomers != null ? filteredCustomers.size() : 0;
        binding.tvCustomerCount.setText("共 " + count + " 个客户");
    }

    private void showLoading(boolean show) {
        binding.progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
        binding.swipeRefresh.setRefreshing(false);
    }

    private void showError(String message) {
        Snackbar.make(binding.getRoot(), message, Snackbar.LENGTH_LONG).show();
    }

    private void showEmptyView() {
        binding.llEmpty.setVisibility(View.VISIBLE);
        binding.rvCustomers.setVisibility(View.GONE);
    }

    private void hideEmptyView() {
        binding.llEmpty.setVisibility(View.GONE);
        binding.rvCustomers.setVisibility(View.VISIBLE);
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onCustomerDelete(CustomerResponse customer) {
        showDeleteConfirmDialog(customer);
    }

    @Override
    public void onCustomerEditInNewPage(CustomerResponse customer) {
        Intent intent = new Intent(this, CustomerEditActivity.class);
        intent.putExtra("customer_id", (int) customer.getId());
        editActivityLauncher.launch(intent);
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }

    private void showDeleteConfirmDialog(CustomerResponse customer) {
        new MaterialAlertDialogBuilder(this)
                .setTitle("删除确认")
                .setMessage("确定要删除客户 \"" + customer.getName() + "\" 吗？")
                .setPositiveButton("删除", (dialog, which) -> deleteCustomer(customer))
                .setNegativeButton("取消", null)
                .show();
    }

    private void deleteCustomer(CustomerResponse customer) {
        CustomerRequest request = new CustomerRequest();
        request.setId((int) customer.getId());

        Call<ApiResponse<Void>> call = customerRepository.deleteCustomer(request);
        call.enqueue(new Callback<ApiResponse<Void>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<Void>> call,
                                   @NonNull Response<ApiResponse<Void>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<Void> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        Log.d(TAG, "删除客户成功，开始刷新列表");
                        Snackbar.make(binding.getRoot(), "删除成功", Snackbar.LENGTH_SHORT).show();

                        // 延迟一点时间再刷新，确保Snackbar显示
                        binding.getRoot().postDelayed(() -> {
                            refreshCustomerList();
                        }, 100);
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "删除失败");
                    }
                } else {
                    showError("删除失败");
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<Void>> call, @NonNull Throwable t) {
                Log.e(TAG, "删除客户失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
            }
        });
    }
}
