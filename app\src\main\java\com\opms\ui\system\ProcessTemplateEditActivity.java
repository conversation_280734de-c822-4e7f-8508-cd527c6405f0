package com.opms.ui.system;

import android.os.Bundle;
import android.util.Log;
import android.view.MenuItem;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import com.google.android.material.snackbar.Snackbar;
import com.opms.R;
import com.opms.data.model.request.ProcessTemplateRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.ProcessTemplateResponse;
import com.opms.data.repository.ProcessTemplateRepository;
import com.opms.databinding.ActivityProcessTemplateEditBinding;

import java.util.List;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class ProcessTemplateEditActivity extends AppCompatActivity {

    private static final String TAG = "ProcessTemplateEdit";

    @Inject
    ProcessTemplateRepository processTemplateRepository;

    private ActivityProcessTemplateEditBinding binding;
    private boolean isEditMode = false;
    private int processTemplateId = -1;
    private List<ProcessTemplateResponse> allProcessTemplates;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityProcessTemplateEditBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setupToolbar();
        setupButtons();
        loadExistingData();
        loadAllProcessTemplates();
    }

    private void setupToolbar() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
    }

    private void setupButtons() {
        binding.btnSave.setOnClickListener(v -> saveProcessTemplate());
        binding.btnCancel.setOnClickListener(v -> finish());
    }

    private void loadExistingData() {
        // 检查是否是编辑模式
        processTemplateId = getIntent().getIntExtra("process_template_id", -1);
        if (processTemplateId != -1) {
            isEditMode = true;
            if (getSupportActionBar() != null) {
                getSupportActionBar().setTitle("编辑流程");
            }

            // 填充现有数据
            String name = getIntent().getStringExtra("process_template_name");
            String code = getIntent().getStringExtra("process_template_code");
            String description = getIntent().getStringExtra("process_template_description");
            int sequence = getIntent().getIntExtra("process_template_sequence", 1);
            String status = getIntent().getStringExtra("process_template_status");

            binding.etName.setText(name);
            binding.etCode.setText(code);
            binding.etCode.setEnabled(false); // 编辑时不允许修改代码
            binding.etDescription.setText(description);
            binding.etSequence.setText(String.valueOf(sequence));

            // 设置状态
            binding.switchStatus.setChecked("1".equals(status) || "ACTIVE".equals(status));
        } else {
            if (getSupportActionBar() != null) {
                getSupportActionBar().setTitle("添加流程");
            }
        }
    }

    private void loadAllProcessTemplates() {
        processTemplateRepository.getProcessTemplates().enqueue(new Callback<ApiResponse<List<ProcessTemplateResponse>>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<List<ProcessTemplateResponse>>> call,
                                   @NonNull Response<ApiResponse<List<ProcessTemplateResponse>>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<List<ProcessTemplateResponse>> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        allProcessTemplates = apiResponse.getData();
                    }
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<List<ProcessTemplateResponse>>> call, @NonNull Throwable t) {
                Log.e(TAG, "获取流程列表失败: " + t.getMessage(), t);
            }
        });
    }

    private void saveProcessTemplate() {
        if (!validateInput()) {
            return;
        }

        String name = binding.etName.getText().toString().trim();
        String code = binding.etCode.getText().toString().trim();
        String description = binding.etDescription.getText().toString().trim();
        int sequence = Integer.parseInt(binding.etSequence.getText().toString().trim());
        boolean status = binding.switchStatus.isChecked();

        ProcessTemplateRequest request = new ProcessTemplateRequest();
        request.setName(name);
        request.setCode(code);
        request.setDescription(description);
        request.setSequence(sequence);
        request.setStatus(status ? "1" : "0");

        if (isEditMode) {
            request.setId(processTemplateId);
            updateProcessTemplate(request);
        } else {
            createProcessTemplate(request);
        }
    }

    private boolean validateInput() {
        boolean isValid = true;

        // 验证名称
        String name = binding.etName.getText().toString().trim();
        if (name.isEmpty()) {
            binding.tilName.setError("流程名称不能为空");
            isValid = false;
        } else {
            binding.tilName.setError(null);
        }

        // 验证代码
        String code = binding.etCode.getText().toString().trim();
        if (code.isEmpty()) {
            binding.tilCode.setError("流程代码不能为空");
            isValid = false;
        } else {
            binding.tilCode.setError(null);
        }

        // 验证序号
        String sequenceStr = binding.etSequence.getText().toString().trim();
        if (sequenceStr.isEmpty()) {
            binding.tilSequence.setError("流程序号不能为空");
            isValid = false;
        } else {
            try {
                int sequence = Integer.parseInt(sequenceStr);
                if (sequence <= 0) {
                    binding.tilSequence.setError("流程序号必须大于0");
                    isValid = false;
                } else {
                    binding.tilSequence.setError(null);
                }
            } catch (NumberFormatException e) {
                binding.tilSequence.setError("请输入有效的数字");
                isValid = false;
            }
        }

        // 检查代码是否重复（基于内存中的数据）
        if (isValid && allProcessTemplates != null) {
            for (ProcessTemplateResponse processTemplate : allProcessTemplates) {
                // 如果是编辑模式，跳过当前编辑的流程
                if (isEditMode && processTemplate.getId() == processTemplateId) {
                    continue;
                }

                if (code.equals(processTemplate.getCode())) {
                    String status = processTemplate.getStatus();
                    String statusText = "1".equals(status) || "ACTIVE".equals(status) ? "" : "(已禁用)";
                    String msg = "流程代码[" + code + "]已存在[" + processTemplate.getName() + "]" + statusText;
                    binding.tilCode.setError(msg);
                    isValid = false;
                    break;
                }
            }
        }

        // 检查序号是否重复（基于内存中的数据）
        if (isValid && allProcessTemplates != null && !sequenceStr.isEmpty()) {
            try {
                int sequence = Integer.parseInt(sequenceStr);
                for (ProcessTemplateResponse processTemplate : allProcessTemplates) {
                    // 如果是编辑模式，跳过当前编辑的流程
                    if (isEditMode && processTemplate.getId() == processTemplateId) {
                        continue;
                    }

                    if (processTemplate.getSequence() == sequence) {
                        String status = processTemplate.getStatus();
                        String statusText = "1".equals(status) || "ACTIVE".equals(status) ? "" : "(已禁用)";
                        String msg = "流程序号[" + sequence + "]已存在[" + processTemplate.getName() + "]" + statusText;
                        binding.tilSequence.setError(msg);
                        isValid = false;
                        break;
                    }
                }
            } catch (NumberFormatException e) {
                // 已在上面处理
            }
        }

        return isValid;
    }

    private void createProcessTemplate(ProcessTemplateRequest request) {
        binding.btnSave.setEnabled(false);

        processTemplateRepository.createProcessTemplate(request).enqueue(new Callback<ApiResponse<ProcessTemplateResponse>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<ProcessTemplateResponse>> call,
                                   @NonNull Response<ApiResponse<ProcessTemplateResponse>> response) {
                binding.btnSave.setEnabled(true);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<ProcessTemplateResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        Toast.makeText(ProcessTemplateEditActivity.this, "流程创建成功", Toast.LENGTH_SHORT).show();
                        setResult(RESULT_OK);
                        finish();
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "创建失败");
                    }
                } else {
                    showError("创建失败");
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<ProcessTemplateResponse>> call, @NonNull Throwable t) {
                binding.btnSave.setEnabled(true);
                Log.e(TAG, "创建流程失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    private void updateProcessTemplate(ProcessTemplateRequest request) {
        binding.btnSave.setEnabled(false);

        processTemplateRepository.updateProcessTemplate(processTemplateId, request)
                .enqueue(new Callback<ApiResponse<ProcessTemplateResponse>>() {
                    @Override
                    public void onResponse(@NonNull Call<ApiResponse<ProcessTemplateResponse>> call,
                                           @NonNull Response<ApiResponse<ProcessTemplateResponse>> response) {
                        binding.btnSave.setEnabled(true);

                        if (response.isSuccessful() && response.body() != null) {
                            ApiResponse<ProcessTemplateResponse> apiResponse = response.body();
                            if (apiResponse.isSuccess()) {
                                Toast.makeText(ProcessTemplateEditActivity.this, "流程更新成功", Toast.LENGTH_SHORT).show();
                                setResult(RESULT_OK);
                                finish();
                            } else {
                                showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "更新失败");
                            }
                        } else {
                            showError("更新失败");
                        }
                    }

                    @Override
                    public void onFailure(@NonNull Call<ApiResponse<ProcessTemplateResponse>> call, @NonNull Throwable t) {
                        binding.btnSave.setEnabled(true);
                        Log.e(TAG, "更新流程失败: " + t.getMessage(), t);
                        showError("网络错误: " + t.getMessage());
                    }
                });
    }

    private void showError(String message) {
        Snackbar.make(binding.getRoot(), message, Snackbar.LENGTH_LONG).show();
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void finish() {
        super.finish();
        // 添加返回动画
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right);
    }
}
