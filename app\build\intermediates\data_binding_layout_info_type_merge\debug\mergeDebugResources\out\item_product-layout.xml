<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_product" modulePackage="com.opms" filePath="app\src\main\res\layout\item_product.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView" rootNodeViewId="@+id/card_product"><Targets><Target id="@+id/card_product" tag="layout/item_product_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="147" endOffset="51"/></Target><Target id="@+id/tv_product_name" view="TextView"><Expressions/><location startLine="34" startOffset="16" endLine="42" endOffset="46"/></Target><Target id="@+id/tv_product_status" view="TextView"><Expressions/><location startLine="44" startOffset="16" endLine="54" endOffset="45"/></Target><Target id="@+id/tv_product_code" view="TextView"><Expressions/><location startLine="59" startOffset="12" endLine="66" endOffset="41"/></Target><Target id="@+id/tv_product_model" view="TextView"><Expressions/><location startLine="69" startOffset="12" endLine="76" endOffset="41"/></Target><Target id="@+id/tv_product_standard" view="TextView"><Expressions/><location startLine="89" startOffset="12" endLine="95" endOffset="41"/></Target><Target id="@+id/tv_product_price" view="TextView"><Expressions/><location startLine="98" startOffset="12" endLine="106" endOffset="42"/></Target><Target id="@+id/btn_edit" view="ImageView"><Expressions/><location startLine="117" startOffset="16" endLine="127" endOffset="52"/></Target><Target id="@+id/btn_delete" view="ImageView"><Expressions/><location startLine="130" startOffset="16" endLine="139" endOffset="50"/></Target></Targets></Layout>