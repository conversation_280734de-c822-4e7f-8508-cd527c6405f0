package com.opms.ui.system.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.button.MaterialButton;
import com.google.android.material.switchmaterial.SwitchMaterial;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.opms.R;
import com.opms.data.model.response.PostResponse;

import java.util.ArrayList;
import java.util.List;

/**
 * 岗位列表适配器
 */
public class PostAdapter extends RecyclerView.Adapter<PostAdapter.ViewHolder> {
    private final Context context;
    private List<PostResponse> posts;
    private OnPostClickListener onPostClickListener;
    private int editingPosition = -1; // 当前编辑的位置

    public PostAdapter(Context context) {
        this.context = context;
        this.posts = new ArrayList<>();
    }

    public void setOnPostClickListener(OnPostClickListener listener) {
        this.onPostClickListener = listener;
    }

    public void setPosts(List<PostResponse> posts) {
        this.posts = posts != null ? posts : new ArrayList<>();
        this.editingPosition = -1; // 重置编辑状态
        notifyDataSetChanged();
    }

    public void setEditingPosition(int position) {
        int oldEditingPosition = this.editingPosition;
        this.editingPosition = position;

        // 刷新旧的编辑项
        if (oldEditingPosition != -1) {
            notifyItemChanged(oldEditingPosition);
        }

        // 刷新新的编辑项
        if (position != -1) {
            notifyItemChanged(position);
        }
    }

    public void cancelEditing() {
        if (editingPosition != -1) {
            int position = editingPosition;
            editingPosition = -1;
            notifyItemChanged(position);
        }
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_post, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        PostResponse postResponse = posts.get(position);
        holder.bind(postResponse);
    }

    @Override
    public int getItemCount() {
        return posts.size();
    }

    public interface OnPostClickListener {
        void onPostEdit(PostResponse post, int itemPosition);

        void onPostSave(PostResponse post, int itemPosition, String newName, boolean newStatus);

        void onPostCancel(int itemPosition);
        void onPostDelete(PostResponse post);
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        // 查看模式控件
        private final LinearLayout llViewMode;
        private final TextView tvName;
        private final TextView tvCode;
        private final TextView tvStatus;

        // 编辑模式控件
        private final LinearLayout llEditMode;
        private final TextInputLayout tilEditName;
        private final TextInputEditText etEditName;
        private final SwitchMaterial switchEditStatus;
        private final MaterialButton btnSave;
        private final MaterialButton btnCancel;

        // 删除按钮
        private final ImageView ivDelete;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            // 查看模式控件
            this.llViewMode = itemView.findViewById(R.id.ll_view_mode);
            this.tvName = itemView.findViewById(R.id.tv_name);
            this.tvCode = itemView.findViewById(R.id.tv_code);
            this.tvStatus = itemView.findViewById(R.id.tv_status);

            // 编辑模式控件
            this.llEditMode = itemView.findViewById(R.id.ll_edit_mode);
            this.tilEditName = itemView.findViewById(R.id.til_edit_name);
            this.etEditName = itemView.findViewById(R.id.et_edit_name);
            this.switchEditStatus = itemView.findViewById(R.id.switch_edit_status);
            this.btnSave = itemView.findViewById(R.id.btn_save);
            this.btnCancel = itemView.findViewById(R.id.btn_cancel);

            // 删除按钮
            this.ivDelete = itemView.findViewById(R.id.iv_delete);
        }

        public void bind(PostResponse post) {
            int currentPosition = getAdapterPosition();
            boolean isEditing = currentPosition == editingPosition;

            // 显示/隐藏对应的模式
            llViewMode.setVisibility(isEditing ? View.GONE : View.VISIBLE);
            llEditMode.setVisibility(isEditing ? View.VISIBLE : View.GONE);

            if (isEditing) {
                // 编辑模式
                etEditName.setText(post.getName());
                switchEditStatus.setChecked("ACTIVE".equals(post.getStatus()) || "1".equals(post.getStatus()));

                // 设置编辑模式的事件
                btnSave.setOnClickListener(v -> {
                    String newName = etEditName.getText().toString().trim();
                    boolean newStatus = switchEditStatus.isChecked();

                    if (newName.isEmpty()) {
                        tilEditName.setError("岗位名称不能为空");
                        return;
                    } else {
                        tilEditName.setError(null);
                    }

                    if (onPostClickListener != null) {
                        onPostClickListener.onPostSave(post, currentPosition, newName, newStatus);
                    }
                });

                btnCancel.setOnClickListener(v -> {
                    if (onPostClickListener != null) {
                        onPostClickListener.onPostCancel(currentPosition);
                    }
                });
            } else {
                // 查看模式
                tvName.setText(post.getName());
                tvCode.setText(post.getCode());

                // 设置状态
                String status = post.getStatus();
                if ("ACTIVE".equals(status) || "1".equals(status)) {
                    tvStatus.setText("启用");
                    tvStatus.setTextColor(context.getColor(R.color.success));
                    tvStatus.setBackgroundResource(R.drawable.bg_status_active);
                } else {
                    tvStatus.setText("禁用");
                    tvStatus.setTextColor(context.getColor(R.color.error));
                    tvStatus.setBackgroundResource(R.drawable.bg_status_inactive);
                }

                // 设置点击事件 - 进入编辑模式
                itemView.setOnClickListener(v -> {
                    if (onPostClickListener != null) {
                        onPostClickListener.onPostEdit(post, currentPosition);
                    }
                });
            }

            // 删除按钮事件
            ivDelete.setOnClickListener(v -> {
                if (onPostClickListener != null) {
                    onPostClickListener.onPostDelete(post);
                }
            });
        }
    }
}
