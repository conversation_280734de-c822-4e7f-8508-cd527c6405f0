package com.opms.data.local.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.opms.data.local.entity.Product;

import java.util.List;

@Dao
public interface ProductDao {
    @Insert
    long insert(Product product);

    @Update
    void update(Product product);

    @Delete
    void delete(Product product);

    @Query("SELECT * FROM products WHERE code = :code")
    Product findByCode(String code);

    @Query("SELECT * FROM products WHERE id = :id")
    Product findById(long id);

    @Query("SELECT * FROM products")
    List<Product> getAllProducts();

    @Query("SELECT * FROM products WHERE name LIKE '%' || :keyword || '%' OR code LIKE '%' || :keyword || '%'")
    List<Product> searchProducts(String keyword);
} 