package com.opms.data.local.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.opms.data.local.entity.Post;

import java.util.List;

@Dao
public interface PostDao {
    @Insert
    long insert(Post post);

    @Update
    void update(Post post);

    @Delete
    void delete(Post post);

    @Query("SELECT * FROM posts WHERE code = :code")
    Post findByCode(String code);

    @Query("SELECT * FROM posts WHERE id = :id")
    Post findById(long id);

    @Query("SELECT * FROM posts")
    List<Post> getAllPosts();
} 