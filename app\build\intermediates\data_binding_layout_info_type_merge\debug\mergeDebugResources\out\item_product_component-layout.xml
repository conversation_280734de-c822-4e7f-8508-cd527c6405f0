<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_product_component" modulePackage="com.opms" filePath="app\src\main\res\layout\item_product_component.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView" rootNodeViewId="@+id/card_component"><Targets><Target id="@+id/card_component" tag="layout/item_product_component_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="201" endOffset="51"/></Target><Target id="@+id/iv_component_image" view="ImageView"><Expressions/><location startLine="25" startOffset="12" endLine="30" endOffset="65"/></Target><Target id="@+id/tv_component_name" view="TextView"><Expressions/><location startLine="48" startOffset="16" endLine="56" endOffset="46"/></Target><Target id="@+id/tv_component_status" view="TextView"><Expressions/><location startLine="58" startOffset="16" endLine="68" endOffset="45"/></Target><Target id="@+id/tv_component_code" view="TextView"><Expressions/><location startLine="73" startOffset="12" endLine="80" endOffset="41"/></Target><Target id="@+id/tv_component_model" view="TextView"><Expressions/><location startLine="89" startOffset="16" endLine="96" endOffset="45"/></Target><Target id="@+id/tv_component_standard" view="TextView"><Expressions/><location startLine="98" startOffset="16" endLine="105" endOffset="45"/></Target><Target id="@+id/tv_component_quantity" view="TextView"><Expressions/><location startLine="133" startOffset="20" endLine="140" endOffset="50"/></Target><Target id="@+id/til_component_quantity" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="143" startOffset="20" endLine="164" endOffset="75"/></Target><Target id="@+id/et_component_quantity" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="155" startOffset="24" endLine="162" endOffset="53"/></Target><Target id="@+id/btn_edit_component" view="ImageView"><Expressions/><location startLine="169" startOffset="16" endLine="180" endOffset="52"/></Target><Target id="@+id/btn_delete_component" view="ImageView"><Expressions/><location startLine="183" startOffset="16" endLine="193" endOffset="50"/></Target></Targets></Layout>