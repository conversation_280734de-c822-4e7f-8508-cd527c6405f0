<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_product_component" modulePackage="com.opms" filePath="app\src\main\res\layout\item_product_component.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView" rootNodeViewId="@+id/card_component"><Targets><Target id="@+id/card_component" tag="layout/item_product_component_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="188" endOffset="51"/></Target><Target id="@+id/tv_component_name" view="TextView"><Expressions/><location startLine="33" startOffset="16" endLine="41" endOffset="46"/></Target><Target id="@+id/tv_component_status" view="TextView"><Expressions/><location startLine="43" startOffset="16" endLine="53" endOffset="45"/></Target><Target id="@+id/tv_component_model" view="TextView"><Expressions/><location startLine="58" startOffset="12" endLine="65" endOffset="41"/></Target><Target id="@+id/tv_component_quantity" view="TextView"><Expressions/><location startLine="83" startOffset="16" endLine="90" endOffset="46"/></Target><Target id="@+id/til_component_quantity" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="93" startOffset="16" endLine="114" endOffset="71"/></Target><Target id="@+id/et_component_quantity" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="105" startOffset="20" endLine="112" endOffset="49"/></Target><Target id="@+id/tv_component_code" view="TextView"><Expressions/><location startLine="129" startOffset="12" endLine="136" endOffset="41"/></Target><Target id="@+id/tv_component_standard" view="TextView"><Expressions/><location startLine="139" startOffset="12" endLine="145" endOffset="41"/></Target><Target id="@+id/btn_edit_component" view="ImageView"><Expressions/><location startLine="156" startOffset="16" endLine="167" endOffset="52"/></Target><Target id="@+id/btn_delete_component" view="ImageView"><Expressions/><location startLine="170" startOffset="16" endLine="180" endOffset="50"/></Target></Targets></Layout>