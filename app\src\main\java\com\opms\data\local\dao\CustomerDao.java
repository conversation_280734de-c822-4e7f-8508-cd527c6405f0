package com.opms.data.local.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.opms.data.local.entity.Customer;

import java.util.List;

@Dao
public interface CustomerDao {
    @Insert
    void insert(Customer customer);

    @Update
    void update(Customer customer);

    @Delete
    void delete(Customer customer);

    @Query("SELECT * FROM customers WHERE code = :code")
    Customer findByCode(String code);

    @Query("SELECT * FROM customers WHERE id = :id")
    Customer findById(int id);

    @Query("SELECT * FROM customers")
    List<Customer> getAllCustomers();

    @Query("SELECT * FROM customers WHERE name LIKE '%' || :keyword || '%' OR code LIKE '%' || :keyword || '%'")
    List<Customer> searchCustomers(String keyword);
} 