# 多图片上传功能使用指南

## 📋 功能概述

ImageUploadUtils 现已支持多图片的显示、上传和删除功能，提供了完整的多图片管理解决方案。

## 🛠️ 新增功能

### 1. API 接口扩展

#### **新增的 API 接口**

```java
// 批量图片上传
@Multipart
@POST("api/image/uploadMultipleImages")
Call<ApiResponse<List<String>>> uploadMultipleImages(@Part("businessType") RequestBody businessType,
                                                     @Part("businessId") RequestBody businessId,
                                                     @Part("operator") RequestBody operator,
                                                     @Part List<MultipartBody.Part> images);

// 删除图片
@POST("api/image/deleteImage")
Call<ApiResponse<Void>> deleteImage(@Query("businessType") String businessType,
                                    @Query("businessId") String businessId,
                                    @Query("operator") String operator,
                                    @Query("imageUrl") String imageUrl);

// 获取图片列表
@GET("api/image/getImages")
Call<ApiResponse<List<String>>> getImages(@Query("businessType") String businessType,
                                          @Query("businessId") String businessId);
```

### 2. 新增回调接口

#### **多图片上传回调**

```java
public interface MultiImageUploadCallback {
    void onUploadStart();
    void onUploadProgress(int uploadedCount, int totalCount);
    void onSingleImageSuccess(String imageUrl);
    void onSingleImageFailure(String errorMessage);
    void onUploadComplete(List<String> successUrls, List<String> failureMessages);
}
```

#### **图片删除回调**

```java
public interface ImageDeleteCallback {
    void onDeleteStart();
    void onDeleteSuccess();
    void onDeleteFailure(String errorMessage);
    void onDeleteComplete();
}
```

#### **图片列表获取回调**

```java
public interface ImageListCallback {
    void onLoadStart();
    void onLoadSuccess(List<String> imageUrls);
    void onLoadFailure(String errorMessage);
    void onLoadComplete();
}
```

### 3. 核心工具方法

#### **批量上传图片**

```java
ImageUploadUtils.uploadMultipleImages(
    context,
    apiService,
    imageUris,                              // List<Uri> 图片URI列表
    ImageUploadUtils.BusinessType.PRODUCT,  // 业务类型
    String.valueOf(productId),              // 业务ID
    operator,                               // 操作人
    new ImageUploadUtils.MultiImageUploadCallback() {
        @Override
        public void onUploadStart() {
            // 开始上传
        }

        @Override
        public void onUploadProgress(int uploadedCount, int totalCount) {
            // 上传进度更新
        }

        @Override
        public void onSingleImageSuccess(String imageUrl) {
            // 单个图片上传成功
        }

        @Override
        public void onSingleImageFailure(String errorMessage) {
            // 单个图片上传失败
        }

        @Override
        public void onUploadComplete(List<String> successUrls, List<String> failureMessages) {
            // 全部上传完成
        }
    }
);
```

#### **删除图片**

```java
ImageUploadUtils.deleteImage(
    apiService,
    ImageUploadUtils.BusinessType.PRODUCT,  // 业务类型
    String.valueOf(productId),              // 业务ID
    operator,                               // 操作人
    imageUrl,                               // 要删除的图片URL
    new ImageUploadUtils.ImageDeleteCallback() {
        @Override
        public void onDeleteStart() {
            // 开始删除
        }

        @Override
        public void onDeleteSuccess() {
            // 删除成功
        }

        @Override
        public void onDeleteFailure(String errorMessage) {
            // 删除失败
        }

        @Override
        public void onDeleteComplete() {
            // 删除完成
        }
    }
);
```

#### **获取图片列表**

```java
ImageUploadUtils.getImages(
    apiService,
    ImageUploadUtils.BusinessType.PRODUCT,  // 业务类型
    String.valueOf(productId),              // 业务ID
    new ImageUploadUtils.ImageListCallback() {
        @Override
        public void onLoadStart() {
            // 开始加载
        }

        @Override
        public void onLoadSuccess(List<String> imageUrls) {
            // 加载成功
        }

        @Override
        public void onLoadFailure(String errorMessage) {
            // 加载失败
        }

        @Override
        public void onLoadComplete() {
            // 加载完成
        }
    }
);
```

## 🎨 UI 管理工具

### MultiImageManager 使用方法

#### **1. 基本设置**

```java
public class ProductEditActivity extends AppCompatActivity {
    
    private MultiImageManager imageManager;
    
    @Inject
    ImageUploadRepository imageUploadRepository;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_product_edit);
        
        // 初始化多图片管理器
        RecyclerView recyclerView = findViewById(R.id.rv_images);
        imageManager = new MultiImageManager(this, recyclerView);
        
        // 设置配置
        imageManager.setup(
            imageUploadRepository,
            ImageUploadUtils.BusinessType.PRODUCT,
            String.valueOf(productId),
            "currentUser",
            isEditMode  // 是否为编辑模式
        );
        
        // 设置监听器
        imageManager.setOnImageActionListener(new MultiImageManager.OnImageActionListener() {
            @Override
            public void onAddImageClick() {
                // 点击添加图片按钮
                openImagePicker();
            }
            
            @Override
            public void onImageClick(MultiImageManager.ImageItem item, int position) {
                // 点击图片
                showImagePreview(item);
            }
            
            @Override
            public void onImageLongClick(MultiImageManager.ImageItem item, int position) {
                // 长按图片
                showImageOptions(item, position);
            }
            
            @Override
            public void onImageDelete(MultiImageManager.ImageItem item, int position) {
                // 删除图片
                confirmDeleteImage(item, position);
            }
            
            @Override
            public void onImageUploadProgress(int uploadedCount, int totalCount) {
                // 上传进度
                updateUploadProgress(uploadedCount, totalCount);
            }
            
            @Override
            public void onImageUploadComplete(List<String> successUrls, List<String> failureMessages) {
                // 上传完成
                handleUploadComplete(successUrls, failureMessages);
            }
        });
        
        // 加载现有图片
        imageManager.loadImages();
    }
    
    private void openImagePicker() {
        // 打开图片选择器，支持多选
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
        intent.setType("image/*");
        intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true);
        startActivityForResult(intent, REQUEST_CODE_PICK_IMAGES);
    }
    
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        if (requestCode == REQUEST_CODE_PICK_IMAGES && resultCode == RESULT_OK) {
            List<Uri> imageUris = new ArrayList<>();
            
            if (data.getClipData() != null) {
                // 多选
                for (int i = 0; i < data.getClipData().getItemCount(); i++) {
                    imageUris.add(data.getClipData().getItemAt(i).getUri());
                }
            } else if (data.getData() != null) {
                // 单选
                imageUris.add(data.getData());
            }
            
            // 添加图片到管理器
            imageManager.addImages(imageUris);
        }
    }
    
    private void confirmDeleteImage(MultiImageManager.ImageItem item, int position) {
        new AlertDialog.Builder(this)
            .setTitle("删除图片")
            .setMessage("确定要删除这张图片吗？")
            .setPositiveButton("删除", (dialog, which) -> {
                imageManager.deleteImage(item, position);
            })
            .setNegativeButton("取消", null)
            .show();
    }
}
```

#### **2. 布局文件**

```xml
<!-- activity_product_edit.xml -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">
    
    <!-- 其他字段 -->
    
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="产品图片"
        android:textSize="16sp"
        android:textStyle="bold"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="8dp" />
    
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_images"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:nestedScrollingEnabled="false" />
        
</LinearLayout>
```

## 🔧 Repository 层使用

### 通过 Repository 使用（推荐）

```java
@Inject
ImageUploadRepository imageUploadRepository;

// 批量上传
imageUploadRepository.uploadMultipleImages(
    context,
    imageUris,
    ImageUploadUtils.BusinessType.PRODUCT,
    String.valueOf(productId),
    operator,
    callback
);

// 删除图片
imageUploadRepository.deleteImage(
    ImageUploadUtils.BusinessType.PRODUCT,
    String.valueOf(productId),
    operator,
    imageUrl,
    callback
);

// 获取图片列表
imageUploadRepository.getImages(
    ImageUploadUtils.BusinessType.PRODUCT,
    String.valueOf(productId),
    callback
);
```

## 📱 特性说明

### 1. 支持的功能

- ✅ 多图片选择和上传
- ✅ 图片删除
- ✅ 图片预览
- ✅ 上传进度显示
- ✅ 编辑模式控制
- ✅ 最大图片数量限制（默认9张）
- ✅ 错误处理和用户提示

### 2. 支持的业务类型

- `USER` - 用户头像
- `CUSTOMER` - 客户图片
- `PRODUCT` - 产品图片
- `ORDER` - 订单图片
- `COMPONENT` - 部件图片

### 3. 图片格式支持

- JPEG
- PNG
- WebP
- 其他 Android 支持的图片格式

## 🚀 最佳实践

### 1. 错误处理

```java
@Override
public void onUploadComplete(List<String> successUrls, List<String> failureMessages) {
    if (!failureMessages.isEmpty()) {
        // 显示失败信息
        String errorMsg = "部分图片上传失败：\n" + String.join("\n", failureMessages);
        Snackbar.make(recyclerView, errorMsg, Snackbar.LENGTH_LONG).show();
    }
    
    if (!successUrls.isEmpty()) {
        // 显示成功信息
        Snackbar.make(recyclerView, "成功上传 " + successUrls.size() + " 张图片", 
                     Snackbar.LENGTH_SHORT).show();
    }
}
```

### 2. 权限检查

```java
private void checkPermissions() {
    if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE)
            != PackageManager.PERMISSION_GRANTED) {
        ActivityCompat.requestPermissions(this,
                new String[]{Manifest.permission.READ_EXTERNAL_STORAGE},
                REQUEST_CODE_PERMISSION);
    }
}
```

### 3. 内存优化

- 图片自动压缩到合适尺寸
- 使用 Glide 进行图片缓存
- 及时清理临时文件

## 🔄 迁移指南

### 从单图片上传迁移到多图片上传

1. **替换单图片组件**：将单个 ImageView 替换为 RecyclerView
2. **使用 MultiImageManager**：替代手动管理图片逻辑
3. **更新 API 调用**：使用新的批量上传接口
4. **处理回调**：适配新的回调接口

### 向后兼容

- 保留了原有的单图片上传方法
- 现有代码无需修改即可继续使用
- 可以逐步迁移到多图片功能

## 📝 注意事项

1. **服务器支持**：确保服务器端实现了对应的 API 接口
2. **权限申请**：需要申请存储读取权限
3. **网络状态**：建议在网络良好时进行批量上传
4. **用户体验**：提供清晰的上传进度和状态反馈
5. **错误恢复**：支持重试机制和错误恢复

## 🎯 使用场景

- 产品管理：产品多图展示
- 订单管理：订单相关图片
- 客户管理：客户资料图片
- 部件管理：部件多角度图片
- 其他需要多图片管理的业务场景
