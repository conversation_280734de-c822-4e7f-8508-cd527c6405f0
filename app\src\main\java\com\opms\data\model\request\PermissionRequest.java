package com.opms.data.model.request;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * 权限请求模型
 */
public class PermissionRequest {
    @SerializedName("id")
    private int id;

    @SerializedName("name")
    private String name;

    @SerializedName("code")
    private String code;

    @SerializedName("description")
    private String description;

    @SerializedName("status")
    private String status;

    private List<PermissionItemRequest> modules;

    public long getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<PermissionItemRequest> getModules() {
        return modules;
    }

    public void setModules(List<PermissionItemRequest> modules) {
        this.modules = modules;
    }

    @Override
    public String toString() {
        return "PermissionRequest{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", code='" + code + '\'' +
                ", description='" + description + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
