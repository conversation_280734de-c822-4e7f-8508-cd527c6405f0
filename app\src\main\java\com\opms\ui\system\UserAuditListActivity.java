package com.opms.ui.system;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.google.android.material.snackbar.Snackbar;
import com.opms.R;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.UserResponse;
import com.opms.data.repository.UserAuditRepository;
import com.opms.databinding.ActivityUserAuditListBinding;
import com.opms.ui.system.adapter.PendingUserAdapter;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class UserAuditListActivity extends AppCompatActivity
        implements SwipeRefreshLayout.OnRefreshListener, PendingUserAdapter.OnUserClickListener {

    private static final String TAG = "UserAuditList";

    @Inject
    UserAuditRepository userAuditRepository;

    private ActivityUserAuditListBinding binding;
    private PendingUserAdapter adapter;
    private List<UserResponse> allUsers;
    private List<UserResponse> filteredUsers;
    private String currentStatus = "0"; // 默认显示待审核用户
    private String currentKeyword = "";

    private ActivityResultLauncher<Intent> auditActivityLauncher;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityUserAuditListBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setupActivityResultLauncher();
        setupToolbar();
        setupRecyclerView();
        setupSearchAndFilter();
        loadPendingUsers();
    }

    private void setupActivityResultLauncher() {
        auditActivityLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (result.getResultCode() == RESULT_OK) {
                        // 刷新列表
                        loadPendingUsers();
                    }
                }
        );
    }

    private void setupToolbar() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
    }

    private void setupRecyclerView() {
        adapter = new PendingUserAdapter(this);
        adapter.setOnUserClickListener(this);

        binding.rvUsers.setLayoutManager(new LinearLayoutManager(this));
        binding.rvUsers.setAdapter(adapter);

        // 设置下拉刷新
        binding.swipeRefresh.setOnRefreshListener(this);
        binding.swipeRefresh.setColorSchemeResources(R.color.primary);
    }

    private void setupSearchAndFilter() {
        // 搜索功能
        binding.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                currentKeyword = s.toString().trim();
                filterUsers();
            }
        });

        // 状态筛选
        binding.chipGroupStatus.setOnCheckedStateChangeListener((group, checkedIds) -> {
            if (!checkedIds.isEmpty()) {
                int checkedId = checkedIds.get(0);
                if (checkedId == R.id.chip_pending) {
                    currentStatus = "0"; // 待审核
                } else if (checkedId == R.id.chip_approved) {
                    currentStatus = "1"; // 已通过
                } else if (checkedId == R.id.chip_rejected) {
                    currentStatus = "-1"; // 已拒绝
                }
                filterUsers();
            }
        });
    }

    private void loadPendingUsers() {
        binding.swipeRefresh.setRefreshing(true);

        // 使用新的API获取所有审核用户（包括所有状态）
        userAuditRepository.getAllAuditUsers().enqueue(new Callback<ApiResponse<List<UserResponse>>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<List<UserResponse>>> call,
                                   @NonNull Response<ApiResponse<List<UserResponse>>> response) {
                binding.swipeRefresh.setRefreshing(false);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<List<UserResponse>> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        allUsers = apiResponse.getData();
                        filterUsers();
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "获取用户列表失败");
                    }
                } else {
                    showError("获取用户列表失败");
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<List<UserResponse>>> call, @NonNull Throwable t) {
                binding.swipeRefresh.setRefreshing(false);
                Log.e(TAG, "获取用户列表失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    private void filterUsers() {
        if (allUsers == null) {
            return;
        }

        filteredUsers = new ArrayList<>();
        for (UserResponse user : allUsers) {
            // 状态筛选
            if (!currentStatus.equals(user.getStatus())) {
                continue;
            }

            // 关键字搜索
            if (!currentKeyword.isEmpty()) {
                String keyword = currentKeyword.toLowerCase();
                boolean matches = false;

                if (user.getUsername() != null && user.getUsername().toLowerCase().contains(keyword)) {
                    matches = true;
                }
                if (user.getName() != null && user.getName().toLowerCase().contains(keyword)) {
                    matches = true;
                }
                if (user.getPhone() != null && user.getPhone().contains(keyword)) {
                    matches = true;
                }

                if (!matches) {
                    continue;
                }
            }

            filteredUsers.add(user);
        }

        adapter.setUsers(filteredUsers);
        updateEmptyState();
    }

    private void updateEmptyState() {
        if (filteredUsers == null || filteredUsers.isEmpty()) {
            binding.layoutEmpty.setVisibility(View.VISIBLE);
            binding.rvUsers.setVisibility(View.GONE);

            // 根据当前状态更新空状态消息
            String emptyMessage;
            switch (currentStatus) {
                case "0":
                    emptyMessage = "暂无待审核用户";
                    break;
                case "1":
                    emptyMessage = "暂无已通过用户";
                    break;
                case "-1":
                    emptyMessage = "暂无已拒绝用户";
                    break;
                default:
                    emptyMessage = "暂无相关用户";
                    break;
            }
            binding.tvEmptyMessage.setText(emptyMessage);
        } else {
            binding.layoutEmpty.setVisibility(View.GONE);
            binding.rvUsers.setVisibility(View.VISIBLE);
        }
    }

    private void showError(String message) {
        Snackbar.make(binding.getRoot(), message, Snackbar.LENGTH_LONG).show();
    }

    @Override
    public void onRefresh() {
        loadPendingUsers();
    }

    @Override
    public void onUserClick(UserResponse user, int position) {
        // 点击用户项，跳转到审核页面
        startAuditActivity(user);
    }

    @Override
    public void onAuditClick(UserResponse user, int position) {
        // 点击审核按钮，跳转到审核页面
        startAuditActivity(user);
    }

    private void startAuditActivity(UserResponse user) {
        Intent intent = new Intent(this, UserAuditActivity.class);
        intent.putExtra("user_id", user.getId());
        auditActivityLauncher.launch(intent);
        // 添加滑动动画效果
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void finish() {
        super.finish();
        // 添加返回动画
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right);
    }
}
