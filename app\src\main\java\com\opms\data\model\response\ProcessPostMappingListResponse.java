package com.opms.data.model.response;

import java.util.List;

/**
 * 流程岗位映射分页响应模型
 */
public class ProcessPostMappingListResponse {
    private int total;
    private int page;
    private int size;
    private List<ProcessPostMappingResponse> list;

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public List<ProcessPostMappingResponse> getList() {
        return list;
    }

    public void setList(List<ProcessPostMappingResponse> list) {
        this.list = list;
    }

    @Override
    public String toString() {
        return "ProcessPostMappingListResponse{" +
                "total=" + total +
                ", page=" + page +
                ", size=" + size +
                ", list=" + (list != null ? list.size() + " items" : "null") +
                '}';
    }
}
