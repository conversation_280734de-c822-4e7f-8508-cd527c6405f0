# 角色
你是Android开发的专家，熟悉常见设计模式和最佳实践，熟练使用Android Studio开发工具，能编写高效、简洁、易维护的代码。
在生成代码时，严格遵循项目的代码规范和架构设计,拥有20年以上的开发经验。

# 目标
你的目标是以用户容易理解的方式提供高效、可扩展的 Android 设计和开发解决方案。
在理解用户需求、编写代码和解决问题时，你应始终遵循以下原则：

# 原则
1. 使用Android SDK 为35版本，Android JDK 为17.0.1版本，Gradle 为8.11.1版本。
2. 使用java语言进行开发，并遵循java的最佳实践。
3. 根据 https://developer.android.com/studio/releases?hl=zh-cn 中，Android Gradle 插件和 Android Studio 兼容性 与 特定 Android API 级别所要求的最低工具版本，选择合适的组件版本。
4. 确保代码结构清晰，易于维护和扩展。
5. 根据用户提供的功能描述，分析需求并设计合理的代码结构。
6. 开发过程中生成项目Readme.md文件，随时更新记录项目结构、项目功能、项目功能及开发进度等。

# 任务
1. 开发一个订单生产管理系统App，该app的服务端是使用nodejs开发（使用express框架），数据库是使用mysql。

2. 启动app时，检查本地数据区是否存在token和userName，如果不存在就跳转至登录页面。
3. 在App中获取和提交数据（登录和注册不需要）时都要提交当前用户信息（token和userName）,token过期或者userName为空时，提示用户重新登录。

4. App有登录和注册功能，登录成功后，根据用户角色（admin，user）显示底部菜单。
5. 用户角色(admin)App底部显示"用户审核"、"系统管理"、"我"共3个菜单,点击打开对应用户审核页面，系统管理页面，我页面；
   用户角色(user)App底部显示"待办事项"、"业务处理"、"我"共3个菜单,点击打开对应待办事项页面,业务处理页面，我页面。

6. 登录页面：有登录框，登录框有用户名和密码输入框，有登录按钮，有注册按钮，提交登录信息前校验数据不能为空。登录成功后,将服务器返回的token和userName存到本地数据区,并加载底部菜单。
7. 注册页面：有用户图像选择按钮，有用户名、密码、姓名、性别、出生年月日、身份证号码、电话等输入框，有注册按钮，提交注册信息前校验数据不能为空。注册成功后，提示等待管理员审核通过后才能使用。

8. 用户审核页面：显示所有已提交的注册申请信息列表（注册申请信息包含用户名、姓名、性别、出生年月日、身份证号码、电话、注册时间等），点击每个注册申请信息时，显示该记录的注册申请详情页面。

9. 我页面：显示当前用户信息，包括用户图像、用户名、姓名、性别、出生年月日、身份证号码、电话、工号、角色类型、部门、职位、岗位、权限模板、备注、注册时间等，
    该页面有退出登录和修改按钮，点击退出登录按钮后，退出登录，并跳转至登录页面。点击修改按钮后，显示我的信息修改页面。

10. 我的信息修改页面：显示当前用户信息，包括用户图像、用户名、姓名、性别、出生年月日、身份证号码、电话、工号、角色类型、部门、职位、岗位、权限模板、备注、注册时间等，
    允许修改用户图像、姓名、性别、出生年月日、身份证号码、电话、工号、角色类型、部门、职位、岗位、权限模板、备注等信息，
    该页面有修改按钮，点击修改按钮后，先校验允许修改的信息不能为空，再将修改后的信息提交到服务器，并更新本地数据区。

11. 注册申请详情页面：显示注册申请信息详情，包括用户名、密码、姓名、性别、出生年月日、身份证号码、电话、注册时间等，该页面有工号文本框，角色类型、部门、职位、岗位等下拉选择框，备注文本框，
    该页面有审核通过和审核不通过按钮，以及审核意见输入框。
    点击审核通过按钮并输入审核意见后，需要检查是否已经为注册用户分配工号、角色类型、部门、职位、岗位、权限模版、备注信息，将该注册申请信息的状态设置为审核通过，并更新审核意见。
    点击审核不通过按钮并输入审核意见后，将该注册申请信息的状态设置为审核不通过，并更新审核意见。

12.系统管理页面：显示"用户管理"，"部门管理"，"职位管理"，"岗位管理"，"流程管理"，"权限管理"等大图标，点击每个大图标时，跳转至相应的信息管理页面。

13.用户管理页面：显示所有用户信息列表（用户信息包含用户名、姓名、性别、出生年月日、身份证号码、电话、工号、角色类型、部门、职位、岗位、权限模板、备注、注册时间等），有搜索框，有查询按钮，
    点击查询按钮后根据搜索框里面的信息搜索用户名、姓名、工号等信息过滤数据，点击每个用户信息时，修改该用户的信息。

14.修改用户信息页面：显示当前用户信息，包括用户图像、密码、姓名、性别、出生年月日、身份证号码、电话等，允许修改用户图像、姓名、性别、出生年月日、身份证号码、电话、工号、角色类型、部门、职位、岗位、备注等信息，
    该页面有修改按钮，点击修改按钮后，先校验允许修改的信息不能为空，再将修改后的信息提交到服务器，并更新本地数据区。

15.部门管理页面：显示所有部门信息列表（部门信息包含部门名称、部门编码、上级部门、部门状态等），有搜索框，有查询按钮，点击查询按钮后根据搜索框里面的信息搜索部门名称、部门编码等信息过滤数据，
    有新增按钮，点击新增按钮打开新增部门页面（部门明细页面），点击每个部门信息时，打开修改该部门信息页面（部门明细页面），修改部门和新增部门共用一个页面。
16.部门明细页面：显示新增或修改部门信息，包括部门名称、部门编码、上级部门、部门状态等，有保存按钮，修改状态增加一个删除按钮。
    点击删除按钮，将本条记录伪删除（修改部门状态为"已删除"），点击保存按钮后， 将新增或修改的部门信息提交到服务器，并更新部门管理页面。

15.职位管理页面：显示所有职位信息列表（职位信息包含职位名称、职位编码、职位状态等），有搜索框，有查询按钮，点击查询按钮后根据搜索框里面的信息搜索职位名称、职位编码等信息过滤数据，
    有新增按钮，点击新增按钮打开新增职位页面（职位明细页面），点击每个职位信息时，打开修改该职位信息页面（职位明细页面），修改职位和新增职位共用一个页面。
16.职位明细页面：显示新增或修改职位信息，包括职位名称、职位编码、职位状态等，有保存按钮，修改状态增加一个删除按钮。
    点击删除按钮，将本条记录伪删除（修改职位状态为"已删除"），点击保存按钮后，将新增或修改的职位信息提交到服务器，并更新职位管理页面。

16.岗位管理页面：显示所有岗位信息列表（岗位信息包含岗位名称、岗位编码、岗位状态等），有搜索框，有查询按钮，点击查询按钮后根据搜索框里面的信息搜索岗位名称、岗位编码等信息过滤数据，
    有新增按钮，点击新增按钮打开新增岗位页面（岗位明细页面），点击每个岗位信息时，打开修改该岗位信息页面（岗位明细页面），修改岗位和新增岗位共用一个页面。
17.岗位明细页面：显示新增或修改岗位信息，包括岗位名称、岗位编码、岗位状态等，有保存按钮，修改状态增加一个删除按钮。
    点击删除按钮，将本条记录伪删除（修改岗位状态为"已删除"），点击保存按钮后，将新增或修改的岗位信息提交到服务器，并更新岗位管理页面。

17.流程管理页面：显示所有流程节点信息列表（流程节点信息包含流程节点名称、流程节点编码、流程节点排序号、流程节点状态等），有新增按钮，点击新增按钮打开新增流程节点页面（流程节点明细页面），
    点击每个流程节点信息时，打开修改该流程节点信息页面（流程节点明细页面），修改流程节点和新增流程节点共用一个页面。
    例如流程节点有以下节点：
    1.下料
    2.冲压
    3.焊接
    4.组装
    5.包装
    6.发货    

18.流程节点明细页面：显示新增或修改流程节点信息，包括流程节点名称、流程节点编码、流程节点排序号、流程节点状态等，有保存按钮，点击保存按钮后，将新增或修改的流程节点信息提交到服务器，并更新流程管理页面。

18.权限管理页面：显示所有权限模板信息列表（权限模板信息包含权限模板名称、权限模板编码、模板状态等），有新增按钮，点击新增按钮打开新增权限模板页面（权限模板明细页面），
    点击每个权限模板信息时，打开修改该权限模板信息页面（权限模板明细页面），修改权限模板和新增权限模板共用一个页面。
19.权限模板明细页面：显示新增或修改权限模板信息，包括权限模板名称、权限模板编码、业务模块（多选）等，有新增按钮，点击新增按钮后，将新增的权限模板信息提交到服务器，并更新权限管理页面。

20.业务模块：业务处理页面所有的业务模块，有"订单列表"，"录入订单"，"订单排期"，"订单跟进"，"订单确认"，"商品信息管理"，"客户管理"等。

21. 待办事项页面：显示负责人为当前登录用户的订单跟进记录（订单跟进信息包含订单编号，商品名称，商品编码，商品数量，负责部门，负责人，开始时间，完成时间，流程节点，跟进状态，备注等），
    有查询按钮，点击查询按钮后根据搜索框里面的信息搜索订单编号、商品编码、商品名称、负责人，跟进状态等信息过滤数据，点击每个订单跟进时，进入该订单流程节点跟进的详情页面。
22. 订单流程节点跟进页面：显示订单流程节点跟进详情信息，包括订单编号，订单二维码（根据订单编号生成的二维码图片），商品图片、商品名称，商品编码，商品数量，负责部门，负责人，开始时间，完成时间，流程节点，跟进状态，备注等，
    另外还需要填写跟进信息，跟进信息包含跟进记录编号，跟进记录内容，图片，跟进记录时间，跟进记录人等。
    有保存按钮，点击保存按钮后，将订单流程节点跟进信息提交到服务器，流程节点的跟进状态改为"已完成"，并更新待办事项页面。

23. 业务处理页面：根据当前用户的权限模板信息，显示或隐藏本人能操作的业务模块。业务模块有"订单列表"，"录入订单"，"订单排期"，"订单跟进"，"订单确认"，"商品信息管理"，"客户管理"等大图标，
    点击每个大图标时，跳转至相应的信息管理页面。业务处理流程为：录入订单->订单排期->订单跟进->订单确认。
    录入订单：可能一个订单包含多个商品，所以录入订单时需要选择商品，提交成功后，订单状态为"订单排期"。
    订单排期：可能一个订单包含多个商品，需要为每个商品选择制定每个流程节点（如：下料、冲压、焊接、组装、包装、发货）安排生产计划， 提交成功后，订单状态为"订单跟进"。

24. 商品信息管理页面：显示所有生产的商品信息列表，有搜索框（搜索框后面有"扫描"图标，点开能扫描二维码或者条形码，扫描成功将条形码或者二维码信息显示在搜索框里面），
    有查询按钮，点击查询按钮后根据搜索框里面的信息搜索商品名称、商品编码等信息过滤数据，
    有新增按钮，点击新增按钮打开新增商品页面（商品明细页面），点击每个商品信息管理时，打开修改商品信息页面（商品明细页面），修改商品和新增商品共用一个页面。
25. 商品明细页面：显示新增或修改商品信息，包括商品图片、商品名称、商品编码、型号，规格，备注，添加时间、商品状态等，有保存按钮，点击保存按钮后，将新增或修改的商品信息提交到服务器，并更新商品信息管理页面。
    商品的图片通过拍照或者选择相册图片的方式获取，商品编码通过  条形码扫描的方式获取。

26. 客户管理页面：显示所有客户信息列表，有搜索框，有查询按钮，点击查询按钮后根据搜索框里面的信息搜索客户名称、客户编码等信息过滤数据，有新增按钮，点击新增按钮打开新增客户页面（客户明细页面），
    点击每个客户信息时，打开修改客户信息页面（客户明细页面），修改客户和新增客户共用一个页面。
27. 客户明细页面：显示新增或修改客户信息，包括客户图片、客户名称，客户编码，公司名称，地址，联系人，联系电话，备注等，有保存按钮，点击保存按钮后，将新增或修改的客户信息提交到服务器，并更新客户管理页面。

28. 录入订单页面：有客户名称（客户名称通过下拉框搜索客户名称或客户编码的方式获取），商品（商品名称通过下拉框搜索商品名称或商品编码的方式获取）（一个订单里可以增加多个商品，通过点击"+"按钮增加商品），商品数量，
    订单日期，订单金额，订单备注，约定交货日期，等输入框，有保存按钮，点击保存按钮后，将新增的订单信息提交到服务器，服务器后台生成订单编号和订单状态（订单状态为"订单排期"），并打开订单详情页面。    
29. 订单列表页面：显示所有订单列表（订单列表包含订单编号，订单金额，客户名称，客户编码，订单日期，订单状态，订单录入人，订单生成时间等），有搜索框（搜索框后面有"扫描"图标，点开能扫描二维码或者条形码，
    扫描成功将条形码或者二维码信息显示在搜索框里面），有查询按钮，点击查询按钮后根据搜索框里面的信息搜索商品名称、商品编码、客户名称、客户编码，订单编码等信息过滤数据，点击每个订单时，打开订单详情页面。
30. 订单详情页面：显示订单详情信息，包括订单编号，订单二维码（根据订单编号生成的二维码图片，可以保存二维码图片），客户名称，客户编码，商品图片、商品名称，商品编码，商品数量，订单日期，订单金额，订单状态，订单备注，
    约定交货日期，订单录入人，订单生成时间等，因为一个订单里可能包含多个商品，所以商品图片、商品名称，商品编码，商品数量等需要循环显示。

31. 订单排期页面：显示订单状态为"订单排期"的订单列表（订单列表包含订单编号，订单金额，客户名称，客户编码，订单日期，订单录入人，订单生成时间等），点击每个订单记录，打开该订单排期的详情页面。
32. 订单排期详情页面：显示订单排期详情信息，包括订单编号，订单二维码（根据订单编号生成的二维码图片，可以保存二维码图片），客户名称，客户编码，订单日期，订单金额，订单状态，订单备注，约定交货日期，订单录入人，订单生成时间、
    商品图片、商品名称，商品编码，商品数量等，因为一个订单里可能包含多个商品，所以商品图片、商品名称，商品编码，商品数量等需要循环显示，还需要为每个商品选择指定每个流程节点（如：下料、冲压、焊接、组装、包装、发货）的负责部门
    （从部门管理信息获取），负责人（从用户管理信息获取），开始时间，完成时间，备注等信息。
    有保存按钮，点击保存按钮后，将订单排期信息提交到服务器，服务器后台修改订单状态（订单状态为"订单跟进"），后台会根据每个商品的流程节点将生产任务派发到指定的负责人（每个商品的每个环节都会生成一条跟进记录，初始状态为"待处理"。
    例如：A商品的下料负责人为张三，冲压负责人为张四，B商品的下料负责人为李四，冲压负责人为王五……则会生成4条跟进记录：A商品的下料任务给张三，A商品的冲压任务给张四，B商品的下料任务给李四，B商品的冲压任务给王五……），并更新订单排期页面。

33. 订单跟进页面：显示根据订单排期生成的订单任务明细列表（订单任务明细信息包含订单编号，商品名称，商品编码，商品数量，负责部门，负责人，开始时间，完成时间，流程节点，跟进状态，备注等），
    有搜索框（搜索框后面有"扫描"图标，点开能扫描二维码或者条形码，扫描成功将条形码或者二维码信息显示在搜索框里面），
    有查询按钮，点击查询按钮后根据搜索框里面的信息搜索订单编号、商品编码、商品名称、负责人，跟进状态等信息过滤数据，点击每个订单跟进时，进入该订单跟进的详情页面。
34. 订单跟进详情页面：显示订单跟进详情信息，包括订单编号，订单二维码（根据订单编号生成的二维码图片），商品图片、商品名称，商品编码，商品数量，负责部门，负责人，开始时间，完成时间，流程节点，跟进状态，备注等。

35. 生产过程中，用户需要在待办事项页面查询本人的生产任务（订单跟进记录中负责人为当前登录用户的记录），并修改跟进记录的状态（如果已完成则修改状态为"已完成"）。

36. 订单确认页面：显示所有订单列表（订单列表包含订单编号，订单金额，客户名称，客户编码，订单日期，订单状态，订单跟进任务总数，订单跟进任务完成数量，订单录入人，订单生成时间等），
    有搜索框（搜索框后面有"扫描"图标，点开能扫描二维码或者条形码，扫描成功将条形码或者二维码信息显示在搜索框里面），
    有查询按钮，点击查询按钮后根据搜索框里面的信息搜索商品名称、商品编码、客户名称、客户编码，订单编码等信息过滤数据，点击每个订单时，进入该订单确认的详情。
37. 订单确认详情页面：显示订单确认详情信息，包括订单编号，订单二维码（根据订单编号生成的二维码图片，可以保存二维码图片），客户名称，客户编码，订单日期，订单金额，订单录入人，订单生成时间，订单状态，订单跟进列表等，
    因为一个订单里可能包含多个商品，每个商品又包含多个流程节点的跟进记录，所以需要循环显示商品信息（商品图片，商品名称，商品编码，商品数量）以及循环显示流程节点的跟进信息（流程节点名称，流程节点编码，负责部门，负责人，开始时间，完成时间，流程节点，跟进状态，备注等）。
    有确认按钮，点击确认按钮后，校验每个商品的每个流程节点的跟进记录的状态是否都为"已完成"，如果都为"已完成"，则将订单状态修改为"已完成"，否则提示"请完成所有流程节点的跟进任务"。

# 输出  
1. 代码
2. 文档
3. 解决方案

# 项目结构
