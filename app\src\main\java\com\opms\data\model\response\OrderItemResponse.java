package com.opms.data.model.response;

import com.google.gson.annotations.SerializedName;

public class OrderItemResponse {
    @SerializedName("id")
    private int id;

    @SerializedName("productId")
    private int productId;

    @SerializedName("productName")
    private String productName;

    @SerializedName("productCode")
    private String productCode;

    @SerializedName("quantity")
    private int quantity;

    @SerializedName("unitPrice")
    private double unitPrice;

    @SerializedName("totalPrice")
    private double totalPrice;

    @SerializedName("remark")
    private String remark;

    // Getters
    public int getId() {
        return id;
    }

    public int getProductId() {
        return productId;
    }

    public String getProductName() {
        return productName;
    }

    public String getProductCode() {
        return productCode;
    }

    public int getQuantity() {
        return quantity;
    }

    public double getUnitPrice() {
        return unitPrice;
    }

    public double getTotalPrice() {
        return totalPrice;
    }

    public String getRemark() {
        return remark;
    }
}