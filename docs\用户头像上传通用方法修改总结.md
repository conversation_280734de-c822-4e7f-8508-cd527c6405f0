# 用户头像上传通用方法修改总结

## 📋 修改目标

将用户头像更新功能从使用特定的 `api/user/updateAvatar` 接口改为使用通用的图片上传API接口：

```java
@Multipart
@POST("api/image/uploadImage")
Call<ApiResponse<String>> uploadImage(@Part("businessType") RequestBody businessType,
                                      @Part("businessId") RequestBody businessId,
                                      @Part("operator") RequestBody operator,
                                      @Part MultipartBody.Part image);
```

## 🔄 修改内容

### 1. AvatarViewActivity.java 修改

**修改前：**

```java
// 使用特定的用户头像上传方法
private void uploadAvatar(Bitmap bitmap) {
    // 创建临时文件并使用 api/user/updateAvatar 接口
    uploadAvatarDirectly(avatarFile, cachedPath);
}
```

**修改后：**

```java
// 使用通用图片上传方法
private void uploadAvatarWithGenericMethod(Uri imageUri) {
    imageUploadRepository.uploadUserAvatar(
            this,
            imageUri,
            userId,
            operator,
            callback
    );
}
```

### 2. 关键变化

1. **新增依赖注入**：添加 `ImageUploadRepository` 依赖注入
2. **方法调用**：从直接调用 `api/user/updateAvatar` 改为使用
   `ImageUploadRepository.uploadUserAvatar()`
3. **业务类型**：底层自动使用 `ImageUploadUtils.BusinessType.USER`
4. **参数传递**：传入用户ID、操作人和回调接口
5. **API接口**：底层使用通用的 `/api/image/uploadImage` 接口

### 3. 新增方法

#### uploadAvatarWithGenericMethod()

- 获取当前用户ID
- 获取操作人信息
- 调用通用图片上传方法
- 处理上传成功/失败的回调
- 自动缓存头像到本地

#### getCurrentUserIdAsync()

- 异步获取当前用户ID
- 首先尝试从 SharedPreferences 获取缓存的用户ID
- 如果没有缓存，则调用 `getUserProfile()` API 从服务器获取
- 获取成功后自动缓存到 SharedPreferences
- 使用回调接口处理异步结果

#### cacheAvatarBitmap()

- 将Bitmap转换为临时文件
- 使用AvatarCacheUtils.cacheAvatarFile()缓存
- 提供完整的错误处理

### 4. 弃用标记

将以下方法标记为 `@Deprecated`：

- `uploadAvatar(Bitmap bitmap)`
- `uploadAvatarDirectly(File avatarFile, String cachedPath)`
- `uploadAvatarWithRetrofit(File avatarFile, String cachedPath)`

## 🛠️ 技术实现

### API调用链路

```
AvatarViewActivity.uploadAvatarWithGenericMethod()
    ↓
ImageUploadRepository.uploadUserAvatar()
    ↓
ImageUploadUtils.uploadImage()
    ↓
ImageUploadUtils.uploadImageFile()
    ↓
ApiService.uploadImage() // 通用API接口
    ↓
POST /api/image/uploadImage
```

### 请求参数

通用API接口接收以下参数：

- **businessType**: "user" (业务类型)
- **businessId**: "123" (用户ID字符串)
- **operator**: "system" (操作人)
- **image**: MultipartBody.Part (图片文件)

## 📁 修改文件清单

```
app/src/main/java/com/opms/
├── ui/profile/
│   └── AvatarViewActivity.java                      # 主要修改：使用通用图片上传
├── data/remote/
│   └── ApiService.java                              # 标记 updateAvatar 为 @Deprecated
├── data/repository/
│   ├── UserRepository.java                          # 标记 updateAvatar 为 @Deprecated
│   └── UserRepositoryImpl.java                      # 标记 updateAvatar 为 @Deprecated
└── docs/
    └── 用户头像上传通用方法修改总结.md                # 本文档
```

## ✅ 修改优势

### 1. 统一接口

- 所有图片上传使用相同的API接口
- 一致的参数格式和返回值
- 统一的错误处理机制

### 2. 代码复用

- 复用通用图片上传逻辑
- 避免重复的文件处理代码
- 减少维护成本

### 3. 类型安全

- 使用枚举定义业务类型
- 编译时检查，避免运行时错误

### 4. 向后兼容

- 保留原有API接口和方法
- 标记为 @Deprecated 但仍可使用
- 平滑迁移，不影响现有功能

### 5. 统一回调

- 标准化的上传回调接口
- 一致的成功/失败处理
- 详细的日志记录

## 🎯 使用方式

### 新的推荐方式

```java
// 通过 ImageUploadRepository 上传用户头像
imageUploadRepository.uploadUserAvatar(
        context,
        imageUri,
        userId,
        operator,
        new ImageUploadUtils.ImageUploadCallback() {
            @Override
            public void onUploadStart() {
                // 开始上传
            }

            @Override
            public void onUploadSuccess(String imageUrl) {
                // 上传成功
            }

            @Override
            public void onUploadFailure(String errorMessage) {
                // 上传失败
            }

            @Override
            public void onUploadComplete() {
                // 上传完成
            }
        }
);
```

### 旧的方式（已弃用）

```java
// 不推荐使用，但仍然可用
userRepository.updateAvatar(userId, avatarFile);
```

## 🔄 兼容性说明

### 保留的API接口

为了保持向后兼容，保留了原有的用户头像上传API接口：

```java
// 用户头像上传（保留兼容性，但已标记为 @Deprecated）
@Deprecated
@Multipart
@POST("api/user/updateAvatar")
Call<ApiResponse<String>> updateAvatar(@Part("userId") RequestBody userId,
                                       @Part MultipartBody.Part avatar);
```

### 迁移建议

1. 新功能直接使用 `ImageUploadRepository.uploadUserAvatar()`
2. 现有功能可以逐步迁移到新方法
3. 旧方法仍然可用，但建议尽快迁移

## 🔧 问题修复

### 用户ID获取问题

**问题**：原始实现中，登录时只保存了 token、username 和 role，没有保存用户ID，导致 `getCurrentUserId()`
返回 0。

**解决方案**：

1. 修改 `getCurrentUserId()` 为异步方法 `getCurrentUserIdAsync()`
2. 首先尝试从 SharedPreferences 获取缓存的用户ID
3. 如果没有缓存，调用 `getUserProfile()` API 从服务器获取用户信息
4. 获取成功后自动缓存用户ID到 SharedPreferences
5. 使用回调接口处理异步结果

**代码实现**：

```java
private void getCurrentUserIdAsync(UserIdCallback callback) {
    // 首先尝试从缓存获取
    int cachedUserId = sharedPreferences.getInt("userId", 0);
    if (cachedUserId > 0) {
        callback.onUserIdReceived(cachedUserId);
        return;
    }

    // 从服务器获取用户信息
    userRepository.getUserProfile().enqueue(new Callback<>() {
        // 处理响应并缓存用户ID
    });
}
```

## 📝 注意事项

1. **用户ID获取**：现在使用异步方法获取用户ID，确保能正确处理
2. **权限检查**：上传前检查存储权限
3. **网络状态**：处理网络异常情况
4. **本地缓存**：即使上传失败也要缓存到本地
5. **错误处理**：提供友好的错误提示
6. **方法兼容性**：新增了`cacheAvatarBitmap()`方法来处理Bitmap缓存
7. **异步处理**：用户ID获取现在是异步的，需要在回调中处理结果

## 🧪 测试建议

1. **功能测试**：验证头像上传功能正常工作
2. **网络测试**：测试网络异常情况的处理
3. **权限测试**：测试权限被拒绝的情况
4. **兼容性测试**：确保旧方法仍然可用
5. **性能测试**：验证上传性能没有下降
