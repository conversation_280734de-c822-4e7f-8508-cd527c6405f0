package com.opms.data.local.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.opms.data.local.entity.Department;

import java.util.List;

@Dao
public interface DepartmentDao {
    @Insert
    void insert(Department department);

    @Update
    void update(Department department);

    @Delete
    void delete(Department department);

    @Query("SELECT * FROM departments WHERE code = :code LIMIT 1")
    Department findByCode(String code);

    @Query("SELECT * FROM departments WHERE id = :id LIMIT 1")
    Department findById(int id);

    @Query("SELECT * FROM departments")
    List<Department> getAllDepartments();

    @Query("SELECT * FROM departments WHERE parentCode = :parentCode")
    List<Department> getChildDepartments(String parentCode);
} 