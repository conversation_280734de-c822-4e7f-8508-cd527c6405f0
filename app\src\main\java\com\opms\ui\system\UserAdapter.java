package com.opms.ui.system;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.opms.R;
import com.opms.common.utils.AvatarCacheUtils;
import com.opms.data.model.response.UserResponse;

import java.util.ArrayList;
import java.util.List;

public class UserAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    private static final int VIEW_TYPE_ITEM = 0;
    private static final int VIEW_TYPE_LOADING = 1;

    private final List<UserResponse> userList;
    private final Context context;
    private OnUserClickListener listener;
    private boolean isLoadingAdded = false;

    public UserAdapter(Context context) {
        this.context = context;
        this.userList = new ArrayList<>();
    }

    public void setOnUserClickListener(OnUserClickListener listener) {
        this.listener = listener;
    }

    public void setUserList(List<UserResponse> users) {
        this.userList.clear();
        if (users != null) {
            this.userList.addAll(users);
        }
        notifyDataSetChanged();
    }

    public void addUsers(List<UserResponse> users) {
        int startPosition = userList.size();
        if (users != null && !users.isEmpty()) {
            this.userList.addAll(users);
            notifyItemRangeInserted(startPosition, users.size());
        }
    }

    public void addLoadingFooter() {
        if (!isLoadingAdded) {
            isLoadingAdded = true;
            notifyItemInserted(userList.size());
        }
    }

    public void removeLoadingFooter() {
        if (isLoadingAdded && !userList.isEmpty()) {
            isLoadingAdded = false;
            notifyItemRemoved(userList.size());
        }
    }

    public void updateUser(UserResponse updatedUser) {
        for (int i = 0; i < userList.size(); i++) {
            if (userList.get(i).getUsername().equals(updatedUser.getUsername())) {
                userList.set(i, updatedUser);
                notifyItemChanged(i);
                break;
            }
        }
    }

    public void clear() {
        userList.clear();
        notifyDataSetChanged();
    }

    @Override
    public int getItemViewType(int position) {
        return (position == userList.size() && isLoadingAdded) ? VIEW_TYPE_LOADING : VIEW_TYPE_ITEM;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        if (viewType == VIEW_TYPE_ITEM) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_user, parent, false);
            return new UserViewHolder(view);
        } else {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_loading, parent, false);
            return new LoadingViewHolder(view);
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        if (holder instanceof UserViewHolder) {
            UserResponse user = userList.get(position);
            ((UserViewHolder) holder).bind(user);

            holder.itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onUserClick(user);
                }
            });
        } else if (holder instanceof LoadingViewHolder) {
            ((LoadingViewHolder) holder).progressBar.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public int getItemCount() {
        return userList.size() + (isLoadingAdded ? 1 : 0);
    }

    /**
     * 将位图转换为圆形位图
     *
     * @param bitmap 原始位图
     * @return 圆形位图
     */
    private android.graphics.Bitmap getCircleBitmap(android.graphics.Bitmap bitmap) {
        int width = bitmap.getWidth();
        int height = bitmap.getHeight();
        int radius = Math.min(width, height) / 2;

        android.graphics.Bitmap output = android.graphics.Bitmap.createBitmap(
                width, height, android.graphics.Bitmap.Config.ARGB_8888);
        android.graphics.Canvas canvas = new android.graphics.Canvas(output);

        final android.graphics.Paint paint = new android.graphics.Paint();
        final android.graphics.Rect rect = new android.graphics.Rect(0, 0, width, height);

        paint.setAntiAlias(true);
        canvas.drawARGB(0, 0, 0, 0);
        paint.setColor(0xFFFFFFFF);

        // 绘制圆形
        canvas.drawCircle(width / 2f, height / 2f, radius, paint);

        // 设置图像交集模式
        paint.setXfermode(new android.graphics.PorterDuffXfermode(android.graphics.PorterDuff.Mode.SRC_IN));

        // 绘制位图
        canvas.drawBitmap(bitmap, rect, rect, paint);

        return output;
    }

    public interface OnUserClickListener {
        void onUserClick(UserResponse user);
    }

    public static class LoadingViewHolder extends RecyclerView.ViewHolder {
        public final ProgressBar progressBar;

        public LoadingViewHolder(@NonNull View itemView) {
            super(itemView);
            progressBar = itemView.findViewById(R.id.progressBar);
        }
    }

    public class UserViewHolder extends RecyclerView.ViewHolder {
        private final ImageView ivAvatar;
        private final TextView tvUsername;
        private final TextView tvName;
        private final TextView tvEmployeeId;
        private final TextView tvRole;
        private final TextView tvDepartment;
        private final TextView tvPosition;
        private final TextView tvPhone;

        public UserViewHolder(@NonNull View itemView) {
            super(itemView);
            ivAvatar = itemView.findViewById(R.id.ivAvatar);
            tvUsername = itemView.findViewById(R.id.tvUsername);
            tvName = itemView.findViewById(R.id.tvName);
            tvEmployeeId = itemView.findViewById(R.id.tvEmployeeId);
            tvRole = itemView.findViewById(R.id.tvRole);
            tvDepartment = itemView.findViewById(R.id.tvDepartment);
            tvPosition = itemView.findViewById(R.id.tvPosition);
            tvPhone = itemView.findViewById(R.id.tvPhone);
        }

        public void bind(UserResponse user) {
            tvUsername.setText(user.getUsername());
            tvName.setText(user.getName());
            tvEmployeeId.setText(user.getEmployeeId());
            tvRole.setText(user.getRole());

            // 使用格式化的部门和职位显示
            tvDepartment.setText(user.getDepartmentDisplay());
            tvPosition.setText(user.getPositionDisplay());

            tvPhone.setText(user.getPhone());

            // 加载头像
            loadUserAvatar(user.getAvatarUrl());
        }

        private void loadUserAvatar(String avatarUrl) {
            if (avatarUrl == null || avatarUrl.isEmpty()) {
                Log.d("UserAdapter", "Avatar URL is null or empty, using default avatar");
                ivAvatar.setImageResource(R.drawable.ic_person);
                return;
            }

            Log.d("UserAdapter", "Loading user avatar from URL: " + avatarUrl);

            // 直接使用URL加载图像
            String processedImageUrl = com.opms.common.utils.ImageUtils.processImageUrl(avatarUrl);
            Glide.with(context)
                    .load(processedImageUrl)
                    .placeholder(R.drawable.ic_person)
                    .error(R.drawable.ic_person)
                    .circleCrop()
                    .into(ivAvatar);
        }
    }
}
