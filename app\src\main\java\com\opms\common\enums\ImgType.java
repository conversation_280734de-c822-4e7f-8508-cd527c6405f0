package com.opms.common.enums;

public enum ImgType {
    AVATAR("avatar", "图像"),
    ORDER_NUMBER("OrderNumber", "订单编号");

    private final String code;
    private final String name;

    ImgType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        for (ImgType sde : ImgType.values()) {
            if (code.equalsIgnoreCase(sde.getCode())) {
                return sde.getName();
            }
        }
        return null;
    }
}
