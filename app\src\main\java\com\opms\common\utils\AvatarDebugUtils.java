package com.opms.common.utils;

import android.util.Log;

/**
 * 头像调试工具类
 * 用于调试base64图像显示问题
 */
public class AvatarDebugUtils {
    private static final String TAG = "AvatarDebugUtils";

    /**
     * 分析头像数据格式
     */
    public static void analyzeAvatarData(String avatarData, String source) {
        Log.d(TAG, "=== Avatar Data Analysis (" + source + ") ===");

        if (avatarData == null) {
            Log.d(TAG, "Avatar data is NULL");
            return;
        }

        if (avatarData.isEmpty()) {
            Log.d(TAG, "Avatar data is EMPTY");
            return;
        }

        Log.d(TAG, "Data length: " + avatarData.length());
        Log.d(TAG, "First 100 chars: " + (avatarData.length() > 100 ? avatarData.substring(0, 100) + "..." : avatarData));

        // 检查各种格式
        if (avatarData.startsWith("data:image")) {
            Log.d(TAG, "Format: Data URL with data:image prefix");
            int commaIndex = avatarData.indexOf(",");
            if (commaIndex != -1) {
                String mimeType = avatarData.substring(0, commaIndex);
                String base64Data = avatarData.substring(commaIndex + 1);
                Log.d(TAG, "MIME type: " + mimeType);
                Log.d(TAG, "Base64 data length: " + base64Data.length());
                Log.d(TAG, "Base64 preview: " + (base64Data.length() > 50 ? base64Data.substring(0, 50) + "..." : base64Data));
            }
        } else if (avatarData.startsWith("http://") || avatarData.startsWith("https://")) {
            Log.d(TAG, "Format: HTTP URL");
        } else if (avatarData.contains("\\") || avatarData.startsWith("image/")) {
            Log.d(TAG, "Format: File path");
        } else if (avatarData.matches("^[A-Za-z0-9+/=\\s]+$")) {
            Log.d(TAG, "Format: Potential Base64 string (no prefix)");
            // 检查Base64格式
            String cleaned = avatarData.replaceAll("\\s", "");
            Log.d(TAG, "Cleaned length: " + cleaned.length());
            Log.d(TAG, "Length mod 4: " + (cleaned.length() % 4));

            // 检查是否有有效的Base64字符
            boolean hasValidChars = cleaned.matches("^[A-Za-z0-9+/]*={0,2}$");
            Log.d(TAG, "Valid Base64 characters: " + hasValidChars);
        } else {
            Log.d(TAG, "Format: Unknown/Other");
        }

        // 检查是否包含特殊字符
        boolean hasNewlines = avatarData.contains("\n") || avatarData.contains("\r");
        boolean hasSpaces = avatarData.contains(" ");
        boolean hasTabs = avatarData.contains("\t");

        Log.d(TAG, "Contains newlines: " + hasNewlines);
        Log.d(TAG, "Contains spaces: " + hasSpaces);
        Log.d(TAG, "Contains tabs: " + hasTabs);

        // 使用AvatarCacheUtils检测
        boolean isDetectedAsBase64 = AvatarCacheUtils.isBase64Image(avatarData);
        Log.d(TAG, "Detected as Base64 by AvatarCacheUtils: " + isDetectedAsBase64);

        Log.d(TAG, "=== End Analysis ===");
    }

    /**
     * 测试Base64解码
     */
    public static void testBase64Decode(String avatarData, String source) {
        Log.d(TAG, "=== Base64 Decode Test (" + source + ") ===");

        if (avatarData == null || avatarData.isEmpty()) {
            Log.d(TAG, "No data to test");
            return;
        }

        android.graphics.Bitmap bitmap = AvatarCacheUtils.decodeBase64ToBitmap(avatarData);
        if (bitmap != null) {
            Log.d(TAG, "✅ Successfully decoded to bitmap: " + bitmap.getWidth() + "x" + bitmap.getHeight());
            Log.d(TAG, "Bitmap config: " + bitmap.getConfig());
            Log.d(TAG, "Bitmap byte count: " + bitmap.getByteCount());
        } else {
            Log.e(TAG, "❌ Failed to decode to bitmap");
        }

        Log.d(TAG, "=== End Decode Test ===");
    }

    /**
     * 记录头像加载过程
     */
    public static void logAvatarLoadingProcess(String avatarData, String source, String step) {
        Log.d(TAG, "[" + source + "] " + step + " - Data: " +
                (avatarData != null ?
                        (avatarData.length() > 30 ? avatarData.substring(0, 30) + "..." : avatarData) :
                        "null"));
    }

    /**
     * 验证ImageView状态
     */
    public static void verifyImageViewState(android.widget.ImageView imageView, String source) {
        if (imageView == null) {
            Log.e(TAG, "[" + source + "] ImageView is NULL!");
            return;
        }

        Log.d(TAG, "[" + source + "] ImageView state:");
        Log.d(TAG, "  - Visibility: " + getVisibilityString(imageView.getVisibility()));
        Log.d(TAG, "  - Width: " + imageView.getWidth() + ", Height: " + imageView.getHeight());
        Log.d(TAG, "  - Layout Width: " + imageView.getLayoutParams().width + ", Layout Height: " + imageView.getLayoutParams().height);

        android.graphics.drawable.Drawable drawable = imageView.getDrawable();
        if (drawable != null) {
            Log.d(TAG, "  - Drawable: " + drawable.getClass().getSimpleName());
            Log.d(TAG, "  - Drawable bounds: " + drawable.getBounds().toString());
            if (drawable instanceof android.graphics.drawable.BitmapDrawable) {
                android.graphics.Bitmap bitmap = ((android.graphics.drawable.BitmapDrawable) drawable).getBitmap();
                if (bitmap != null) {
                    Log.d(TAG, "  - Bitmap size: " + bitmap.getWidth() + "x" + bitmap.getHeight());
                    Log.d(TAG, "  - Bitmap config: " + bitmap.getConfig());
                    Log.d(TAG, "  - Bitmap recycled: " + bitmap.isRecycled());
                } else {
                    Log.d(TAG, "  - Bitmap is NULL");
                }
            }
        } else {
            Log.d(TAG, "  - Drawable is NULL");
        }
    }

    private static String getVisibilityString(int visibility) {
        switch (visibility) {
            case android.view.View.VISIBLE:
                return "VISIBLE";
            case android.view.View.INVISIBLE:
                return "INVISIBLE";
            case android.view.View.GONE:
                return "GONE";
            default:
                return "UNKNOWN(" + visibility + ")";
        }
    }
}
