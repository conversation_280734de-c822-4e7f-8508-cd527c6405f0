package com.opms.ui.system;

import android.app.AlertDialog;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.google.android.material.snackbar.Snackbar;
import com.opms.R;
import com.opms.data.model.request.PositionRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.PositionResponse;
import com.opms.data.repository.PositionRepository;
import com.opms.databinding.ActivityPositionManagementBinding;
import com.opms.ui.system.adapter.PositionAdapter;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class PositionManagementActivity extends AppCompatActivity
        implements SwipeRefreshLayout.OnRefreshListener, PositionAdapter.OnPositionClickListener {

    private static final String TAG = "PositionManagement";

    @Inject
    PositionRepository positionRepository;

    private ActivityPositionManagementBinding binding;
    private PositionAdapter adapter;
    private List<PositionResponse> allPositions;
    private List<PositionResponse> filteredPositions;
    private boolean isToolbarExpanded = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityPositionManagementBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setupToolbar();
        setupRecyclerView();
        setupButtons();
        loadPositions();
    }

    private void setupToolbar() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("职位管理");
        }
    }

    private void setupRecyclerView() {
        adapter = new PositionAdapter(this);
        adapter.setOnPositionClickListener(this);

        binding.rvPositions.setLayoutManager(new LinearLayoutManager(this));
        binding.rvPositions.setAdapter(adapter);

        binding.swipeRefresh.setOnRefreshListener(this);
        binding.swipeRefresh.setColorSchemeResources(R.color.primary);
    }

    private void setupButtons() {
        // 浮动操作按钮
        binding.fabAdd.setOnClickListener(v -> startAddActivity());

        // 工具栏切换按钮
        binding.btnToggleToolbar.setOnClickListener(v -> toggleToolbar());

        // 快速搜索按钮
        binding.btnToggleSearch.setOnClickListener(v -> {
            if (!isToolbarExpanded) {
                toggleToolbar();
            }
            binding.etSearch.requestFocus();
        });

        // 搜索功能
        setupSearch();
    }

    private void setupSearch() {
        binding.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                filterPositions(s.toString().trim());
            }
        });
    }

    private void filterPositions(String query) {
        if (allPositions == null) return;

        if (query.isEmpty()) {
            filteredPositions = new ArrayList<>(allPositions);
        } else {
            filteredPositions = new ArrayList<>();
            String lowerQuery = query.toLowerCase();
            for (PositionResponse position : allPositions) {
                if (position.getName().toLowerCase().contains(lowerQuery) ||
                        position.getCode().toLowerCase().contains(lowerQuery)) {
                    filteredPositions.add(position);
                }
            }
        }

        adapter.setPositions(filteredPositions);
        updatePositionCount();
    }

    private void loadPositions() {
        showLoading(true);

        positionRepository.getPositions().enqueue(new Callback<ApiResponse<List<PositionResponse>>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<List<PositionResponse>>> call,
                                   @NonNull Response<ApiResponse<List<PositionResponse>>> response) {
                showLoading(false);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<List<PositionResponse>> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        allPositions = apiResponse.getData();
                        filteredPositions = new ArrayList<>(allPositions);
                        adapter.setPositions(filteredPositions);
                        updatePositionCount();
                        hideEmptyView();
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "获取职位列表失败");
                        showEmptyView();
                    }
                } else {
                    showError("获取职位列表失败");
                    showEmptyView();
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<List<PositionResponse>>> call, @NonNull Throwable t) {
                showLoading(false);
                Log.e(TAG, "获取职位列表失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
                showEmptyView();
            }
        });
    }

    private void startAddActivity() {
        Intent intent = new Intent(this, PositionEditActivity.class);
        startActivity(intent);
        // 添加滑动动画效果
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }

    private void showLoading(boolean show) {
        binding.progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
        if (binding.swipeRefresh.isRefreshing() && !show) {
            binding.swipeRefresh.setRefreshing(false);
        }
    }

    private void showEmptyView() {
        binding.llEmpty.setVisibility(View.VISIBLE);
        binding.rvPositions.setVisibility(View.GONE);
    }

    private void hideEmptyView() {
        binding.llEmpty.setVisibility(View.GONE);
        binding.rvPositions.setVisibility(View.VISIBLE);
    }

    private void showError(String message) {
        Snackbar.make(binding.getRoot(), message, Snackbar.LENGTH_SHORT).show();
    }

    private void updatePositionCount() {
        if (allPositions != null) {
            int totalCount = allPositions.size();
            binding.tvPositionCount.setText("共 " + totalCount + " 个职位");
        }
    }

    private void toggleToolbar() {
        if (isToolbarExpanded) {
            collapseToolbar();
        } else {
            expandToolbar();
        }
    }

    private void expandToolbar() {
        isToolbarExpanded = true;
        binding.btnToggleToolbar.setIcon(getDrawable(R.drawable.ic_expand_less));
        binding.btnToggleToolbar.setText("收起");
        binding.cardExpandableTools.setVisibility(View.VISIBLE);
    }

    private void collapseToolbar() {
        isToolbarExpanded = false;
        binding.btnToggleToolbar.setIcon(getDrawable(R.drawable.ic_expand_more));
        binding.btnToggleToolbar.setText("工具");
        binding.cardExpandableTools.setVisibility(View.GONE);
    }

    @Override
    public void onRefresh() {
        loadPositions();
    }

    @Override
    public void onPositionEdit(PositionResponse position, int itemPosition) {
        // 进入编辑模式
        adapter.setEditingPosition(itemPosition);
    }

    @Override
    public void onPositionSave(PositionResponse position, int itemPosition, String newName, boolean newStatus) {
        // 验证名称是否重复
        if (allPositions != null) {
            for (PositionResponse pos : allPositions) {
                if (pos.getId() != position.getId() && newName.equals(pos.getName())) {
                    Snackbar.make(binding.getRoot(), "职位名称已存在", Snackbar.LENGTH_SHORT).show();
                    return;
                }
            }
        }

        // 保存更改
        int id = position.getId();
        PositionRequest request = new PositionRequest();
        request.setId(id);
        request.setName(newName);
        request.setCode(position.getCode()); // 代码不可修改
        request.setStatus(newStatus ? "1" : "0");

        updatePosition(id, request, itemPosition);
    }

    @Override
    public void onPositionCancel(int itemPosition) {
        // 取消编辑模式
        adapter.cancelEditing();
    }

    @Override
    public void onPositionDelete(PositionResponse position) {
        showDeleteConfirmDialog(position);
    }

    private void updatePosition(int positionId, PositionRequest request, int itemPosition) {
        positionRepository.updatePosition(positionId, request)
                .enqueue(new Callback<ApiResponse<PositionResponse>>() {
                    @Override
                    public void onResponse(@NonNull Call<ApiResponse<PositionResponse>> call,
                                           @NonNull Response<ApiResponse<PositionResponse>> response) {
                        if (response.isSuccessful() && response.body() != null) {
                            ApiResponse<PositionResponse> apiResponse = response.body();
                            if (apiResponse.isSuccess()) {
                                Snackbar.make(binding.getRoot(), "职位更新成功", Snackbar.LENGTH_SHORT).show();

                                // 更新本地数据
                                PositionResponse updatedPosition = apiResponse.getData();
                                if (updatedPosition != null && allPositions != null) {
                                    for (int i = 0; i < allPositions.size(); i++) {
                                        if (allPositions.get(i).getId() == positionId) {
                                            allPositions.set(i, updatedPosition);
                                            break;
                                        }
                                    }
                                    // 更新过滤后的列表
                                    filterPositions(binding.etSearch.getText().toString().trim());
                                }

                                // 退出编辑模式
                                adapter.cancelEditing();
                            } else {
                                showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "更新失败");
                            }
                        } else {
                            showError("更新失败");
                        }
                    }

                    @Override
                    public void onFailure(@NonNull Call<ApiResponse<PositionResponse>> call, @NonNull Throwable t) {
                        Log.e(TAG, "更新职位失败: " + t.getMessage(), t);
                        showError("网络错误: " + t.getMessage());
                    }
                });
    }

    private void showDeleteConfirmDialog(PositionResponse position) {
        new AlertDialog.Builder(this)
                .setTitle("删除职位")
                .setMessage("确定要删除职位 \"" + position.getName() + "\" 吗？")
                .setPositiveButton("删除", (dialog, which) -> deletePosition(position))
                .setNegativeButton("取消", null)
                .show();
    }

    private void deletePosition(PositionResponse position) {
        int id = (int) position.getId();
        PositionRequest request = new PositionRequest();
        request.setId(id);
        positionRepository.deletePosition(request)
                .enqueue(new Callback<ApiResponse<Void>>() {
                    @Override
                    public void onResponse(@NonNull Call<ApiResponse<Void>> call,
                                           @NonNull Response<ApiResponse<Void>> response) {
                        if (response.isSuccessful() && response.body() != null) {
                            ApiResponse<Void> apiResponse = response.body();
                            if (apiResponse.isSuccess()) {
                                Snackbar.make(binding.getRoot(), "职位删除成功", Snackbar.LENGTH_SHORT).show();
                                loadPositions(); // 重新加载数据
                            } else {
                                showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "删除失败");
                            }
                        } else {
                            showError("删除失败");
                        }
                    }

                    @Override
                    public void onFailure(@NonNull Call<ApiResponse<Void>> call, @NonNull Throwable t) {
                        Log.e(TAG, "删除职位失败: " + t.getMessage(), t);
                        showError("网络错误: " + t.getMessage());
                    }
                });
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 从编辑页面返回时刷新数据
        loadPositions();
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void finish() {
        super.finish();
        // 添加返回动画
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right);
    }
}
