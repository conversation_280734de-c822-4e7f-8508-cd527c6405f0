package com.opms.data.remote;

import com.opms.data.local.entity.Permission;
import java.util.List;
import io.reactivex.rxjava3.core.Single;
import retrofit2.http.Body;
import retrofit2.http.DELETE;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.PUT;
import retrofit2.http.Path;

public interface PermissionApi {
    @GET("permissions")
    Single<List<Permission>> getAllPermissions();

    @GET("permissions/{id}")
    Single<Permission> getPermissionById(@Path("id") int id);

    @GET("permissions/code/{code}")
    Single<Permission> getPermissionByCode(@Path("code") String code);

    @POST("permissions")
    Single<Permission> createPermission(@Body Permission permission);

    @PUT("permissions/{id}")
    Single<Permission> updatePermission(@Body Permission permission);

    @DELETE("permissions/{id}")
    Single<Void> deletePermission(@Path("id") int id);

    @GET("permissions/template/{templateId}")
    Single<List<Permission>> getPermissionsByTemplateId(@Path("templateId") int templateId);
} 