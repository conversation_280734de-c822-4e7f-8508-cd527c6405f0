package com.opms.data.repository;

import com.opms.data.model.request.ProcessPostMappingRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.ProcessPostMappingListResponse;
import com.opms.data.model.response.ProcessPostMappingResponse;
import com.opms.data.remote.ApiService;

import javax.inject.Inject;
import javax.inject.Singleton;

import retrofit2.Call;

/**
 * 流程岗位映射仓库实现
 */
@Singleton
public class ProcessPostMappingRepositoryImpl implements ProcessPostMappingRepository {
    private final ApiService api;

    @Inject
    public ProcessPostMappingRepositoryImpl(ApiService apiService) {
        this.api = apiService;
    }

    @Override
    public Call<ApiResponse<ProcessPostMappingListResponse>> getProcessPostMappings(int page, int size, String keyword) {
        return api.getProcessPostMappings(page, size, keyword);
    }

    @Override
    public Call<ApiResponse<ProcessPostMappingResponse>> createProcessPostMapping(ProcessPostMappingRequest request) {
        return api.createProcessPostMapping(request);
    }

    @Override
    public Call<ApiResponse<ProcessPostMappingResponse>> updateProcessPostMapping(int id, ProcessPostMappingRequest request) {
        return api.updateProcessPostMapping(id, request);
    }

    @Override
    public Call<ApiResponse<Void>> deleteProcessPostMapping(ProcessPostMappingRequest request) {
        return api.deleteProcessPostMapping(request);
    }
}
