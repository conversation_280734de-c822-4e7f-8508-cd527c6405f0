package com.opms.data.repository;

import com.opms.data.model.request.ProcessTemplateRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.ProcessTemplateResponse;

import java.util.List;

import retrofit2.Call;

/**
 * 流程模板仓库接口
 */
public interface ProcessTemplateRepository {
    /**
     * 获取所有流程模板
     */
    Call<ApiResponse<List<ProcessTemplateResponse>>> getProcessTemplates();

    /**
     * 创建流程模板
     */
    Call<ApiResponse<ProcessTemplateResponse>> createProcessTemplate(ProcessTemplateRequest request);

    /**
     * 更新流程模板
     */
    Call<ApiResponse<ProcessTemplateResponse>> updateProcessTemplate(int id, ProcessTemplateRequest request);

    /**
     * 删除流程模板
     */
    Call<ApiResponse<Void>> deleteProcessTemplate(ProcessTemplateRequest request);
}
