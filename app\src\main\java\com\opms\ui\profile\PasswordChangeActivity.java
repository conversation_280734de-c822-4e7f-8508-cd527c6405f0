package com.opms.ui.profile;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;

import com.opms.R;
import com.opms.common.utils.AvatarCacheUtils;
import com.opms.data.local.PreferencesManager;
import com.opms.data.model.request.ChangePasswordRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.repository.UserRepository;
import com.opms.databinding.ActivityPasswordChangeBinding;
import com.opms.ui.login.LoginActivity;
import com.opms.utils.MD5Utils;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class PasswordChangeActivity extends AppCompatActivity {
    private static final String TAG = "PasswordChangeActivity";
    @Inject
    UserRepository userRepository;
    private ActivityPasswordChangeBinding binding;
    // 标记密码是否已修改成功
    private boolean passwordChanged = false;

    public static Intent createIntent(Context context) {
        return new Intent(context, PasswordChangeActivity.class);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityPasswordChangeBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // 设置返回按钮
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("修改密码");
        }

        setupButtons();
    }

    private void setupButtons() {
        // 设置保存按钮
        binding.btnSave.setOnClickListener(v -> changePassword());

        // 设置取消按钮
        binding.btnCancel.setOnClickListener(v -> finish());

        // 添加文本变化监听器，当输入框内容变化时清除错误提示
        setupTextChangeListeners();
    }

    private void setupTextChangeListeners() {
        // 为旧密码输入框添加文本变化监听器
        binding.etOldPassword.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                // 当文本变化时，清除错误提示
                binding.tilOldPassword.setError(null);
            }

            @Override
            public void afterTextChanged(Editable s) {
                // 获取旧密码和新密码
                String oldPassword = s.toString().trim();
                String newPassword = binding.etNewPassword.getText().toString().trim();

                // 如果新密码已经有内容，验证新密码是否与旧密码相同
                if (!TextUtils.isEmpty(newPassword) && !TextUtils.isEmpty(oldPassword)) {
                    if (newPassword.equals(oldPassword)) {
                        binding.tilNewPassword.setError("新密码不能与旧密码相同");
                    } else {
                        binding.tilNewPassword.setError(null);
                    }
                }
            }
        });

        // 为新密码输入框添加文本变化监听器
        binding.etNewPassword.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                // 当文本变化时，清除错误提示
                binding.tilNewPassword.setError(null);
                // 同时清除确认密码的错误提示，因为新密码变了，之前的比较结果已经不再有效
                binding.tilConfirmPassword.setError(null);
            }

            @Override
            public void afterTextChanged(Editable s) {
                // 获取新密码和其他相关密码
                String newPassword = s.toString().trim();
                String oldPassword = binding.etOldPassword.getText().toString().trim();
                String confirmPassword = binding.etConfirmPassword.getText().toString().trim();

                // 验证新密码是否与旧密码相同
                if (!TextUtils.isEmpty(newPassword) && !TextUtils.isEmpty(oldPassword)) {
                    if (newPassword.equals(oldPassword)) {
                        binding.tilNewPassword.setError("新密码不能与旧密码相同");
                    } else {
                        binding.tilNewPassword.setError(null);
                    }
                }

                // 如果确认密码已经有内容，实时验证两次密码是否一致
                if (!TextUtils.isEmpty(confirmPassword)) {
                    if (!confirmPassword.equals(newPassword)) {
                        binding.tilConfirmPassword.setError("两次输入的密码不一致");
                    } else {
                        binding.tilConfirmPassword.setError(null);
                    }
                }
            }
        });

        // 为确认密码输入框添加文本变化监听器
        binding.etConfirmPassword.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                // 当文本变化时，清除错误提示
                binding.tilConfirmPassword.setError(null);
            }

            @Override
            public void afterTextChanged(Editable s) {
                // 实时验证两次密码是否一致
                String newPassword = binding.etNewPassword.getText().toString().trim();
                String confirmPassword = s.toString().trim();

                // 只有当两个输入框都不为空时才进行比较
                if (!TextUtils.isEmpty(newPassword) && !TextUtils.isEmpty(confirmPassword)) {
                    if (!confirmPassword.equals(newPassword)) {
                        binding.tilConfirmPassword.setError("两次输入的密码不一致");
                    } else {
                        binding.tilConfirmPassword.setError(null);
                    }
                }
            }
        });
    }

    private void changePassword() {
        // 验证输入
        if (!validateInput()) {
            return;
        }

        // 显示进度条
        binding.progressBar.setVisibility(View.VISIBLE);

        // 准备请求数据
        String oldPassword = binding.etOldPassword.getText().toString().trim();
        String newPassword = binding.etNewPassword.getText().toString().trim();
        ChangePasswordRequest request = new ChangePasswordRequest(MD5Utils.md5(oldPassword), MD5Utils.md5(newPassword));

        // 发送修改密码请求
        userRepository.changePassword(request).enqueue(new Callback<ApiResponse<Void>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<Void>> call, @NonNull Response<ApiResponse<Void>> response) {
                binding.progressBar.setVisibility(View.GONE);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<Void> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        // 设置密码已修改标记
                        passwordChanged = true;
                        // 显示对话框提示用户需要重新登录
                        showReloginDialog();
                    } else {
                        Toast.makeText(PasswordChangeActivity.this,
                                apiResponse.getMessage() != null ? apiResponse.getMessage() : "密码修改失败",
                                Toast.LENGTH_SHORT).show();
                    }
                } else {
                    Toast.makeText(PasswordChangeActivity.this, "密码修改失败", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<Void>> call, @NonNull Throwable t) {
                binding.progressBar.setVisibility(View.GONE);
                Log.e(TAG, "修改密码失败: " + t.getMessage(), t);
                Toast.makeText(PasswordChangeActivity.this, "网络错误: " + t.getMessage(), Toast.LENGTH_SHORT).show();
            }
        });
    }

    private boolean validateInput() {
        boolean isValid = true;

        // 验证旧密码
        String oldPassword = binding.etOldPassword.getText().toString().trim();
        if (TextUtils.isEmpty(oldPassword)) {
            binding.tilOldPassword.setError("旧密码不能为空");
            isValid = false;
        } else {
            binding.tilOldPassword.setError(null);
        }

        // 验证新密码
        String newPassword = binding.etNewPassword.getText().toString().trim();
        if (TextUtils.isEmpty(newPassword)) {
            binding.tilNewPassword.setError("新密码不能为空");
            isValid = false;
        } else if (oldPassword.equals(newPassword)) {
            binding.tilNewPassword.setError("新密码不能与旧密码一样");
            isValid = false;
        } else {
            binding.tilNewPassword.setError(null);
        }

        // 验证确认密码
        String confirmPassword = binding.etConfirmPassword.getText().toString().trim();
        if (TextUtils.isEmpty(confirmPassword)) {
            binding.tilConfirmPassword.setError("确认密码不能为空");
            isValid = false;
        } else if (!confirmPassword.equals(newPassword)) {
            binding.tilConfirmPassword.setError("两次输入的密码不一致");
            isValid = false;
        } else {
            binding.tilConfirmPassword.setError(null);
        }

        return isValid;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onBackPressed() {
        // 如果密码已修改成功，不允许通过返回键返回
        if (passwordChanged) {
            // 不执行任何操作，防止用户通过返回键绕过重新登录
            return;
        }
        super.onBackPressed();
    }

    /**
     * 显示重新登录对话框
     */
    private void showReloginDialog() {
        new AlertDialog.Builder(this)
                .setTitle("密码修改成功")
                .setMessage("您的密码已成功修改，请重新登录。")
                .setPositiveButton("确定", (dialog, which) -> {
                    // 清除用户登录状态
                    clearUserLoginState();

                    // 跳转到登录页面
                    Intent intent = new Intent(this, LoginActivity.class);
                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                    startActivity(intent);
                    finish();
                })
                .setCancelable(false) // 不允许通过按返回键取消对话框
                .show();
    }

    /**
     * 清除用户登录状态
     */
    private void clearUserLoginState() {
        // 注入的PreferencesManager可能为null，因为我们在对话框回调中使用它
        // 所以这里直接获取实例
        PreferencesManager preferencesManager = new PreferencesManager(this);
        preferencesManager.clearUserData();

        // 清除缓存的头像
        AvatarCacheUtils.clearCachedAvatar(this);
    }

    @Override
    public void finish() {
        super.finish();
        // 添加返回动画
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right);
    }
}
