package com.opms.ui.system;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 分页滚动监听器
 * 用于实现RecyclerView的分页加载功能
 */
public abstract class PaginationScrollListener extends RecyclerView.OnScrollListener {
    private final LinearLayoutManager layoutManager;

    /**
     * 构造函数
     *
     * @param layoutManager LinearLayoutManager
     */
    public PaginationScrollListener(LinearLayoutManager layoutManager) {
        this.layoutManager = layoutManager;
    }

    @Override
    public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
        super.onScrolled(recyclerView, dx, dy);

        int visibleItemCount = layoutManager.getChildCount();
        int totalItemCount = layoutManager.getItemCount();
        int firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition();

        if (!isLoading() && !isLastPage()) {
            if ((visibleItemCount + firstVisibleItemPosition) >= totalItemCount
                    && firstVisibleItemPosition >= 0) {
                loadMoreItems();
            }
        }
    }

    /**
     * 加载更多数据
     */
    protected abstract void loadMoreItems();

    /**
     * 是否正在加载
     *
     * @return 是否正在加载
     */
    public abstract boolean isLoading();

    /**
     * 是否是最后一页
     *
     * @return 是否是最后一页
     */
    public abstract boolean isLastPage();
}
