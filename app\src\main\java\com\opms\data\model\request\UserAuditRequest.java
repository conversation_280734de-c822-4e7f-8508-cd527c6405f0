package com.opms.data.model.request;

import com.google.gson.annotations.SerializedName;

/**
 * 用户审核请求模型
 */
public class UserAuditRequest {
    @SerializedName("id")
    private int id;

    @SerializedName("status")
    private String status; // "1"=通过, "-1"=拒绝, "0"=待审核

    @SerializedName("remark")
    private String remark;

    @SerializedName("employeeId")
    private String employeeId;

    @SerializedName("role")
    private String role;

    @SerializedName("department")
    private String department;

    @SerializedName("position")
    private String position;

    @SerializedName("job")
    private String job;

    @SerializedName("permissionTemplate")
    private String permissionTemplate;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(String employeeId) {
        this.employeeId = employeeId;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getJob() {
        return job;
    }

    public void setJob(String job) {
        this.job = job;
    }

    public String getPermissionTemplate() {
        return permissionTemplate;
    }

    public void setPermissionTemplate(String permissionTemplate) {
        this.permissionTemplate = permissionTemplate;
    }

    @Override
    public String toString() {
        return "UserAuditRequest{" +
                "id=" + id +
                ", status='" + status + '\'' +
                ", remark='" + remark + '\'' +
                ", employeeId='" + employeeId + '\'' +
                ", role='" + role + '\'' +
                ", department=" + department +
                ", position=" + position +
                ", job=" + job +
                ", permissionTemplate=" + permissionTemplate +
                '}';
    }
}
