package com.opms.data.model.response;

import com.google.gson.annotations.SerializedName;

/**
 * 流程岗位映射响应模型
 */
public class ProcessPostMappingResponse {
    @SerializedName("id")
    private int id;

    @SerializedName("processId")
    private int processId;

    @SerializedName("processName")
    private String processName;

    @SerializedName("processCode")
    private String processCode;

    @SerializedName("processStatus")
    private String processStatus;

    @SerializedName("postId")
    private int postId;

    @SerializedName("postName")
    private String postName;

    @SerializedName("postCode")
    private String postCode;

    @SerializedName("postStatus")
    private String postStatus;

    @SerializedName("status")
    private String status;

    @SerializedName("remark")
    private String remark;

    @SerializedName("createTime")
    private String createTime;

    @SerializedName("updateTime")
    private String updateTime;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getProcessId() {
        return processId;
    }

    public void setProcessId(int processId) {
        this.processId = processId;
    }

    public String getProcessName() {
        return processName;
    }

    public void setProcessName(String processName) {
        this.processName = processName;
    }

    public String getProcessCode() {
        return processCode;
    }

    public void setProcessCode(String processCode) {
        this.processCode = processCode;
    }

    public int getPostId() {
        return postId;
    }

    public void setPostId(int postId) {
        this.postId = postId;
    }

    public String getPostName() {
        return postName;
    }

    public void setPostName(String postName) {
        this.postName = postName;
    }

    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    public String getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(String processStatus) {
        this.processStatus = processStatus;
    }

    public String getPostStatus() {
        return postStatus;
    }

    public void setPostStatus(String postStatus) {
        this.postStatus = postStatus;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "ProcessPostMappingResponse{" +
                "id=" + id +
                ", processId=" + processId +
                ", processName='" + processName + '\'' +
                ", processCode='" + processCode + '\'' +
                ", processStatus='" + processStatus + '\'' +
                ", postId=" + postId +
                ", postName='" + postName + '\'' +
                ", postCode='" + postCode + '\'' +
                ", postStatus='" + postStatus + '\'' +
                ", status='" + status + '\'' +
                ", remark='" + remark + '\'' +
                ", createTime='" + createTime + '\'' +
                ", updateTime='" + updateTime + '\'' +
                '}';
    }
}
