<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_component" modulePackage="com.opms" filePath="app\src\main\res\layout\item_component.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView" rootNodeViewId="@+id/card_component"><Targets><Target id="@+id/card_component" tag="layout/item_component_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="128" endOffset="51"/></Target><Target id="@+id/tv_component_name" view="TextView"><Expressions/><location startLine="27" startOffset="12" endLine="36" endOffset="42"/></Target><Target id="@+id/tv_component_model" view="TextView"><Expressions/><location startLine="39" startOffset="12" endLine="48" endOffset="41"/></Target><Target id="@+id/tv_component_remark" view="TextView"><Expressions/><location startLine="51" startOffset="12" endLine="60" endOffset="41"/></Target><Target id="@+id/tv_component_code" view="TextView"><Expressions/><location startLine="73" startOffset="12" endLine="82" endOffset="41"/></Target><Target id="@+id/tv_component_standard" view="TextView"><Expressions/><location startLine="85" startOffset="12" endLine="93" endOffset="41"/></Target><Target id="@+id/btn_edit" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="103" startOffset="16" endLine="110" endOffset="50"/></Target><Target id="@+id/btn_delete" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="112" startOffset="16" endLine="120" endOffset="49"/></Target></Targets></Layout>