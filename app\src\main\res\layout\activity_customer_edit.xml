<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="16dp">

            <!-- 客户图片 -->
            <LinearLayout
                android:id="@+id/ll_image_container"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:gravity="center"
                android:orientation="vertical"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_image"
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    app:cardCornerRadius="60dp"
                    app:cardElevation="4dp">

                    <ImageView
                        android:id="@+id/iv_customer_image"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="centerCrop"
                        android:src="@drawable/ic_customer_management" />

                    <View
                        android:id="@+id/view_image_overlay"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/bg_image_overlay"
                        android:clickable="true"
                        android:focusable="true" />

                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:id="@+id/tv_image_hint"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="编辑时点击可更换图片"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:visibility="gone" />

            </LinearLayout>

            <!-- 客户名称 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_customer_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="客户名称 *"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ll_image_container">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_customer_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 客户编码 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_customer_code"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="客户编码 *"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/til_customer_name">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_customer_code"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 公司名称 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_company_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="公司名称 *"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/til_customer_code">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_company_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 地址 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_address"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="地址"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/til_company_name">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_address"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textMultiLine"
                    android:maxLines="3" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 联系人 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_contact"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_marginBottom="16dp"
                android:hint="联系人 *"
                app:layout_constraintEnd_toStartOf="@+id/til_phone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/til_address">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_contact"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPersonName"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 联系电话 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_phone"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginBottom="16dp"
                android:hint="联系电话 *"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/til_contact"
                app:layout_constraintTop_toBottomOf="@+id/til_address">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_phone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="phone"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 状态 -->
            <LinearLayout
                android:id="@+id/ll_status"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:orientation="vertical"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/til_contact">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="状态"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp" />

                <Spinner
                    android:id="@+id/spinner_status"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp" />

            </LinearLayout>

            <!-- 备注 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_remark"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="32dp"
                android:hint="备注"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ll_status">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_remark"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textMultiLine"
                    android:maxLines="4" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 操作按钮 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:orientation="horizontal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/til_remark">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_cancel"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:layout_weight="1"
                    android:text="取消" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_save"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1"
                    android:text="保存" />

            </LinearLayout>

            <!-- 加载指示器 -->
            <ProgressBar
                android:id="@+id/progress_bar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
