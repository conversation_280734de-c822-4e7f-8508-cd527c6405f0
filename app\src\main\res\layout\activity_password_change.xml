<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <!-- 密码修改卡片 -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/cardPasswordChange"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp"
        app:contentPadding="16dp"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="修改密码"
                android:textColor="@color/primary"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- 旧密码 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/tilOldPassword"
                style="@style/Widget.OPMS.TextInputLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="旧密码"
                app:endIconMode="password_toggle">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etOldPassword"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPassword" />
            </com.google.android.material.textfield.TextInputLayout>

            <!-- 新密码 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/tilNewPassword"
                style="@style/Widget.OPMS.TextInputLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:hint="新密码"
                app:endIconMode="password_toggle">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etNewPassword"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPassword" />
            </com.google.android.material.textfield.TextInputLayout>

            <!-- 确认新密码 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/tilConfirmPassword"
                style="@style/Widget.OPMS.TextInputLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:hint="确认新密码"
                app:endIconMode="password_toggle">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etConfirmPassword"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPassword" />
            </com.google.android.material.textfield.TextInputLayout>
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- 按钮区域 -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnSave"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="8dp"
        android:text="保存"
        app:layout_constraintEnd_toStartOf="@id/btnCancel"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cardPasswordChange" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnCancel"
        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:text="取消"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btnSave"
        app:layout_constraintTop_toTopOf="@id/btnSave" />

    <!-- 进度条 -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
