package com.opms.utils;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.opms.R;

/**
 * 动画工具类
 * 统一管理应用中的页面切换动画
 */
public class AnimationUtils {

    /**
     * 启动Activity并应用动画
     *
     * @param context       上下文
     * @param intent        Intent对象
     * @param animationType 动画类型
     */
    public static void startActivityWithAnimation(Context context, Intent intent, AnimationType animationType) {
        if (context instanceof Activity) {
            Activity activity = (Activity) context;
            activity.startActivity(intent);
            applyActivityAnimation(activity, animationType, true);
        }
    }

    /**
     * 结束Activity并应用动画
     *
     * @param activity      Activity对象
     * @param animationType 动画类型
     */
    public static void finishActivityWithAnimation(Activity activity, AnimationType animationType) {
        activity.finish();
        applyActivityAnimation(activity, animationType, false);
    }

    /**
     * 应用Activity动画
     *
     * @param activity      Activity对象
     * @param animationType 动画类型
     * @param isEnter       是否为进入动画
     */
    private static void applyActivityAnimation(Activity activity, AnimationType animationType, boolean isEnter) {
        switch (animationType) {
            case SLIDE_RIGHT:
                if (isEnter) {
                    activity.overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
                } else {
                    activity.overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right);
                }
                break;
            case SLIDE_LEFT:
                if (isEnter) {
                    activity.overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right);
                } else {
                    activity.overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
                }
                break;
            case FADE:
                activity.overridePendingTransition(R.anim.fade_in, R.anim.fade_out);
                break;
            case NONE:
            default:
                // 不应用动画
                break;
        }
    }

    /**
     * 替换Fragment并应用动画
     *
     * @param fragmentManager FragmentManager对象
     * @param containerId     容器ID
     * @param fragment        要显示的Fragment
     * @param animationType   动画类型
     */
    public static void replaceFragmentWithAnimation(FragmentManager fragmentManager, int containerId,
                                                    Fragment fragment, AnimationType animationType) {
        FragmentTransaction transaction = fragmentManager.beginTransaction();

        // 获取当前Fragment
        Fragment currentFragment = fragmentManager.findFragmentById(containerId);

        // 应用动画
        if (currentFragment == null) {
            // 首次加载，使用淡入动画
            transaction.setCustomAnimations(R.anim.fade_in, R.anim.fade_out);
        } else {
            applyFragmentAnimation(transaction, animationType);
        }

        transaction.replace(containerId, fragment)
                .setTransition(FragmentTransaction.TRANSIT_FRAGMENT_FADE)
                .commit();
    }

    /**
     * 应用Fragment动画
     *
     * @param transaction   FragmentTransaction对象
     * @param animationType 动画类型
     */
    private static void applyFragmentAnimation(FragmentTransaction transaction, AnimationType animationType) {
        switch (animationType) {
            case SLIDE_RIGHT:
                transaction.setCustomAnimations(
                        R.anim.slide_in_right,
                        R.anim.slide_out_left,
                        R.anim.slide_in_left,
                        R.anim.slide_out_right
                );
                break;
            case SLIDE_LEFT:
                transaction.setCustomAnimations(
                        R.anim.slide_in_left,
                        R.anim.slide_out_right,
                        R.anim.slide_in_right,
                        R.anim.slide_out_left
                );
                break;
            case FADE:
                transaction.setCustomAnimations(R.anim.fade_in, R.anim.fade_out);
                break;
            case NONE:
            default:
                // 不应用动画
                break;
        }
    }

    /**
     * 为底部导航切换应用动画
     *
     * @param fragmentManager FragmentManager对象
     * @param containerId     容器ID
     * @param fragment        要显示的Fragment
     */
    public static void replaceFragmentForBottomNavigation(FragmentManager fragmentManager, int containerId, Fragment fragment) {
        replaceFragmentWithAnimation(fragmentManager, containerId, fragment, AnimationType.SLIDE_RIGHT);
    }

    /**
     * 启动编辑页面
     *
     * @param context 上下文
     * @param intent  Intent对象
     */
    public static void startEditActivity(Context context, Intent intent) {
        startActivityWithAnimation(context, intent, AnimationType.SLIDE_RIGHT);
    }

    /**
     * 启动详情页面
     *
     * @param context 上下文
     * @param intent  Intent对象
     */
    public static void startDetailActivity(Context context, Intent intent) {
        startActivityWithAnimation(context, intent, AnimationType.SLIDE_RIGHT);
    }

    /**
     * 启动登录相关页面
     *
     * @param context 上下文
     * @param intent  Intent对象
     */
    public static void startAuthActivity(Context context, Intent intent) {
        startActivityWithAnimation(context, intent, AnimationType.FADE);
    }

    /**
     * 结束编辑页面
     *
     * @param activity Activity对象
     */
    public static void finishEditActivity(Activity activity) {
        finishActivityWithAnimation(activity, AnimationType.SLIDE_RIGHT);
    }

    /**
     * 结束详情页面
     *
     * @param activity Activity对象
     */
    public static void finishDetailActivity(Activity activity) {
        finishActivityWithAnimation(activity, AnimationType.SLIDE_RIGHT);
    }

    /**
     * 结束登录相关页面
     *
     * @param activity Activity对象
     */
    public static void finishAuthActivity(Activity activity) {
        finishActivityWithAnimation(activity, AnimationType.FADE);
    }

    /**
     * 动画类型枚举
     */
    public enum AnimationType {
        SLIDE_RIGHT,    // 从右侧滑入
        SLIDE_LEFT,     // 从左侧滑入
        FADE,           // 淡入淡出
        NONE            // 无动画
    }
}
