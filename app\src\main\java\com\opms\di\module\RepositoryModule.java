package com.opms.di.module;

import com.opms.data.repository.ComponentRepository;
import com.opms.data.repository.ComponentRepositoryImpl;
import com.opms.data.repository.CustomerRepository;
import com.opms.data.repository.CustomerRepositoryImpl;
import com.opms.data.repository.DepartmentRepository;
import com.opms.data.repository.DepartmentRepositoryImpl;
import com.opms.data.repository.ImageUploadRepository;
import com.opms.data.repository.ImageUploadRepositoryImpl;
import com.opms.data.repository.OrderRepository;
import com.opms.data.repository.OrderRepositoryImpl;
import com.opms.data.repository.PermissionRepository;
import com.opms.data.repository.PermissionRepositoryImpl;
import com.opms.data.repository.PositionRepository;
import com.opms.data.repository.PositionRepositoryImpl;
import com.opms.data.repository.PostRepository;
import com.opms.data.repository.PostRepositoryImpl;
import com.opms.data.repository.ProcessPostMappingRepository;
import com.opms.data.repository.ProcessPostMappingRepositoryImpl;
import com.opms.data.repository.ProcessRepository;
import com.opms.data.repository.ProcessRepositoryImpl;
import com.opms.data.repository.ProductRepository;
import com.opms.data.repository.ProductRepositoryImpl;

import javax.inject.Singleton;

import dagger.Binds;
import dagger.Module;
import dagger.hilt.InstallIn;
import dagger.hilt.components.SingletonComponent;

@Module
@InstallIn(SingletonComponent.class)
public abstract class RepositoryModule {
    @Binds
    @Singleton
    abstract DepartmentRepository bindDepartmentRepository(DepartmentRepositoryImpl impl);

    @Binds
    @Singleton
    abstract PositionRepository bindPositionRepository(PositionRepositoryImpl impl);

    @Binds
    @Singleton
    abstract PostRepository bindPostRepository(PostRepositoryImpl impl);

    @Binds
    @Singleton
    abstract ProcessRepository bindProcessRepository(ProcessRepositoryImpl impl);

    @Binds
    @Singleton
    abstract ProductRepository bindProductRepository(ProductRepositoryImpl impl);

    @Binds
    @Singleton
    abstract CustomerRepository bindCustomerRepository(CustomerRepositoryImpl impl);

    @Binds
    @Singleton
    abstract OrderRepository bindOrderRepository(OrderRepositoryImpl impl);

    @Binds
    @Singleton
    abstract PermissionRepository bindPermissionRepository(PermissionRepositoryImpl impl);

    @Binds
    @Singleton
    abstract ImageUploadRepository bindImageUploadRepository(ImageUploadRepositoryImpl impl);

    @Binds
    @Singleton
    abstract ProcessPostMappingRepository bindProcessPostMappingRepository(ProcessPostMappingRepositoryImpl impl);

    @Binds
    @Singleton
    abstract ComponentRepository bindComponentRepository(ComponentRepositoryImpl impl);
}