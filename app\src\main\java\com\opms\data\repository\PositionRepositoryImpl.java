package com.opms.data.repository;

import com.opms.data.local.dao.PositionDao;
import com.opms.data.local.entity.Position;
import com.opms.data.model.request.PositionRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.PositionResponse;
import com.opms.data.remote.ApiService;

import java.util.Collections;
import java.util.List;

import javax.inject.Inject;

import retrofit2.Call;

public class PositionRepositoryImpl implements PositionRepository {
    private final PositionDao dao;
    private final ApiService api;

    @Inject
    public PositionRepositoryImpl(PositionDao positionDao, ApiService apiService) {
        this.dao = positionDao;
        this.api = apiService;
    }

    public Call<ApiResponse<List<PositionResponse>>> getPositions() {
        return api.getPositions();
    }

    public Call<ApiResponse<PositionResponse>> createPosition(PositionRequest request) {
        return api.createPosition(request);
    }

    public Call<ApiResponse<PositionResponse>> updatePosition(int id, PositionRequest request) {
        return api.updatePosition(id, request);
    }

    public Call<ApiResponse<Void>> deletePosition(PositionRequest request) {
        return api.deletePosition(request);
    }

    public long insert(Position position) {
        return 0;
    }

    public void update(Position position) {

    }

    public void delete(Position position) {

    }

    public Position findByCode(String code) {
        return null;
    }

    public Position findById(long id) {
        return null;
    }

    public List<Position> getAllPositions() {
        return Collections.emptyList();
    }
}