package com.opms.data.repository;

import com.opms.data.local.entity.Permission;
import com.opms.data.model.request.PermissionRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.PermissionCompleteResponse;
import com.opms.data.model.response.PermissionResponse;

import java.util.List;

import retrofit2.Call;

/**
 * 权限仓库接口
 */
public interface PermissionRepository {
    /**
     * 获取所有权限
     */
    Call<ApiResponse<List<PermissionResponse>>> getPermissions();

    /**
     * 根据模板ID获取权限
     */
    Call<ApiResponse<PermissionCompleteResponse>> getPermissionsById(int id);

    /**
     * 创建权限
     */
    Call<ApiResponse<PermissionResponse>> createPermission(PermissionRequest request);

    /**
     * 更新权限
     */
    Call<ApiResponse<PermissionResponse>> updatePermission(int id, PermissionRequest request);

    /**
     * 删除权限
     */
    Call<ApiResponse<Void>> deletePermission(PermissionRequest request);

    // Local database operations
    Permission getPermissionById(int id);
    Permission getPermissionByCode(String code);
    void insert(Permission permission);
    void update(Permission permission);
    void delete(Permission permission);
}