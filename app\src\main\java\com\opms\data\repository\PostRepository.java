package com.opms.data.repository;

import com.opms.data.local.entity.Post;
import com.opms.data.model.request.PostRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.PostResponse;

import java.util.List;

import retrofit2.Call;

public interface PostRepository {
    Call<ApiResponse<List<PostResponse>>> getPosts();
    Call<ApiResponse<PostResponse>> createPost(PostRequest request);
    Call<ApiResponse<PostResponse>> updatePost(int id, PostRequest request);

    Call<ApiResponse<Void>> deletePost(PostRequest request);
    long insert(Post post);
    void update(Post post);
    void delete(Post post);
    Post findByCode(String code);
    Post findById(int id);
    List<Post> getAllPosts();
} 