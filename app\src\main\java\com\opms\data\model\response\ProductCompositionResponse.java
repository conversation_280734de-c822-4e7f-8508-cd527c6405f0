package com.opms.data.model.response;

import com.google.gson.annotations.SerializedName;

/**
 * 产品构成信息Model
 */
public class ProductCompositionResponse extends ComponentResponse {
    /**
     * compositionId
     */
    @SerializedName("compositionId")
    private int compositionId;
    /**
     * 产品Id
     */
    @SerializedName("productId")
    private int productId;
    /**
     * 部件数量
     */
    @SerializedName("number")
    private int number;
    /**
     * 状态
     */
    @SerializedName("status")
    private String status;

    public int getCompositionId() {
        return compositionId;
    }

    public void setCompositionId(int id) {
        this.compositionId = compositionId;
    }

    public int getProductId() {
        return productId;
    }

    public void setProductId(int productId) {
        this.productId = productId;
    }

    public int getNumber() {
        return number;
    }

    public void setNumber(int number) {
        this.number = number;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
