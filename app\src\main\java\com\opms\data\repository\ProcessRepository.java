package com.opms.data.repository;

import com.opms.data.local.entity.Process;
import com.opms.data.model.request.ProcessRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.ProcessResponse;

import java.util.List;

import retrofit2.Call;

public interface ProcessRepository {
    Call<ApiResponse<List<ProcessResponse>>> getProcesses();
    Call<ApiResponse<ProcessResponse>> createProcess(ProcessRequest request);
    Call<ApiResponse<ProcessResponse>> updateProcess(int id, ProcessRequest request);

    Call<ApiResponse<Void>> deleteProcess(ProcessRequest request);
    long insert(Process process);
    void update(Process process);
    void delete(Process process);
    Process findByCode(String code);
    Process findById(int id);
    List<Process> getAllProcesses();
}