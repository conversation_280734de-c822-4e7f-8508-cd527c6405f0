package com.opms.ui.system;

import android.os.Bundle;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.ArrayAdapter;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import com.google.android.material.snackbar.Snackbar;
import com.opms.R;
import com.opms.data.model.request.ProcessPostMappingRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.PostResponse;
import com.opms.data.model.response.ProcessPostMappingResponse;
import com.opms.data.model.response.ProcessTemplateResponse;
import com.opms.data.repository.PostRepository;
import com.opms.data.repository.ProcessPostMappingRepository;
import com.opms.data.repository.ProcessTemplateRepository;
import com.opms.databinding.ActivityProcessPostMappingEditBinding;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class ProcessPostMappingEditActivity extends AppCompatActivity {

    private static final String TAG = "ProcessPostMappingEdit";

    @Inject
    ProcessPostMappingRepository processPostMappingRepository;

    @Inject
    ProcessTemplateRepository processTemplateRepository;

    @Inject
    PostRepository postRepository;

    private ActivityProcessPostMappingEditBinding binding;
    private boolean isEditMode = false;
    private int mappingId = -1;

    private List<ProcessTemplateResponse> processList;
    private List<PostResponse> postList;
    private ArrayAdapter<ProcessTemplateResponse> processAdapter;
    private ArrayAdapter<PostResponse> postAdapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityProcessPostMappingEditBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setupToolbar();
        setupSpinners();
        setupButtons();
        loadData();
        checkEditMode();
    }

    private void setupToolbar() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("流程岗位映射");
        }
    }

    private void setupSpinners() {
        // 初始化流程下拉框
        processList = new ArrayList<>();
        processAdapter = new ArrayAdapter<ProcessTemplateResponse>(this,
                android.R.layout.simple_spinner_item, processList) {
            @Override
            public View getView(int position, View convertView, android.view.ViewGroup parent) {
                View view = super.getView(position, convertView, parent);
                ProcessTemplateResponse process = getItem(position);
                if (process != null) {
                    android.widget.TextView textView = (android.widget.TextView) view;
                    textView.setText(process.getName() + "(" + process.getCode() + ")");
                    textView.setPadding(0, 0, 0, 0); // 移除内边距，由Spinner本身控制
                    textView.setTextSize(16);
                    textView.setTextColor(getResources().getColor(R.color.text_primary));
                    textView.setGravity(android.view.Gravity.CENTER_VERTICAL);
                    textView.setMinHeight(72); // 确保足够的高度
                }
                return view;
            }

            @Override
            public View getDropDownView(int position, View convertView, android.view.ViewGroup parent) {
                View view = super.getDropDownView(position, convertView, parent);
                ProcessTemplateResponse process = getItem(position);
                if (process != null) {
                    android.widget.TextView textView = (android.widget.TextView) view;
                    textView.setText(process.getName() + "(" + process.getCode() + ")");
                    textView.setPadding(16, 20, 16, 20); // 增加下拉项的内边距
                    textView.setTextSize(16);
                    textView.setTextColor(getResources().getColor(R.color.text_primary));
                    textView.setMinHeight(64); // 下拉项最小高度
                }
                return view;
            }
        };
        processAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        binding.spinnerProcess.setAdapter(processAdapter);

        // 初始化岗位下拉框
        postList = new ArrayList<>();
        postAdapter = new ArrayAdapter<PostResponse>(this,
                android.R.layout.simple_spinner_item, postList) {
            @Override
            public View getView(int position, View convertView, android.view.ViewGroup parent) {
                View view = super.getView(position, convertView, parent);
                PostResponse post = getItem(position);
                if (post != null) {
                    android.widget.TextView textView = (android.widget.TextView) view;
                    textView.setText(post.getName() + "(" + post.getCode() + ")");
                    textView.setPadding(0, 0, 0, 0); // 移除内边距，由Spinner本身控制
                    textView.setTextSize(16);
                    textView.setTextColor(getResources().getColor(R.color.text_primary));
                    textView.setGravity(android.view.Gravity.CENTER_VERTICAL);
                    textView.setMinHeight(72); // 确保足够的高度
                }
                return view;
            }

            @Override
            public View getDropDownView(int position, View convertView, android.view.ViewGroup parent) {
                View view = super.getDropDownView(position, convertView, parent);
                PostResponse post = getItem(position);
                if (post != null) {
                    android.widget.TextView textView = (android.widget.TextView) view;
                    textView.setText(post.getName() + "(" + post.getCode() + ")");
                    textView.setPadding(16, 20, 16, 20); // 增加下拉项的内边距
                    textView.setTextSize(16);
                    textView.setTextColor(getResources().getColor(R.color.text_primary));
                    textView.setMinHeight(64); // 下拉项最小高度
                }
                return view;
            }
        };
        postAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        binding.spinnerPost.setAdapter(postAdapter);
    }

    private void setupButtons() {
        binding.btnSave.setOnClickListener(v -> saveMapping());
        binding.btnCancel.setOnClickListener(v -> finish());
    }

    private void loadData() {
        loadProcesses();
        loadPosts();
    }

    private void loadProcesses() {
        processTemplateRepository.getProcessTemplates().enqueue(new Callback<ApiResponse<List<ProcessTemplateResponse>>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<List<ProcessTemplateResponse>>> call,
                                   @NonNull Response<ApiResponse<List<ProcessTemplateResponse>>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<List<ProcessTemplateResponse>> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        processList.clear();
                        processList.addAll(apiResponse.getData());
                        processAdapter.notifyDataSetChanged();

                        // 如果是编辑模式，设置选中的流程
                        if (isEditMode) {
                            setSelectedProcess();
                        }
                    }
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<List<ProcessTemplateResponse>>> call, @NonNull Throwable t) {
                Log.e(TAG, "加载流程列表失败: " + t.getMessage(), t);
                showError("加载流程列表失败: " + t.getMessage());
            }
        });
    }

    private void loadPosts() {
        postRepository.getPosts().enqueue(new Callback<ApiResponse<List<PostResponse>>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<List<PostResponse>>> call,
                                   @NonNull Response<ApiResponse<List<PostResponse>>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<List<PostResponse>> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        postList.clear();
                        postList.addAll(apiResponse.getData());
                        postAdapter.notifyDataSetChanged();

                        // 如果是编辑模式，设置选中的岗位
                        if (isEditMode) {
                            setSelectedPost();
                        }
                    }
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<List<PostResponse>>> call, @NonNull Throwable t) {
                Log.e(TAG, "加载岗位列表失败: " + t.getMessage(), t);
                showError("加载岗位列表失败: " + t.getMessage());
            }
        });
    }

    private void checkEditMode() {
        mappingId = getIntent().getIntExtra("mapping_id", -1);
        if (mappingId != -1) {
            isEditMode = true;
            if (getSupportActionBar() != null) {
                getSupportActionBar().setTitle("编辑流程岗位映射");
            }

            // 设置状态
            String status = getIntent().getStringExtra("status");
            binding.switchStatus.setChecked("1".equals(status));

            // 设置备注
            String remark = getIntent().getStringExtra("remark");
            if (remark != null) {
                binding.etRemark.setText(remark);
            }
        } else {
            if (getSupportActionBar() != null) {
                getSupportActionBar().setTitle("新增流程岗位映射");
            }
            binding.switchStatus.setChecked(true); // 默认启用
        }
    }

    private void setSelectedProcess() {
        int processId = getIntent().getIntExtra("process_id", -1);
        if (processId != -1) {
            for (int i = 0; i < processList.size(); i++) {
                if (processList.get(i).getId() == processId) {
                    binding.spinnerProcess.setSelection(i);
                    break;
                }
            }
        }
    }

    private void setSelectedPost() {
        int postId = getIntent().getIntExtra("post_id", -1);
        if (postId != -1) {
            for (int i = 0; i < postList.size(); i++) {
                if (postList.get(i).getId() == postId) {
                    binding.spinnerPost.setSelection(i);
                    break;
                }
            }
        }
    }

    private void saveMapping() {
        if (!validateInput()) {
            return;
        }

        ProcessTemplateResponse selectedProcess = (ProcessTemplateResponse) binding.spinnerProcess.getSelectedItem();
        PostResponse selectedPost = (PostResponse) binding.spinnerPost.getSelectedItem();
        boolean status = binding.switchStatus.isChecked();
        String remark = binding.etRemark.getText().toString().trim();

        ProcessPostMappingRequest request = new ProcessPostMappingRequest();
        if (isEditMode) {
            request.setId(mappingId);
        }
        request.setProcessId(selectedProcess.getId());
        request.setPostId(selectedPost.getId());
        request.setStatus(status ? "1" : "0");
        request.setRemark(remark);

        binding.btnSave.setEnabled(false);

        Call<ApiResponse<ProcessPostMappingResponse>> call;
        if (isEditMode) {
            call = processPostMappingRepository.updateProcessPostMapping(mappingId, request);
        } else {
            call = processPostMappingRepository.createProcessPostMapping(request);
        }

        call.enqueue(new Callback<ApiResponse<ProcessPostMappingResponse>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<ProcessPostMappingResponse>> call,
                                   @NonNull Response<ApiResponse<ProcessPostMappingResponse>> response) {
                binding.btnSave.setEnabled(true);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<ProcessPostMappingResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        // 显示成功提示
                        String successMessage = isEditMode ?
                                "流程岗位映射更新成功！" :
                                "流程岗位映射创建成功！";

                        Snackbar.make(binding.getRoot(), successMessage, Snackbar.LENGTH_LONG)
                                .setAction("确定", v -> {
                                    setResult(RESULT_OK);
                                    finish();
                                })
                                .show();

                        // 延迟自动关闭页面
                        binding.getRoot().postDelayed(() -> {
                            setResult(RESULT_OK);
                            finish();
                        }, 2000);
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() :
                                (isEditMode ? "更新失败" : "创建失败"));
                    }
                } else {
                    showError(isEditMode ? "更新失败" : "创建失败");
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<ProcessPostMappingResponse>> call, @NonNull Throwable t) {
                binding.btnSave.setEnabled(true);
                Log.e(TAG, "保存流程岗位映射失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    private boolean validateInput() {
        if (binding.spinnerProcess.getSelectedItem() == null) {
            showError("请选择流程");
            return false;
        }

        if (binding.spinnerPost.getSelectedItem() == null) {
            showError("请选择岗位");
            return false;
        }

        return true;
    }

    private void showError(String message) {
        Snackbar.make(binding.getRoot(), message, Snackbar.LENGTH_SHORT).show();
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
