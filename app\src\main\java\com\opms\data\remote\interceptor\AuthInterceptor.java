package com.opms.data.remote.interceptor;

import android.content.Context;

import com.opms.data.local.PreferencesManager;

import java.io.IOException;

import javax.inject.Inject;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

public class AuthInterceptor implements Interceptor {
    private final PreferencesManager preferencesManager;

    @Inject
    public AuthInterceptor(Context context) {
        this.preferencesManager = new PreferencesManager(context);
    }

    public AuthInterceptor() {
        this.preferencesManager = null;
    }

    @Override
    public Response intercept(Chain chain) throws IOException {
        Request original = chain.request();
        String token = preferencesManager.getToken();

        if (token.isEmpty()) {
            return chain.proceed(original);
        }

        Request.Builder builder = original.newBuilder()
                .header("Authorization", "Bearer " + token);

        return chain.proceed(builder.build());
    }
} 