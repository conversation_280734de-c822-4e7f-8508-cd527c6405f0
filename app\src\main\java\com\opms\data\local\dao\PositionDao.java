package com.opms.data.local.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.opms.data.local.entity.Position;

import java.util.List;

@Dao
public interface PositionDao {
    @Insert
    long insert(Position position);

    @Update
    void update(Position position);

    @Delete
    void delete(Position position);

    @Query("SELECT * FROM positions WHERE code = :code LIMIT 1")
    Position findByCode(String code);

    @Query("SELECT * FROM positions WHERE id = :id LIMIT 1")
    Position findById(long id);

    @Query("SELECT * FROM positions")
    List<Position> getAllPositions();
} 