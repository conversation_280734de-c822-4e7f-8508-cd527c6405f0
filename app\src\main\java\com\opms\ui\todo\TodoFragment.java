package com.opms.ui.todo;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.opms.databinding.FragmentTodoBinding;

import dagger.hilt.android.AndroidEntryPoint;

@AndroidEntryPoint
public class TodoFragment extends Fragment {
    private FragmentTodoBinding binding;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentTodoBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        // TODO: Initialize views and setup listeners
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
} 