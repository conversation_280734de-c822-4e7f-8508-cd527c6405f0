package com.opms.data.repository;

import com.opms.data.local.dao.CustomerDao;
import com.opms.data.local.entity.Customer;
import com.opms.data.model.request.CustomerRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.CustomerListResponse;
import com.opms.data.model.response.CustomerResponse;
import com.opms.data.remote.ApiService;

import java.util.List;

import javax.inject.Inject;

import retrofit2.Call;

public class CustomerRepositoryImpl implements CustomerRepository {
    private final CustomerDao customerDao;
    private final ApiService apiService;

    @Inject
    public CustomerRepositoryImpl(CustomerDao customerDao, ApiService apiService) {
        this.customerDao = customerDao;
        this.apiService = apiService;
    }


    public Call<ApiResponse<CustomerListResponse>> getCustomerList(int page, int size, String keyword) {
        return apiService.getCustomerList(page, size, keyword);
    }

    public Call<ApiResponse<CustomerResponse>> getCustomerDetail(int id) {
        return apiService.getCustomerDetail(id);
    }

    public Call<ApiResponse<CustomerResponse>> createCustomer(CustomerRequest request) {
        return apiService.createCustomer(request);
    }

    public Call<ApiResponse<CustomerResponse>> updateCustomer(int id, CustomerRequest request) {
        return apiService.updateCustomer(id, request);
    }

    public Call<ApiResponse<Void>> deleteCustomer(CustomerRequest request) {
        return apiService.deleteCustomer(request);
    }

    public void insert(Customer customer) {
        customerDao.insert(customer);
    }

    public void update(Customer customer) {
        customerDao.update(customer);
    }


    public void delete(Customer customer) {
        customerDao.delete(customer);
    }


    public Customer findByCode(String code) {
        return customerDao.findByCode(code);
    }


    public Customer findById(int id) {
        return customerDao.findById(id);
    }


    public List<Customer> getAllCustomers() {
        return customerDao.getAllCustomers();
    }


    public List<Customer> searchCustomers(String keyword) {
        return customerDao.searchCustomers(keyword);
    }
}