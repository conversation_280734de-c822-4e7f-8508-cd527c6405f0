package com.opms.ui.register;

import android.app.DatePickerDialog;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.provider.MediaStore;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RadioGroup;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AppCompatActivity;

import com.google.android.material.snackbar.Snackbar;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.google.gson.Gson;
import com.opms.R;
import com.opms.common.enums.Gender;
import com.opms.common.enums.ImgType;
import com.opms.common.enums.Statuses;
import com.opms.common.utils.DateUtils;
import com.opms.common.utils.ImageUtils;
import com.opms.data.model.request.ImgRequest;
import com.opms.data.model.request.RegisterRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.RegisterResponse;
import com.opms.data.model.response.UserResponse;
import com.opms.data.repository.UserRepository;
import com.opms.ui.login.LoginActivity;
import com.opms.utils.MD5Utils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Locale;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class RegisterActivity extends AppCompatActivity {
    private static final String TAG = "RegisterActivity";
    private ImageView ivUserAvatar;
    private TextInputEditText etUsername;
    private TextInputEditText etPassword;
    private TextInputEditText etName;
    private RadioGroup rgGender;
    private TextInputEditText etBirthday;
    private TextInputEditText etIdCard;
    private TextInputEditText etPhone;
    private Button btnRegister;

    // TextInputLayout references for error display
    private TextInputLayout tilUsername;
    private TextInputLayout tilPassword;
    private TextInputLayout tilName;
    private TextInputLayout tilBirthday;
    private TextInputLayout tilIdCard;
    private TextInputLayout tilPhone;

    private Uri selectedImageUri;
    private final Calendar calendar = Calendar.getInstance();
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());

    // Username validation
    private boolean isUsernameValid = false;
    private boolean isUsernameChecking = false;
    private Handler usernameCheckHandler = new Handler(Looper.getMainLooper());
    private Runnable usernameCheckRunnable;

    @Inject
    UserRepository userRepository;

    private final ActivityResultLauncher<Intent> pickImage = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            result -> {
                if (result.getResultCode() == RESULT_OK && result.getData() != null) {
                    selectedImageUri = result.getData().getData();
                    ImageUtils.loadUserAvatar(RegisterActivity.this, selectedImageUri, ivUserAvatar);
                }
            });

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_register);

        initViews();
        setupListeners();
    }

    private void initViews() {
        ivUserAvatar = findViewById(R.id.iv_user_avatar);
        etUsername = findViewById(R.id.et_username);
        etPassword = findViewById(R.id.et_password);
        etName = findViewById(R.id.et_name);
        rgGender = findViewById(R.id.rg_gender);
        etBirthday = findViewById(R.id.et_birthday);
        etIdCard = findViewById(R.id.et_id_card);
        etPhone = findViewById(R.id.et_phone);
        btnRegister = findViewById(R.id.btn_register);

        // Initialize TextInputLayout references
        tilUsername = findViewById(R.id.til_username);
        tilPassword = findViewById(R.id.til_password);
        tilName = findViewById(R.id.til_name);
        tilBirthday = findViewById(R.id.til_birthday);
        tilIdCard = findViewById(R.id.til_id_card);
        tilPhone = findViewById(R.id.til_phone);
    }

    private void setupListeners() {
        ivUserAvatar.setOnClickListener(v -> {
            Intent intent = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
            pickImage.launch(intent);
        });

        etBirthday.setOnClickListener(v -> showDatePicker());

        btnRegister.setOnClickListener(v -> register());

        // Setup username real-time validation
        etUsername.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                // Cancel previous check
                if (usernameCheckRunnable != null) {
                    usernameCheckHandler.removeCallbacks(usernameCheckRunnable);
                }

                String username = s.toString().trim();

                // Clear previous error
                tilUsername.setError(null);
                isUsernameValid = false;

                // Validate username length first
                if (username.length() > 0 && (username.length() < 6 || username.length() > 20)) {
                    tilUsername.setError(getString(R.string.error_username_length));
                    return;
                }

                // If username is valid length, check if it exists
                if (username.length() >= 6 && username.length() <= 20) {
                    isUsernameChecking = true;
                    tilUsername.setHelperText(getString(R.string.checking_username));

                    // Delay the API call to avoid too many requests
                    usernameCheckRunnable = () -> checkUsernameExists(username);
                    usernameCheckHandler.postDelayed(usernameCheckRunnable, 500);
                }
            }

            @Override
            public void afterTextChanged(Editable s) {
            }
        });

        // Setup password validation
        etPassword.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                String password = s.toString().trim();
                tilPassword.setError(null);

                if (password.length() > 0 && (password.length() < 6 || password.length() > 20)) {
                    tilPassword.setError(getString(R.string.error_password_length));
                }
            }

            @Override
            public void afterTextChanged(Editable s) {
            }
        });
    }

    private void checkUsernameExists(String username) {
        userRepository.checkUsernameExists(username).enqueue(new Callback<ApiResponse<UserResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<UserResponse>> call, Response<ApiResponse<UserResponse>> response) {
                isUsernameChecking = false;
                tilUsername.setHelperText(null);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<UserResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        // Username exists
                        tilUsername.setError(getString(R.string.error_username_exists));
                        isUsernameValid = false;
                    } else {
                        // Username doesn't exist, it's available
                        tilUsername.setError(null);
                        tilUsername.setHelperText(getString(R.string.username_available));
                        isUsernameValid = true;
                    }
                } else {
                    // API error, assume username is available for now
                    tilUsername.setError(null);
                    tilUsername.setHelperText(getString(R.string.username_available));
                    isUsernameValid = true;
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<UserResponse>> call, Throwable t) {
                isUsernameChecking = false;
                tilUsername.setHelperText(null);
                // Network error, assume username is available for now
                tilUsername.setError(null);
                isUsernameValid = true;
                Log.e(TAG, "Username check failed: " + t.getMessage(), t);
            }
        });
    }

    private boolean validateAllFields() {
        boolean isValid = true;

        // Clear all previous errors
        tilUsername.setError(null);
        tilPassword.setError(null);
        tilName.setError(null);
        tilBirthday.setError(null);
        tilIdCard.setError(null);
        tilPhone.setError(null);

        String username = etUsername.getText().toString().trim();
        String password = etPassword.getText().toString().trim();
        String name = etName.getText().toString().trim();
        String birthday = etBirthday.getText().toString().trim();
        String idCard = etIdCard.getText().toString().trim();
        String phone = etPhone.getText().toString().trim();

        // Validate username
        if (TextUtils.isEmpty(username)) {
            tilUsername.setError(getString(R.string.error_username_empty));
            isValid = false;
        } else if (username.length() < 6 || username.length() > 20) {
            tilUsername.setError(getString(R.string.error_username_length));
            isValid = false;
        } else if (!isUsernameValid) {
            tilUsername.setError(getString(R.string.error_username_exists));
            isValid = false;
        }

        // Validate password
        if (TextUtils.isEmpty(password)) {
            tilPassword.setError(getString(R.string.error_password_empty));
            isValid = false;
        } else if (password.length() < 6 || password.length() > 20) {
            tilPassword.setError(getString(R.string.error_password_length));
            isValid = false;
        }

        // Validate name
        if (TextUtils.isEmpty(name)) {
            tilName.setError(getString(R.string.error_name_empty));
            isValid = false;
        }

        // Validate gender
        if (rgGender.getCheckedRadioButtonId() == -1) {
            Snackbar.make(findViewById(android.R.id.content), getString(R.string.error_gender_empty), Snackbar.LENGTH_SHORT).show();
            isValid = false;
        }

        // Validate birthday
        if (TextUtils.isEmpty(birthday)) {
            tilBirthday.setError(getString(R.string.error_birthday_empty));
            isValid = false;
        }

        // Validate ID card
        if (TextUtils.isEmpty(idCard)) {
            tilIdCard.setError(getString(R.string.error_id_card_empty));
            isValid = false;
        }

        // Validate phone
        if (TextUtils.isEmpty(phone)) {
            tilPhone.setError(getString(R.string.error_phone_empty));
            isValid = false;
        }

        // Check if username is still being checked
        if (isUsernameChecking) {
            Snackbar.make(findViewById(android.R.id.content), getString(R.string.checking_username), Snackbar.LENGTH_SHORT).show();
            isValid = false;
        }

        return isValid;
    }

    private void showDatePicker() {
        DatePickerDialog datePickerDialog = new DatePickerDialog(
                this,
                (view, year, month, dayOfMonth) -> {
                    calendar.set(Calendar.YEAR, year);
                    calendar.set(Calendar.MONTH, month);
                    calendar.set(Calendar.DAY_OF_MONTH, dayOfMonth);
                    etBirthday.setText(dateFormat.format(calendar.getTime()));
                },
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH));
        datePickerDialog.show();
    }

    private void register() {
        // Validate all fields first
        if (!validateAllFields()) {
            return;
        }

        String username = etUsername.getText().toString().trim();
        String password = etPassword.getText().toString().trim();
        String name = etName.getText().toString().trim();
        String gender = rgGender.getCheckedRadioButtonId() == R.id.rb_male ? Gender.MALE.getCode()
                : Gender.FEMALE.getCode();
        String birthday = etBirthday.getText().toString().trim();
        String idCard = etIdCard.getText().toString().trim();
        String phone = etPhone.getText().toString().trim();
        String dateStr = DateUtils.formatCalendarToDate(calendar);

        try {
            // 准备注册请求数据
            RegisterRequest request = new RegisterRequest();
            request.setUsername(username);
            request.setPassword(MD5Utils.md5(password));
            request.setName(name);
            request.setGender(gender);
            request.setBirthday(dateStr);
            request.setIdCard(idCard);
            request.setPhone(phone);

            // 准备头像数据
            MultipartBody.Part avatarPart = null;
            if (selectedImageUri != null) {
                String imgName = username + "_avatar";
                InputStream inputStream = getContentResolver().openInputStream(selectedImageUri);
                byte[] byteArray = readStream(inputStream);
                RequestBody requestFile = RequestBody.create(MediaType.parse("image/*"), byteArray, 0, byteArray.length);
                avatarPart = MultipartBody.Part.createFormData("avatar", imgName + ".jpg", requestFile);
            }

            // 准备图片请求信息
            ImgRequest imgRequest = new ImgRequest();
            imgRequest.setImgType(ImgType.AVATAR.getCode());
            imgRequest.setSubType("");
            imgRequest.setBusinessId(username);
            imgRequest.setImgName(username + "_avatar");
            imgRequest.setCreateUser(username);
            Gson gson = new Gson();
            String json = gson.toJson(imgRequest);
            RequestBody requestImgRequest = RequestBody.create(MediaType.parse("application/json"), json);

            // 调用整合后的注册接口
            userRepository.registerWithAvatar(request, requestImgRequest, avatarPart).enqueue(new Callback<ApiResponse<RegisterResponse>>() {
                @Override
                public void onResponse(Call<ApiResponse<RegisterResponse>> call,
                                       Response<ApiResponse<RegisterResponse>> response) {
                    if (response.isSuccessful() && response.body() != null) {
                        ApiResponse<RegisterResponse> apiResponse = response.body();
                        if (apiResponse.isSuccess()) {
                            RegisterResponse registerResponse = apiResponse.getData();
                            String resStatus = "";
                            String resUsername = "";
                            if (registerResponse != null) {
                                resStatus = registerResponse.getStatus();
                                resUsername = registerResponse.getUsername();
                            }
                            if (username.equals(resUsername)
                                    && Statuses.PENDING.getCode().equals(resStatus)) {
                                showRegistrationSuccessAndNavigate();
                            } else {
                                Snackbar.make(findViewById(android.R.id.content), "注册失败：" + apiResponse.getMessage(),
                                        Snackbar.LENGTH_SHORT).show();
                            }
                        } else {
                            Log.e(TAG, "Registration failed: " + apiResponse.getMessage());
                            Snackbar.make(findViewById(android.R.id.content), "注册失败：" + apiResponse.getMessage(), Snackbar.LENGTH_SHORT).show();
                        }
                    } else {
                        try {
                            Log.e(TAG, "Registration failed: " + (response.errorBody() != null ? response.errorBody().string() : "Unknown error"));
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }
                        Snackbar.make(findViewById(android.R.id.content), "注册失败，请稍后重试", Snackbar.LENGTH_SHORT).show();
                    }
                }

                @Override
                public void onFailure(Call<ApiResponse<RegisterResponse>> call, Throwable t) {
                    Log.e(TAG, "Registration failed: " + t.getMessage(), t);
                    Snackbar.make(findViewById(android.R.id.content), "注册失败：" + t.getMessage(), Snackbar.LENGTH_SHORT).show();
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "Registration failed: " + e.getMessage(), e);
            Snackbar.make(findViewById(android.R.id.content), "注册失败：" + e.getMessage(), Snackbar.LENGTH_SHORT).show();
        }
    }

    /**
     * 显示注册成功提示并延迟跳转到登录页面
     */
    private void showRegistrationSuccessAndNavigate() {
        // 禁用注册按钮，防止重复提交
        btnRegister.setEnabled(false);
        btnRegister.setText("注册成功");

        // 显示成功提示
        Snackbar successSnackbar = Snackbar.make(findViewById(android.R.id.content),
                "🎉 注册成功！请等待管理员审核，3秒后自动跳转到登录页面...",
                Snackbar.LENGTH_INDEFINITE);

        // 设置Snackbar样式
        successSnackbar.setBackgroundTint(getResources().getColor(android.R.color.holo_green_dark, null));
        successSnackbar.setTextColor(getResources().getColor(android.R.color.white, null));
        successSnackbar.show();

        // 3秒后跳转到登录页面
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            successSnackbar.dismiss();
            Intent intent = new Intent(RegisterActivity.this, LoginActivity.class);
            // 添加一些额外的信息传递给登录页面
            intent.putExtra("registration_success", true);
            intent.putExtra("message", "注册成功，请使用新账号登录");
            startActivity(intent);
            // 添加滑动动画效果
            overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right);
            finish();
        }, 3000); // 3秒延迟
    }

    @Override
    public void finish() {
        super.finish();
        // 添加返回动画
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right);
    }

    // 辅助方法：将 InputStream 转换为 byte[]
    private byte[] readStream(InputStream inputStream) throws IOException {
        ByteArrayOutputStream byteBuffer = new ByteArrayOutputStream();
        int bufferSize = 1024;
        byte[] buffer = new byte[bufferSize];
        int len;
        while ((len = inputStream.read(buffer)) != -1) {
            byteBuffer.write(buffer, 0, len);
        }
        return byteBuffer.toByteArray();
    }
}