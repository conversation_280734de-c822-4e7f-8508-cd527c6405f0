package com.opms.common.utils;

import android.content.Context;
import android.content.SharedPreferences;

public class SharedPreferencesUtils {
    private static final String PREF_NAME = "opms_pref";
    private static final String KEY_TOKEN = "token";
    private static final String KEY_USERNAME = "username";
    private static final String KEY_ROLE = "role";

    private static SharedPreferences getSharedPreferences(Context context) {
        return context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
    }

    public static void saveToken(Context context, String token) {
        getSharedPreferences(context).edit().putString(KEY_TOKEN, token).apply();
    }

    public static String getToken(Context context) {
        return getSharedPreferences(context).getString(KEY_TOKEN, "");
    }

    public static void saveUsername(Context context, String username) {
        getSharedPreferences(context).edit().putString(KEY_USERNAME, username).apply();
    }

    public static String getUsername(Context context) {
        return getSharedPreferences(context).getString(KEY_USERNAME, "");
    }

    public static void saveRole(Context context, String role) {
        getSharedPreferences(context).edit().putString(KEY_ROLE, role).apply();
    }

    public static String getRole(Context context) {
        return getSharedPreferences(context).getString(KEY_ROLE, "");
    }

    public static void clearUserInfo(Context context) {
        getSharedPreferences(context).edit()
                .remove(KEY_TOKEN)
                .remove(KEY_USERNAME)
                .remove(KEY_ROLE)
                .apply();
    }
} 