package com.opms.data.repository;

import com.opms.data.local.dao.PostDao;
import com.opms.data.local.entity.Post;
import com.opms.data.model.request.PostRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.PostResponse;
import com.opms.data.remote.ApiService;

import java.util.List;

import javax.inject.Inject;

import retrofit2.Call;

public class PostRepositoryImpl implements PostRepository {
    private final PostDao dao;
    private final ApiService api;

    @Inject
    public PostRepositoryImpl(PostDao postDao, ApiService apiService) {
        this.dao = postDao;
        this.api = apiService;
    }

    public Call<ApiResponse<List<PostResponse>>> getPosts() {
        return api.getPosts();
    }

    public Call<ApiResponse<PostResponse>> createPost(PostRequest request) {
        return api.createPost(request);
    }

    public Call<ApiResponse<PostResponse>> updatePost(int id, PostRequest request) {
        return api.updatePost(id, request);
    }

    public Call<ApiResponse<Void>> deletePost(PostRequest request) {
        return api.deletePost(request);
    }

    public long insert(Post post) {
        return dao.insert(post);
    }

    public void update(Post post) {
        dao.update(post);
    }

    public void delete(Post post) {
        dao.delete(post);
    }

    public Post findByCode(String code) {
        return dao.findByCode(code);
    }

    public Post findById(int id) {
        return dao.findById(id);
    }

    public List<Post> getAllPosts() {
        return dao.getAllPosts();
    }
} 