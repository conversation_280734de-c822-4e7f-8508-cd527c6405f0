package com.opms.data.local.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;
import java.util.List;

import com.opms.data.local.entity.Permission;
@Dao
public interface PermissionDao {
    @Insert
    void insert(Permission permission);

    @Update
    void update(Permission permission);

    @Delete
    void delete(Permission permission);

    @Query("SELECT * FROM permissions")
    List<Permission> getAll();

    @Query("SELECT * FROM permissions WHERE id = :id LIMIT 1")
    Permission findById(int id);

    @Query("SELECT * FROM permissions WHERE status = :status")
    List<Permission> getByStatus(String status);

    @Query("SELECT * FROM permissions WHERE code = :code LIMIT 1")
    Permission findByCode(String code);
}
