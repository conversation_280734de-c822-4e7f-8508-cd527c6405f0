package com.opms.ui.business;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.MenuItem;
import android.view.View;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.google.android.material.snackbar.Snackbar;
import com.opms.R;
import com.opms.data.model.request.ProductRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.ProductListResponse;
import com.opms.data.model.response.ProductResponse;
import com.opms.data.repository.ProductRepository;
import com.opms.databinding.ActivityProductManagementBinding;
import com.opms.ui.business.adapter.ProductAdapter;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class ProductManagementActivity extends AppCompatActivity {

    private static final String TAG = "ProductManagementActivity";
    private final int pageSize = 10;
    @Inject
    ProductRepository productRepository;
    private ActivityProductManagementBinding binding;
    private ProductAdapter productAdapter;
    private List<ProductResponse> productList = new ArrayList<>();
    // 分页相关
    private int currentPage = 1;
    private boolean isLoading = false;
    private boolean hasMoreData = true;
    private String currentKeyword = "";

    // 工具栏状态
    private boolean isToolbarExpanded = false;

    private ActivityResultLauncher<Intent> editActivityLauncher;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityProductManagementBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        initializeActivityLauncher();
        setupToolbar();
        setupRecyclerView();
        setupSearchFunctionality();
        setupButtons();
        setupToolbarToggle();

        loadProducts();
    }

    private void initializeActivityLauncher() {
        editActivityLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (result.getResultCode() == RESULT_OK) {
                        // 刷新列表
                        refreshProducts();
                    }
                }
        );
    }

    private void setupToolbar() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("产品管理");
        }
    }

    private void setupRecyclerView() {
        productAdapter = new ProductAdapter(this);
        binding.rvProducts.setLayoutManager(new LinearLayoutManager(this));
        binding.rvProducts.setAdapter(productAdapter);

        // 设置点击监听器
        productAdapter.setOnProductClickListener(new ProductAdapter.OnProductClickListener() {
            @Override
            public void onProductClick(ProductResponse product) {
                openEditProductPage(product.getId());
            }

            @Override
            public void onEditClick(ProductResponse product) {
                openEditProductPage(product.getId());
            }

            @Override
            public void onDeleteClick(ProductResponse product) {
                showDeleteConfirmDialog(product);
            }
        });

        // 设置滚动监听器实现分页加载
        binding.rvProducts.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);

                LinearLayoutManager layoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();
                if (layoutManager != null && hasMoreData && !isLoading) {
                    int visibleItemCount = layoutManager.getChildCount();
                    int totalItemCount = layoutManager.getItemCount();
                    int firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition();

                    if ((visibleItemCount + firstVisibleItemPosition) >= totalItemCount - 2) {
                        currentPage++;
                        loadProducts();
                    }
                }
            }
        });

        // 设置下拉刷新
        binding.swipeRefresh.setOnRefreshListener(this::refreshProducts);
    }

    private void setupSearchFunctionality() {
        binding.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                // 实时搜索
                String keyword = s.toString().trim();
                if (!keyword.equals(currentKeyword)) {
                    currentKeyword = keyword;
                    currentPage = 1;
                    hasMoreData = true;
                    loadProducts();
                }
            }
        });
    }

    private void setupButtons() {
        binding.fabAdd.setOnClickListener(v -> openAddProductPage());
        binding.btnToggleToolbar.setOnClickListener(v -> toggleToolbar());
        binding.btnClearSearch.setOnClickListener(v -> clearSearch());
    }

    private void setupToolbarToggle() {
        // 工具栏默认收起状态
        binding.cardExpandableTools.setVisibility(View.GONE);
        binding.btnToggleToolbar.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_expand_more, 0);
    }

    private void toggleToolbar() {
        if (isToolbarExpanded) {
            // 收起工具栏
            binding.cardExpandableTools.setVisibility(View.GONE);
            binding.btnToggleToolbar.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_expand_more, 0);
            isToolbarExpanded = false;
        } else {
            // 展开工具栏
            binding.cardExpandableTools.setVisibility(View.VISIBLE);
            binding.btnToggleToolbar.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_expand_less, 0);
            isToolbarExpanded = true;
        }
    }

    private void clearSearch() {
        binding.etSearch.setText("");
        currentKeyword = "";
        currentPage = 1;
        hasMoreData = true;
        loadProducts();
    }

    private void loadProducts() {
        if (isLoading) return;

        showLoading(true);
        isLoading = true;

        Call<ApiResponse<ProductListResponse>> call = productRepository.getProductList(
                currentPage, pageSize, currentKeyword);

        call.enqueue(new Callback<ApiResponse<ProductListResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<ProductListResponse>> call,
                                   Response<ApiResponse<ProductListResponse>> response) {
                showLoading(false);
                isLoading = false;
                binding.swipeRefresh.setRefreshing(false);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<ProductListResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        ProductListResponse productListResponse = apiResponse.getData();
                        List<ProductResponse> products = productListResponse.getList();

                        if (currentPage == 1) {
                            // 首页数据，清空现有列表
                            productList.clear();
                            productAdapter.setProductList(products);
                        } else {
                            // 分页数据，追加到现有列表
                            productAdapter.addProducts(products);
                        }

                        productList.addAll(products != null ? products : new ArrayList<>());

                        // 检查是否还有更多数据
                        hasMoreData = products != null && products.size() >= pageSize;

                        // 更新产品数量显示
                        updateProductCount();

                        // 显示/隐藏空状态视图
                        updateEmptyState();
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "获取产品列表失败");
                    }
                } else {
                    showError("网络请求失败，请检查网络连接");
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<ProductListResponse>> call, Throwable t) {
                showLoading(false);
                isLoading = false;
                binding.swipeRefresh.setRefreshing(false);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    private void refreshProducts() {
        currentPage = 1;
        hasMoreData = true;
        loadProducts();
    }

    private void performSearch() {
        currentKeyword = binding.etSearch.getText().toString().trim();
        currentPage = 1;
        hasMoreData = true;
        loadProducts();
    }

    private void updateProductCount() {
        int count = productList.size();
        String countText = "共 " + count + " 个产品";
        binding.tvProductCount.setText(countText);
    }

    private void updateEmptyState() {
        boolean isEmpty = productList.isEmpty();
        binding.llEmpty.setVisibility(isEmpty ? View.VISIBLE : View.GONE);
        binding.rvProducts.setVisibility(isEmpty ? View.GONE : View.VISIBLE);
    }

    private void openAddProductPage() {
        Intent intent = new Intent(this, ProductEditActivity.class);
        editActivityLauncher.launch(intent);
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }

    private void openEditProductPage(int productId) {
        Intent intent = new Intent(this, ProductEditActivity.class);
        intent.putExtra("product_id", productId);
        editActivityLauncher.launch(intent);
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }

    private void showDeleteConfirmDialog(ProductResponse product) {
        new MaterialAlertDialogBuilder(this)
                .setTitle("删除确认")
                .setMessage("确定要删除产品 \"" + product.getName() + "\" 吗？")
                .setPositiveButton("删除", (dialog, which) -> deleteProduct(product))
                .setNegativeButton("取消", null)
                .show();
    }

    private void deleteProduct(ProductResponse product) {
        showLoading(true);

        ProductRequest request = new ProductRequest();
        request.setId(product.getId());

        Call<ApiResponse<Void>> call = productRepository.deleteProduct(request);
        call.enqueue(new Callback<ApiResponse<Void>>() {
            @Override
            public void onResponse(Call<ApiResponse<Void>> call, Response<ApiResponse<Void>> response) {
                showLoading(false);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<Void> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        Snackbar.make(binding.getRoot(), "产品删除成功", Snackbar.LENGTH_SHORT).show();
                        refreshProducts();
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "删除产品失败");
                    }
                } else {
                    showError("网络请求失败，请检查网络连接");
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<Void>> call, Throwable t) {
                showLoading(false);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    private void showLoading(boolean show) {
        binding.progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
    }

    private void showError(String message) {
        Snackbar.make(binding.getRoot(), message, Snackbar.LENGTH_LONG).show();
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
