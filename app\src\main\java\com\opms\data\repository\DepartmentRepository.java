package com.opms.data.repository;

import com.opms.data.local.entity.Department;
import com.opms.data.model.request.DepartmentRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.DepartmentResponse;

import java.util.List;

import retrofit2.Call;

public interface DepartmentRepository {
    Call<ApiResponse<List<DepartmentResponse>>> getDepartmentList();
    Call<ApiResponse<DepartmentResponse>> createDepartment(DepartmentRequest request);
    Call<ApiResponse<DepartmentResponse>> updateDepartment(int id, DepartmentRequest request);

    Call<ApiResponse<Void>> deleteDepartment(DepartmentRequest request);
    
    // Local database operations
    void insert(Department department);
    void update(Department department);
    void delete(Department department);
    Department findByCode(String code);
    Department findById(int id);
    List<Department> getAllDepartments();
    List<Department> getChildDepartments(String parentCode);
} 