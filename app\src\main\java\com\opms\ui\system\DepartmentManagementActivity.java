package com.opms.ui.system;

import android.animation.ValueAnimator;
import android.app.AlertDialog;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.opms.R;
import com.opms.data.model.request.DepartmentRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.DepartmentResponse;
import com.opms.data.repository.DepartmentRepository;
import com.opms.databinding.ActivityDepartmentManagementBinding;
import com.opms.ui.system.adapter.DepartmentTreeAdapter;
import com.opms.ui.system.model.DepartmentNode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class DepartmentManagementActivity extends AppCompatActivity
        implements SwipeRefreshLayout.OnRefreshListener, DepartmentTreeAdapter.OnNodeClickListener {

    private static final String TAG = "DepartmentManagement";

    @Inject
    DepartmentRepository departmentRepository;

    private ActivityDepartmentManagementBinding binding;
    private DepartmentTreeAdapter adapter;
    private List<DepartmentResponse> allDepartments;
    private List<DepartmentNode> rootNodes;
    private List<DepartmentNode> filteredRootNodes;
    private boolean isToolbarExpanded = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityDepartmentManagementBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setupToolbar();
        setupRecyclerView();
        setupButtons();
        loadDepartments();
    }

    private void setupToolbar() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("部门管理");
        }
    }

    private void setupRecyclerView() {
        adapter = new DepartmentTreeAdapter(this);
        adapter.setOnNodeClickListener(this);

        binding.rvDepartments.setLayoutManager(new LinearLayoutManager(this));
        binding.rvDepartments.setAdapter(adapter);

        binding.swipeRefresh.setOnRefreshListener(this);
        binding.swipeRefresh.setColorSchemeResources(R.color.primary);
    }

    private void setupButtons() {
        // 浮动操作按钮
        binding.fabAddRoot.setOnClickListener(v -> showAddDepartmentDialog(null));

        // 工具栏切换按钮
        binding.btnToggleToolbar.setOnClickListener(v -> toggleToolbar());

        // 快速搜索按钮
        binding.btnToggleSearch.setOnClickListener(v -> {
            if (!isToolbarExpanded) {
                toggleToolbar();
            }
            // 聚焦到搜索框
            binding.etSearch.requestFocus();
        });

        // 选中部门操作按钮
        binding.btnAddChild.setOnClickListener(v -> {
            DepartmentNode selectedNode = adapter.getSelectedNode();
            if (selectedNode != null) {
                showAddDepartmentDialog(selectedNode);
            }
        });
        binding.btnEdit.setOnClickListener(v -> {
            DepartmentNode selectedNode = adapter.getSelectedNode();
            if (selectedNode != null) {
                showEditDepartmentDialog(selectedNode);
            }
        });
        binding.btnDelete.setOnClickListener(v -> {
            DepartmentNode selectedNode = adapter.getSelectedNode();
            if (selectedNode != null) {
                showDeleteConfirmDialog(selectedNode);
            }
        });

        // 清除选择按钮
        binding.btnClearSelection.setOnClickListener(v -> {
            adapter.setSelectedNode(null);
            updateSelectionUI(null);
        });

        // 展开/折叠按钮
        binding.btnExpandAll.setOnClickListener(v -> expandAllNodes());
        binding.btnCollapseAll.setOnClickListener(v -> collapseAllNodes());

        // 搜索功能
        setupSearch();
    }

    private void loadDepartments() {
        showLoading(true);
        departmentRepository.getDepartmentList().enqueue(new Callback<ApiResponse<List<DepartmentResponse>>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<List<DepartmentResponse>>> call,
                                   @NonNull Response<ApiResponse<List<DepartmentResponse>>> response) {
                showLoading(false);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<List<DepartmentResponse>> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        allDepartments = apiResponse.getData();
                        buildDepartmentTree();
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "获取部门列表失败");
                        showEmptyView();
                    }
                } else {
                    showError("获取部门列表失败");
                    showEmptyView();
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<List<DepartmentResponse>>> call, @NonNull Throwable t) {
                showLoading(false);
                Log.e(TAG, "获取部门列表失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
                showEmptyView();
            }
        });
    }

    private void buildDepartmentTree() {
        if (allDepartments == null || allDepartments.isEmpty()) {
            showEmptyView();
            return;
        }

        // 创建节点映射
        Map<String, DepartmentNode> nodeMap = new HashMap<>();
        rootNodes = new ArrayList<>();

        // 创建所有节点
        for (DepartmentResponse dept : allDepartments) {
            DepartmentNode node = new DepartmentNode(dept);
            nodeMap.put(dept.getCode(), node);
        }

        // 构建树形结构
        for (DepartmentResponse dept : allDepartments) {
            DepartmentNode node = nodeMap.get(dept.getCode());

            if (dept.getParentCode() == null || dept.getParentCode().isEmpty()) {
                // 根节点
                rootNodes.add(node);
            } else {
                // 子节点
                DepartmentNode parentNode = nodeMap.get(dept.getParentCode());
                if (parentNode != null) {
                    parentNode.addChild(node);
                } else {
                    // 如果找不到父节点，作为根节点处理
                    rootNodes.add(node);
                }
            }
        }

        filteredRootNodes = new ArrayList<>(rootNodes);
        adapter.setRootNodes(filteredRootNodes);
        updateDepartmentCount();
        hideEmptyView();
    }

    private void showLoading(boolean show) {
        binding.progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
        if (binding.swipeRefresh.isRefreshing() && !show) {
            binding.swipeRefresh.setRefreshing(false);
        }
    }

    private void showEmptyView() {
        binding.llEmpty.setVisibility(View.VISIBLE);
        binding.rvDepartments.setVisibility(View.GONE);
    }

    private void hideEmptyView() {
        binding.llEmpty.setVisibility(View.GONE);
        binding.rvDepartments.setVisibility(View.VISIBLE);
    }

    private void showError(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }

    private void updateSelectionUI(DepartmentNode selectedNode) {
        if (selectedNode != null) {
            binding.cardSelectedInfo.setVisibility(View.VISIBLE);
            binding.tvSelectedDepartment.setText("已选择：" + selectedNode.getDepartment().getName());
        } else {
            binding.cardSelectedInfo.setVisibility(View.GONE);
        }
    }

    @Override
    public void onRefresh() {
        loadDepartments();
    }

    @Override
    public void onNodeClick(DepartmentNode node) {
        updateSelectionUI(node);
    }

    @Override
    public void onNodeLongClick(DepartmentNode node) {
        // 长按显示操作菜单
        showEditDepartmentDialog(node);
    }

    @Override
    public void onExpandToggle(DepartmentNode node) {
        // 展开/折叠节点时的回调
    }

    private void showAddDepartmentDialog(DepartmentNode parentNode) {
        View dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_department_edit, null);

        TextInputLayout tilName = dialogView.findViewById(R.id.til_name);
        TextInputLayout tilCode = dialogView.findViewById(R.id.til_code);
        TextInputLayout tilParent = dialogView.findViewById(R.id.til_parent);
        TextInputEditText etName = dialogView.findViewById(R.id.et_name);
        TextInputEditText etCode = dialogView.findViewById(R.id.et_code);
        TextInputEditText etParent = dialogView.findViewById(R.id.et_parent);

        // 设置父部门信息
        if (parentNode != null) {
            etParent.setText(parentNode.getDepartment().getName());
        } else {
            etParent.setText("无（根部门）");
        }

        AlertDialog dialog = new AlertDialog.Builder(this)
                .setView(dialogView)
                .setCancelable(true)
                .create();

        dialogView.findViewById(R.id.btn_cancel).setOnClickListener(v -> dialog.dismiss());
        dialogView.findViewById(R.id.btn_save).setOnClickListener(v -> {
            String name = etName.getText().toString().trim();
            String code = etCode.getText().toString().trim();

            if (validateDepartmentInput(name, code, tilName, tilCode)) {
                createDepartment(name, code, parentNode, dialog);
            }
        });

        dialog.show();
    }

    private void showEditDepartmentDialog(DepartmentNode node) {
        View dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_department_edit, null);

        TextInputLayout tilName = dialogView.findViewById(R.id.til_name);
        TextInputLayout tilCode = dialogView.findViewById(R.id.til_code);
        TextInputLayout tilParent = dialogView.findViewById(R.id.til_parent);
        TextInputEditText etName = dialogView.findViewById(R.id.et_name);
        TextInputEditText etCode = dialogView.findViewById(R.id.et_code);
        TextInputEditText etParent = dialogView.findViewById(R.id.et_parent);

        // 填充现有数据
        etName.setText(node.getDepartment().getName());
        etCode.setText(node.getDepartment().getCode());
        etCode.setEnabled(false); // 编辑时不允许修改代码

        if (node.getParent() != null) {
            etParent.setText(node.getParent().getDepartment().getName());
        } else {
            etParent.setText("无（根部门）");
        }

        AlertDialog dialog = new AlertDialog.Builder(this)
                .setView(dialogView)
                .setCancelable(true)
                .create();

        dialogView.findViewById(R.id.btn_cancel).setOnClickListener(v -> dialog.dismiss());
        dialogView.findViewById(R.id.btn_save).setOnClickListener(v -> {
            String name = etName.getText().toString().trim();
            String code = etCode.getText().toString().trim();

            if (validateDepartmentInput(name, code, tilName, tilCode, node)) {
                updateDepartment(node, name, code, dialog);
            }
        });

        dialog.show();
    }

    private void showDeleteConfirmDialog(DepartmentNode node) {
        String message = "确定要删除部门 \"" + node.getDepartment().getName() + "\" 吗？";
        if (node.hasChildren()) {
            message += "\n\n注意：删除后，该部门的所有子部门也将被删除！";
        }

        new AlertDialog.Builder(this)
                .setTitle("删除部门")
                .setMessage(message)
                .setPositiveButton("删除", (dialog, which) -> deleteDepartment(node))
                .setNegativeButton("取消", null)
                .show();
    }

    private boolean validateDepartmentInput(String name, String code,
                                            TextInputLayout tilName, TextInputLayout tilCode) {
        return validateDepartmentInput(name, code, tilName, tilCode, null);
    }

    private boolean validateDepartmentInput(String name, String code,
                                            TextInputLayout tilName, TextInputLayout tilCode,
                                            DepartmentNode editingNode) {
        if (name.isEmpty()) {
            tilName.setError("部门名称不能为空");
            return false;
        } else {
            tilName.setError(null);
        }

        if (code.isEmpty()) {
            tilCode.setError("部门代码不能为空");
            return false;
        } else if (!code.matches("^[A-Z0-9_]+$")) {
            tilCode.setError("部门代码只能包含大写字母、数字和下划线");
            return false;
        } else {
            tilCode.setError(null);
        }

        // 检查代码是否重复（基于内存中的数据）
        if (allDepartments != null) {
            for (DepartmentResponse dept : allDepartments) {
                // 如果是编辑模式，跳过当前编辑的部门
                if (editingNode != null && dept.getId() == editingNode.getDepartment().getId()) {
                    continue;
                }

                if (code.equals(dept.getCode())) {
                    String status = dept.getStatus();
                    String statusText = "1".equals(status) ? "" : "(已删除)";
                    String msg = "部门代码[" + code + "]已存在[" + dept.getName() + "]" + statusText;
                    tilCode.setError(msg);
                    return false;
                }
            }
        }

        return true;
    }

    private void createDepartment(String name, String code, DepartmentNode parentNode, AlertDialog dialog) {
        DepartmentRequest request = new DepartmentRequest();
        request.setName(name);
        request.setCode(code);
        request.setParentCode(parentNode != null ? parentNode.getDepartment().getCode() : null);
        request.setStatus("1");

        departmentRepository.createDepartment(request).enqueue(new Callback<ApiResponse<DepartmentResponse>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<DepartmentResponse>> call,
                                   @NonNull Response<ApiResponse<DepartmentResponse>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<DepartmentResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        Toast.makeText(DepartmentManagementActivity.this, "部门创建成功", Toast.LENGTH_SHORT).show();
                        dialog.dismiss();
                        loadDepartments(); // 重新加载数据
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "创建失败");
                    }
                } else {
                    showError("创建失败");
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<DepartmentResponse>> call, @NonNull Throwable t) {
                Log.e(TAG, "创建部门失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    private void updateDepartment(DepartmentNode node, String name, String code, AlertDialog dialog) {
        int id = (int) node.getDepartment().getId();
        DepartmentRequest request = new DepartmentRequest();
        request.setId(id);
        request.setName(name);
        request.setCode(code);
        request.setParentCode(node.getParent() != null ? node.getParent().getDepartment().getCode() : null);
        request.setStatus(node.getDepartment().getStatus());

        departmentRepository.updateDepartment(id, request)
                .enqueue(new Callback<ApiResponse<DepartmentResponse>>() {
                    @Override
                    public void onResponse(@NonNull Call<ApiResponse<DepartmentResponse>> call,
                                           @NonNull Response<ApiResponse<DepartmentResponse>> response) {
                        if (response.isSuccessful() && response.body() != null) {
                            ApiResponse<DepartmentResponse> apiResponse = response.body();
                            if (apiResponse.isSuccess()) {
                                Toast.makeText(DepartmentManagementActivity.this, "部门更新成功", Toast.LENGTH_SHORT).show();
                                dialog.dismiss();
                                loadDepartments(); // 重新加载数据
                            } else {
                                showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "更新失败");
                            }
                        } else {
                            showError("更新失败");
                        }
                    }

                    @Override
                    public void onFailure(@NonNull Call<ApiResponse<DepartmentResponse>> call, @NonNull Throwable t) {
                        Log.e(TAG, "更新部门失败: " + t.getMessage(), t);
                        showError("网络错误: " + t.getMessage());
                    }
                });
    }

    private void deleteDepartment(DepartmentNode node) {
        DepartmentRequest request = new DepartmentRequest();
        request.setId((int) node.getDepartment().getId());

        departmentRepository.deleteDepartment(request)
                .enqueue(new Callback<ApiResponse<Void>>() {
                    @Override
                    public void onResponse(@NonNull Call<ApiResponse<Void>> call,
                                           @NonNull Response<ApiResponse<Void>> response) {
                        if (response.isSuccessful() && response.body() != null) {
                            ApiResponse<Void> apiResponse = response.body();
                            if (apiResponse.isSuccess()) {
                                Toast.makeText(DepartmentManagementActivity.this, "部门删除成功", Toast.LENGTH_SHORT).show();
                                adapter.setSelectedNode(null); // 清除选中状态
                                updateSelectionUI(null);
                                loadDepartments(); // 重新加载数据
                            } else {
                                showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "删除失败");
                            }
                        } else {
                            showError("删除失败");
                        }
                    }

                    @Override
                    public void onFailure(@NonNull Call<ApiResponse<Void>> call, @NonNull Throwable t) {
                        Log.e(TAG, "删除部门失败: " + t.getMessage(), t);
                        showError("网络错误: " + t.getMessage());
                    }
                });
    }

    private void setupSearch() {
        binding.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                filterDepartments(s.toString().trim());
            }
        });
    }

    private void filterDepartments(String query) {
        if (rootNodes == null) return;

        if (query.isEmpty()) {
            filteredRootNodes = new ArrayList<>(rootNodes);
        } else {
            filteredRootNodes = new ArrayList<>();
            for (DepartmentNode rootNode : rootNodes) {
                DepartmentNode filteredNode = filterNode(rootNode, query.toLowerCase());
                if (filteredNode != null) {
                    filteredRootNodes.add(filteredNode);
                }
            }
        }

        adapter.setRootNodes(filteredRootNodes);
        updateDepartmentCount();
    }

    private DepartmentNode filterNode(DepartmentNode node, String query) {
        boolean matches = node.getDepartment().getName().toLowerCase().contains(query) ||
                node.getDepartment().getCode().toLowerCase().contains(query);

        List<DepartmentNode> filteredChildren = new ArrayList<>();
        for (DepartmentNode child : node.getChildren()) {
            DepartmentNode filteredChild = filterNode(child, query);
            if (filteredChild != null) {
                filteredChildren.add(filteredChild);
            }
        }

        if (matches || !filteredChildren.isEmpty()) {
            DepartmentNode filteredNode = new DepartmentNode(node.getDepartment());
            filteredNode.setChildren(filteredChildren);
            filteredNode.setExpanded(true); // 搜索时展开匹配的节点
            return filteredNode;
        }

        return null;
    }

    private void expandAllNodes() {
        if (filteredRootNodes != null) {
            for (DepartmentNode rootNode : filteredRootNodes) {
                expandNodeRecursively(rootNode);
            }
            adapter.setRootNodes(filteredRootNodes);
        }
    }

    private void collapseAllNodes() {
        if (filteredRootNodes != null) {
            for (DepartmentNode rootNode : filteredRootNodes) {
                collapseNodeRecursively(rootNode);
            }
            adapter.setRootNodes(filteredRootNodes);
        }
    }

    private void expandNodeRecursively(DepartmentNode node) {
        node.setExpanded(true);
        for (DepartmentNode child : node.getChildren()) {
            expandNodeRecursively(child);
        }
    }

    private void collapseNodeRecursively(DepartmentNode node) {
        node.setExpanded(false);
        for (DepartmentNode child : node.getChildren()) {
            collapseNodeRecursively(child);
        }
    }

    private void updateDepartmentCount() {
        if (allDepartments != null) {
            int totalCount = allDepartments.size();
            binding.tvDepartmentCount.setText("共 " + totalCount + " 个部门");
        }
    }

    private void toggleToolbar() {
        if (isToolbarExpanded) {
            collapseToolbar();
        } else {
            expandToolbar();
        }
    }

    private void expandToolbar() {
        isToolbarExpanded = true;

        // 更新按钮图标和文本
        binding.btnToggleToolbar.setIcon(getDrawable(R.drawable.ic_expand_less));
        binding.btnToggleToolbar.setText("收起");

        // 显示可展开工具栏
        binding.cardExpandableTools.setVisibility(View.VISIBLE);

        // 添加展开动画
        animateToolbarExpansion(true);
    }

    private void collapseToolbar() {
        isToolbarExpanded = false;

        // 更新按钮图标和文本
        binding.btnToggleToolbar.setIcon(getDrawable(R.drawable.ic_expand_more));
        binding.btnToggleToolbar.setText("工具");

        // 添加收起动画
        animateToolbarExpansion(false);
    }

    private void animateToolbarExpansion(boolean expand) {
        View targetView = binding.cardExpandableTools;

        if (expand) {
            // 测量目标高度
            targetView.measure(
                    View.MeasureSpec.makeMeasureSpec(targetView.getWidth(), View.MeasureSpec.EXACTLY),
                    View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
            );
            int targetHeight = targetView.getMeasuredHeight();

            // 从0高度开始展开
            ValueAnimator animator = ValueAnimator.ofInt(0, targetHeight);
            animator.setDuration(300);
            animator.addUpdateListener(animation -> {
                ViewGroup.LayoutParams params = targetView.getLayoutParams();
                params.height = (int) animation.getAnimatedValue();
                targetView.setLayoutParams(params);
            });
            animator.start();
        } else {
            // 从当前高度收起到0
            int startHeight = targetView.getHeight();
            ValueAnimator animator = ValueAnimator.ofInt(startHeight, 0);
            animator.setDuration(300);
            animator.addUpdateListener(animation -> {
                ViewGroup.LayoutParams params = targetView.getLayoutParams();
                params.height = (int) animation.getAnimatedValue();
                targetView.setLayoutParams(params);
            });
            animator.addListener(new android.animation.AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(android.animation.Animator animation) {
                    targetView.setVisibility(View.GONE);
                    ViewGroup.LayoutParams params = targetView.getLayoutParams();
                    params.height = ViewGroup.LayoutParams.WRAP_CONTENT;
                    targetView.setLayoutParams(params);
                }
            });
            animator.start();
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void finish() {
        super.finish();
        // 添加返回动画
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right);
    }
}
