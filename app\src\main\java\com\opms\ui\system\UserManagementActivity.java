package com.opms.ui.system;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.view.inputmethod.EditorInfo;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.google.android.material.snackbar.Snackbar;
import com.opms.R;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.UserListResponse;
import com.opms.data.model.response.UserResponse;
import com.opms.data.remote.ApiService;
import com.opms.databinding.ActivityUserManagementBinding;

import java.util.List;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class UserManagementActivity extends AppCompatActivity implements SwipeRefreshLayout.OnRefreshListener {
    private static final String TAG = "UserManagementActivity";
    private static final int REQUEST_EDIT_USER = 1001;
    private static final int PAGE_START = 1; // 服务器要求页码从1开始
    private static final int PAGE_SIZE = 10;
    @Inject
    ApiService apiService;
    private ActivityUserManagementBinding binding;
    private UserAdapter adapter;
    private LinearLayoutManager layoutManager;
    private int currentPage = PAGE_START;
    private boolean isLoading = false;
    private boolean isLastPage = false;
    private String currentQuery = "";
    private int totalPages = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityUserManagementBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // 设置返回按钮
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("用户管理");
        }

        setupSwipeRefresh();
        setupRecyclerView();
        setupSearchView();
        loadFirstPage();
    }

    private void setupSwipeRefresh() {
        binding.swipeRefresh.setOnRefreshListener(this);
        binding.swipeRefresh.setColorSchemeResources(
                R.color.primary,
                R.color.accent,
                R.color.primary_dark
        );
    }

    private void setupRecyclerView() {
        adapter = new UserAdapter(this);
        layoutManager = new LinearLayoutManager(this);
        binding.rvUsers.setLayoutManager(layoutManager);
        binding.rvUsers.setAdapter(adapter);

        // 设置滚动监听器，实现分页加载
        binding.rvUsers.addOnScrollListener(new PaginationScrollListener(layoutManager) {
            @Override
            protected void loadMoreItems() {
                isLoading = true;
                currentPage += 1;
                loadNextPage();
            }

            @Override
            public boolean isLoading() {
                return isLoading;
            }

            @Override
            public boolean isLastPage() {
                return isLastPage;
            }
        });

        adapter.setOnUserClickListener(user -> {
            Intent intent = new Intent(this, UserEditActivity.class);
            intent.putExtra(UserEditActivity.EXTRA_USERNAME, user.getUsername());
            startActivityForResult(intent, REQUEST_EDIT_USER);
            overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
        });
    }

    private void setupSearchView() {
        binding.etSearch.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                performSearch();
                return true;
            }
            return false;
        });

        binding.btnSearch.setOnClickListener(v -> performSearch());
    }

    private void performSearch() {
        String query = binding.etSearch.getText().toString().trim();
        currentQuery = query;
        resetPagination();
        loadFirstPage();
    }

    /**
     * 重置分页状态
     */
    private void resetPagination() {
        currentPage = PAGE_START;
        isLastPage = false;
        adapter.clear();
    }

    /**
     * 加载第一页数据
     */
    private void loadFirstPage() {
        showLoading(true);

        // 清空之前的数据
        adapter.clear();

        // 使用分页API
        apiService.getUsers(currentPage, PAGE_SIZE, currentQuery).enqueue(new Callback<ApiResponse<UserListResponse>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<UserListResponse>> call, @NonNull Response<ApiResponse<UserListResponse>> response) {
                hideLoading();

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<UserListResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        UserListResponse userListResponse = apiResponse.getData();
                        List<UserResponse> users = userListResponse.getList();

                        // 更新总页数
                        totalPages = userListResponse.getTotal();

                        // 服务器已经处理了分页和搜索，直接使用返回的数据
                        if (users.isEmpty()) {
                            showEmptyView();
                        } else {
                            hideEmptyView();
                            adapter.setUserList(users);

                            // 计算是否是最后一页
                            isLastPage = (userListResponse.getPage() * userListResponse.getSize() >= userListResponse.getTotal());
                            if (!isLastPage) {
                                adapter.addLoadingFooter();
                            }
                        }
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "获取用户列表失败");
                        showEmptyView();
                    }
                } else {
                    showError("获取用户列表失败");
                    showEmptyView();
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<UserListResponse>> call, @NonNull Throwable t) {
                hideLoading();
                Log.e(TAG, "获取用户列表失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
                showEmptyView();
            }
        });
    }

    /**
     * 加载下一页数据
     */
    private void loadNextPage() {
        // 使用分页API加载下一页
        apiService.getUsers(currentPage, PAGE_SIZE, currentQuery).enqueue(new Callback<ApiResponse<UserListResponse>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<UserListResponse>> call, @NonNull Response<ApiResponse<UserListResponse>> response) {
                // 移除加载中的footer
                adapter.removeLoadingFooter();
                isLoading = false;

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<UserListResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        UserListResponse userListResponse = apiResponse.getData();
                        List<UserResponse> users = userListResponse.getList();

                        // 服务器已经处理了分页和搜索，直接使用返回的数据
                        // 添加新数据
                        adapter.addUsers(users);

                        // 计算是否是最后一页
                        isLastPage = (userListResponse.getPage() * userListResponse.getSize() >= userListResponse.getTotal());

                        // 如果还有更多页，添加加载更多的footer
                        if (!isLastPage) {
                            adapter.addLoadingFooter();
                        }
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "加载更多用户失败");
                    }
                } else {
                    showError("加载更多用户失败");
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<UserListResponse>> call, @NonNull Throwable t) {
                adapter.removeLoadingFooter();
                isLoading = false;
                Log.e(TAG, "加载更多用户失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    // 服务器已经处理了搜索，不再需要客户端过滤

    /**
     * 显示空视图
     */
    private void showEmptyView() {
        binding.tvEmpty.setVisibility(View.VISIBLE);
        binding.rvUsers.setVisibility(View.GONE);
    }

    /**
     * 隐藏空视图
     */
    private void hideEmptyView() {
        binding.tvEmpty.setVisibility(View.GONE);
        binding.rvUsers.setVisibility(View.VISIBLE);
    }

    /**
     * 显示加载中
     */
    private void showLoading(boolean isLoading) {
        if (!binding.swipeRefresh.isRefreshing()) {
            binding.progressBar.setVisibility(isLoading ? View.VISIBLE : View.GONE);
        }
    }

    /**
     * 隐藏加载中
     */
    private void hideLoading() {
        binding.progressBar.setVisibility(View.GONE);
        if (binding.swipeRefresh.isRefreshing()) {
            binding.swipeRefresh.setRefreshing(false);
        }
    }

    /**
     * 显示错误信息
     */
    private void showError(String message) {
        Snackbar.make(binding.getRoot(), message, Snackbar.LENGTH_SHORT).show();
    }

    /**
     * 实现 SwipeRefreshLayout.OnRefreshListener 接口的方法
     * 下拉刷新回调
     */
    @Override
    public void onRefresh() {
        resetPagination();
        loadFirstPage();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_EDIT_USER && resultCode == RESULT_OK) {
            // 用户编辑或删除成功，刷新列表
            resetPagination();
            loadFirstPage();
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void finish() {
        super.finish();
        // 添加返回动画
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right);
    }
}
