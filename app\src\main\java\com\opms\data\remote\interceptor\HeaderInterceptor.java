package com.opms.data.remote.interceptor;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import java.io.IOException;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

public class HeaderInterceptor implements Interceptor {
    private final Context context;
    private final SharedPreferences preferences;

    public HeaderInterceptor(Context context) {
        this.context = context;
        this.preferences = context.getSharedPreferences("opms_prefs", Context.MODE_PRIVATE);
    }

    @Override
    public Response intercept(Chain chain) throws IOException {
        Request original = chain.request();
        String token = preferences.getString("token", "");
        String userName = preferences.getString("userName", "");
        Log.d("AuthInterceptor", "Token: " + token + ", UserName: " + userName); // 添加日志

        Request.Builder builder = original.newBuilder()
                .header("Content-Type", "application/json")
                .header("Accept", "application/json");

        if (!token.isEmpty()) {
            builder.header("Authorization", "Bearer " + token);
        }

        if (!userName.isEmpty()) {
            builder.header("X-User-Name", userName);
        }

        Request request = builder.build();
        return chain.proceed(request);
    }
} 