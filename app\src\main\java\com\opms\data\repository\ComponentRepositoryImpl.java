package com.opms.data.repository;

import com.opms.data.model.request.ComponentRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.ComponentListResponse;
import com.opms.data.model.response.ComponentResponse;
import com.opms.data.remote.ApiService;

import javax.inject.Inject;
import javax.inject.Singleton;

import retrofit2.Call;

@Singleton
public class ComponentRepositoryImpl implements ComponentRepository {

    private final ApiService apiService;

    @Inject
    public ComponentRepositoryImpl(ApiService apiService) {
        this.apiService = apiService;
    }

    @Override
    public Call<ApiResponse<ComponentListResponse>> getComponentList(int page, int size, String keyword) {
        return apiService.getComponentList(page, size, keyword);
    }

    @Override
    public Call<ApiResponse<ComponentResponse>> getComponentDetail(int id) {
        return apiService.getComponentDetail(id);
    }

    @Override
    public Call<ApiResponse<ComponentResponse>> createComponent(ComponentRequest request) {
        return apiService.createComponent(request);
    }

    @Override
    public Call<ApiResponse<ComponentResponse>> updateComponent(int id, ComponentRequest request) {
        return apiService.updateComponent(id, request);
    }

    @Override
    public Call<ApiResponse<Void>> deleteComponent(ComponentRequest request) {
        return apiService.deleteComponent(request);
    }
}
