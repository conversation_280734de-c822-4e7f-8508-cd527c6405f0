package com.opms.ui.register;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.widget.ArrayAdapter;
import android.widget.AutoCompleteTextView;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.google.android.material.textfield.TextInputEditText;
import com.opms.R;
import com.opms.api.ApiService;
import com.opms.common.enums.RoleType;
import com.opms.common.utils.ImageUtils;
import com.opms.data.local.entity.Registration;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class RegistrationDetailActivity extends AppCompatActivity {
    private static final String EXTRA_REGISTRATION_ID = "extra_registration_id";

    private ImageView ivUserAvatar;
    private TextInputEditText etUsername;
    private TextInputEditText etName;
    private TextInputEditText etIdCard;
    private TextInputEditText etPhone;
    private TextInputEditText etEmployeeId;
    private AutoCompleteTextView actRoleType;
    private AutoCompleteTextView actDepartment;
    private AutoCompleteTextView actPosition;
    private AutoCompleteTextView actJob;
    private TextInputEditText etRemark;
    private TextInputEditText etAuditComment;
    private Button btnApprove;
    private Button btnReject;

    private String registrationId;
    private Registration registration;

    public static void start(Context context, String registrationId) {
        Intent intent = new Intent(context, RegistrationDetailActivity.class);
        intent.putExtra(EXTRA_REGISTRATION_ID, registrationId);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_registration_detail);

        registrationId = getIntent().getStringExtra(EXTRA_REGISTRATION_ID);
        if (registrationId == null) {
            finish();
            return;
        }

        initViews();
        loadRegistration();
        setupDropdowns();
    }

    private void initViews() {
        ivUserAvatar = findViewById(R.id.iv_user_avatar);
        etUsername = findViewById(R.id.et_username);
        etName = findViewById(R.id.et_name);
        etIdCard = findViewById(R.id.et_id_card);
        etPhone = findViewById(R.id.et_phone);
        etEmployeeId = findViewById(R.id.et_employee_id);
        actRoleType = findViewById(R.id.act_role_type);
        actDepartment = findViewById(R.id.act_department);
        actPosition = findViewById(R.id.act_position);
        actJob = findViewById(R.id.act_job);
        etRemark = findViewById(R.id.et_remark);
        etAuditComment = findViewById(R.id.et_audit_comment);
        btnApprove = findViewById(R.id.btn_approve);
        btnReject = findViewById(R.id.btn_reject);

        btnApprove.setOnClickListener(v -> approveRegistration());
        btnReject.setOnClickListener(v -> rejectRegistration());
    }

    private void loadRegistration() {
        ApiService.getInstance().getRegistration(registrationId).enqueue(new Callback<Registration>() {
            @Override
            public void onResponse(Call<Registration> call, Response<Registration> response) {
                if (response.isSuccessful() && response.body() != null) {
                    registration = response.body();
                    displayRegistration();
                }
            }

            @Override
            public void onFailure(Call<Registration> call, Throwable t) {
                Toast.makeText(RegistrationDetailActivity.this, "加载失败：" + t.getMessage(), Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void displayRegistration() {
        if (registration == null) return;

        ImageUtils.loadUserAvatar(this, registration.getAvatar(), ivUserAvatar);

        etUsername.setText(registration.getUsername());
        etName.setText(registration.getName());
        etIdCard.setText(registration.getIdCard());
        etPhone.setText(registration.getPhone());
        etEmployeeId.setText(registration.getEmployeeId());
        actRoleType.setText(registration.getRole());
        actDepartment.setText(registration.getDepartment());
        actPosition.setText(registration.getPosition());
        actJob.setText(registration.getPost());
        etRemark.setText(registration.getRemark());
        etAuditComment.setText(registration.getAuditOpinion());
    }

    private void setupDropdowns() {
        // TODO: Load data from API
        String[] roleTypes = {RoleType.ADMIN.getCode(), RoleType.USER.getCode()};
        String[] departments = {"生产部", "技术部", "质量部", "行政部"};
        String[] positions = {"经理", "主管", "员工"};
        String[] jobs = {"操作工", "质检员", "技术员", "文员"};

        ArrayAdapter<String> roleTypeAdapter = new ArrayAdapter<>(this, android.R.layout.simple_dropdown_item_1line, roleTypes);
        ArrayAdapter<String> departmentAdapter = new ArrayAdapter<>(this, android.R.layout.simple_dropdown_item_1line, departments);
        ArrayAdapter<String> positionAdapter = new ArrayAdapter<>(this, android.R.layout.simple_dropdown_item_1line, positions);
        ArrayAdapter<String> jobAdapter = new ArrayAdapter<>(this, android.R.layout.simple_dropdown_item_1line, jobs);

        actRoleType.setAdapter(roleTypeAdapter);
        actDepartment.setAdapter(departmentAdapter);
        actPosition.setAdapter(positionAdapter);
        actJob.setAdapter(jobAdapter);
    }

    private void approveRegistration() {
        String employeeId = etEmployeeId.getText().toString().trim();
        String roleType = actRoleType.getText().toString().trim();
        String department = actDepartment.getText().toString().trim();
        String position = actPosition.getText().toString().trim();
        String job = actJob.getText().toString().trim();
        String remark = etRemark.getText().toString().trim();
        String auditComment = etAuditComment.getText().toString().trim();

        if (TextUtils.isEmpty(employeeId) || TextUtils.isEmpty(roleType) || TextUtils.isEmpty(department)
                || TextUtils.isEmpty(position) || TextUtils.isEmpty(job)) {
            Toast.makeText(this, "请填写所有必填项", Toast.LENGTH_SHORT).show();
            return;
        }

        if (TextUtils.isEmpty(auditComment)) {
            Toast.makeText(this, "请输入审核意见", Toast.LENGTH_SHORT).show();
            return;
        }

        registration.setEmployeeId(employeeId);
        registration.setRole(roleType);
        registration.setDepartment(department);
        registration.setPosition(position);
        registration.setPost(job);
        registration.setRemark(remark);
        registration.setAuditOpinion(auditComment);
        registration.setStatus("APPROVED");

        ApiService.getInstance().updateRegistration(registration).enqueue(new Callback<Registration>() {
            @Override
            public void onResponse(Call<Registration> call, Response<Registration> response) {
                if (response.isSuccessful()) {
                    Toast.makeText(RegistrationDetailActivity.this, "审核通过", Toast.LENGTH_SHORT).show();
                    finish();
                } else {
                    Toast.makeText(RegistrationDetailActivity.this, "操作失败：" + response.message(), Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(Call<Registration> call, Throwable t) {
                Toast.makeText(RegistrationDetailActivity.this, "操作失败：" + t.getMessage(), Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void rejectRegistration() {
        String auditComment = etAuditComment.getText().toString().trim();

        if (TextUtils.isEmpty(auditComment)) {
            Toast.makeText(this, "请输入审核意见", Toast.LENGTH_SHORT).show();
            return;
        }

        registration.setAuditOpinion(auditComment);
        registration.setStatus("REJECTED");

        ApiService.getInstance().updateRegistration(registration).enqueue(new Callback<Registration>() {
            @Override
            public void onResponse(Call<Registration> call, Response<Registration> response) {
                if (response.isSuccessful()) {
                    Toast.makeText(RegistrationDetailActivity.this, "已拒绝申请", Toast.LENGTH_SHORT).show();
                    finish();
                } else {
                    Toast.makeText(RegistrationDetailActivity.this, "操作失败：" + response.message(), Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(Call<Registration> call, Throwable t) {
                Toast.makeText(RegistrationDetailActivity.this, "操作失败：" + t.getMessage(), Toast.LENGTH_SHORT).show();
            }
        });
    }
}