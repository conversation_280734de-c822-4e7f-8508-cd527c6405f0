package com.opms.ui.system.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.button.MaterialButton;
import com.google.android.material.switchmaterial.SwitchMaterial;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.opms.R;
import com.opms.data.model.response.ProcessTemplateResponse;

import java.util.ArrayList;
import java.util.List;

/**
 * 流程模板列表适配器
 */
public class ProcessTemplateAdapter extends RecyclerView.Adapter<ProcessTemplateAdapter.ViewHolder> {
    private final Context context;
    private List<ProcessTemplateResponse> processTemplates;
    private OnProcessTemplateClickListener onProcessTemplateClickListener;
    private int editingPosition = -1; // 当前编辑的位置

    public ProcessTemplateAdapter(Context context) {
        this.context = context;
        this.processTemplates = new ArrayList<>();
    }

    public void setOnProcessTemplateClickListener(OnProcessTemplateClickListener listener) {
        this.onProcessTemplateClickListener = listener;
    }

    public void setProcessTemplates(List<ProcessTemplateResponse> processTemplates) {
        this.processTemplates = processTemplates != null ? processTemplates : new ArrayList<>();
        this.editingPosition = -1; // 重置编辑状态
        notifyDataSetChanged();
    }

    public void enterEditMode(int position) {
        if (editingPosition != -1) {
            // 如果有其他项正在编辑，先退出编辑模式
            int oldPosition = editingPosition;
            editingPosition = -1;
            notifyItemChanged(oldPosition);
        }
        editingPosition = position;
        notifyItemChanged(position);
    }

    public void cancelEditing() {
        if (editingPosition != -1) {
            int position = editingPosition;
            editingPosition = -1;
            notifyItemChanged(position);
        }
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_process_template, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        ProcessTemplateResponse processTemplate = processTemplates.get(position);
        holder.bind(processTemplate, position);
    }

    @Override
    public int getItemCount() {
        return processTemplates.size();
    }

    /**
     * 流程模板点击监听器
     */
    public interface OnProcessTemplateClickListener {
        void onProcessTemplateEdit(ProcessTemplateResponse processTemplate, int position);

        void onProcessTemplateSave(ProcessTemplateResponse processTemplate, int position, String newName, String newDescription, int newSequence, boolean newStatus);

        void onProcessTemplateCancel(int position);

        void onProcessTemplateDelete(ProcessTemplateResponse processTemplate);

        void onProcessTemplateEditInNewPage(ProcessTemplateResponse processTemplate);
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        private final TextView tvName;
        private final TextView tvCode;
        private final TextView tvSequence;
        private final TextView tvDescription;
        private final TextView tvStatus;
        private final ImageView ivDelete;

        // 编辑模式控件
        private final LinearLayout llViewMode;
        private final LinearLayout llEditMode;
        private final TextInputLayout tilEditName;
        private final TextInputEditText etEditName;
        private final TextInputLayout tilEditDescription;
        private final TextInputEditText etEditDescription;
        private final TextInputLayout tilEditSequence;
        private final TextInputEditText etEditSequence;
        private final SwitchMaterial switchEditStatus;
        private final MaterialButton btnSave;
        private final MaterialButton btnCancel;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            tvName = itemView.findViewById(R.id.tv_name);
            tvCode = itemView.findViewById(R.id.tv_code);
            tvSequence = itemView.findViewById(R.id.tv_sequence);
            tvDescription = itemView.findViewById(R.id.tv_description);
            tvStatus = itemView.findViewById(R.id.tv_status);
            ivDelete = itemView.findViewById(R.id.iv_delete);

            // 编辑模式控件
            llViewMode = itemView.findViewById(R.id.ll_view_mode);
            llEditMode = itemView.findViewById(R.id.ll_edit_mode);
            tilEditName = itemView.findViewById(R.id.til_edit_name);
            etEditName = itemView.findViewById(R.id.et_edit_name);
            tilEditDescription = itemView.findViewById(R.id.til_edit_description);
            etEditDescription = itemView.findViewById(R.id.et_edit_description);
            tilEditSequence = itemView.findViewById(R.id.til_edit_sequence);
            etEditSequence = itemView.findViewById(R.id.et_edit_sequence);
            switchEditStatus = itemView.findViewById(R.id.switch_edit_status);
            btnSave = itemView.findViewById(R.id.btn_save);
            btnCancel = itemView.findViewById(R.id.btn_cancel);
        }

        public void bind(ProcessTemplateResponse processTemplate, int currentPosition) {
            if (editingPosition == currentPosition) {
                // 编辑模式
                llViewMode.setVisibility(View.GONE);
                llEditMode.setVisibility(View.VISIBLE);

                // 设置编辑字段的值
                etEditName.setText(processTemplate.getName());
                etEditDescription.setText(processTemplate.getDescription());
                etEditSequence.setText(String.valueOf(processTemplate.getSequence()));

                // 设置状态开关
                String status = processTemplate.getStatus();
                switchEditStatus.setChecked("ACTIVE".equals(status) || "1".equals(status));

                // 设置保存按钮点击事件
                btnSave.setOnClickListener(v -> {
                    String newName = etEditName.getText().toString().trim();
                    String newDescription = etEditDescription.getText().toString().trim();
                    String sequenceStr = etEditSequence.getText().toString().trim();
                    boolean newStatus = switchEditStatus.isChecked();

                    // 验证名称
                    if (newName.isEmpty()) {
                        tilEditName.setError("流程名称不能为空");
                        return;
                    }
                    tilEditName.setError(null);

                    // 验证序号
                    int newSequence;
                    if (sequenceStr.isEmpty()) {
                        tilEditSequence.setError("流程序号不能为空");
                        return;
                    }
                    try {
                        newSequence = Integer.parseInt(sequenceStr);
                        if (newSequence <= 0) {
                            tilEditSequence.setError("流程序号必须大于0");
                            return;
                        }
                    } catch (NumberFormatException e) {
                        tilEditSequence.setError("请输入有效的数字");
                        return;
                    }
                    tilEditSequence.setError(null);

                    if (onProcessTemplateClickListener != null) {
                        onProcessTemplateClickListener.onProcessTemplateSave(processTemplate, currentPosition, newName, newDescription, newSequence, newStatus);
                    }
                });

                // 设置取消按钮点击事件
                btnCancel.setOnClickListener(v -> {
                    if (onProcessTemplateClickListener != null) {
                        onProcessTemplateClickListener.onProcessTemplateCancel(currentPosition);
                    }
                });

            } else {
                // 查看模式
                llViewMode.setVisibility(View.VISIBLE);
                llEditMode.setVisibility(View.GONE);

                tvName.setText(processTemplate.getName());
                tvCode.setText(processTemplate.getCode());
                tvSequence.setText("序号: " + processTemplate.getSequence());
                tvDescription.setText(processTemplate.getDescription() != null ? processTemplate.getDescription() : "");

                // 设置状态
                String status = processTemplate.getStatus();
                if ("ACTIVE".equals(status) || "1".equals(status)) {
                    tvStatus.setText("启用");
                    tvStatus.setTextColor(context.getColor(R.color.success));
                    tvStatus.setBackgroundResource(R.drawable.bg_status_active);
                } else {
                    tvStatus.setText("禁用");
                    tvStatus.setTextColor(context.getColor(R.color.error));
                    tvStatus.setBackgroundResource(R.drawable.bg_status_inactive);
                }

                // 设置点击事件 - 进入编辑模式
                itemView.setOnClickListener(v -> {
                    if (onProcessTemplateClickListener != null) {
                        onProcessTemplateClickListener.onProcessTemplateEdit(processTemplate, currentPosition);
                    }
                });

                // 设置长按事件 - 跳转到编辑页面
                itemView.setOnLongClickListener(v -> {
                    if (onProcessTemplateClickListener != null) {
                        onProcessTemplateClickListener.onProcessTemplateEditInNewPage(processTemplate);
                    }
                    return true;
                });
            }

            // 设置删除按钮点击事件
            ivDelete.setOnClickListener(v -> {
                if (onProcessTemplateClickListener != null) {
                    onProcessTemplateClickListener.onProcessTemplateDelete(processTemplate);
                }
            });
        }
    }
}
