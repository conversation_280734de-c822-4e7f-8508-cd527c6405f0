package com.opms.ui.business;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.MenuItem;
import android.view.View;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.google.android.material.snackbar.Snackbar;
import com.opms.R;
import com.opms.data.model.request.ComponentRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.ComponentListResponse;
import com.opms.data.model.response.ComponentResponse;
import com.opms.data.repository.ComponentRepository;
import com.opms.databinding.ActivityComponentManagementBinding;
import com.opms.ui.business.adapter.ComponentAdapter;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class ComponentManagementActivity extends AppCompatActivity
        implements ComponentAdapter.OnComponentClickListener {

    private static final String TAG = "ComponentManagement";

    @Inject
    ComponentRepository componentRepository;

    private ActivityComponentManagementBinding binding;
    private ComponentAdapter adapter;
    private List<ComponentResponse> allComponents;
    private List<ComponentResponse> filteredComponents;
    private boolean isToolbarExpanded = false;

    // 分页相关变量
    private int currentPage = 1;
    private int pageSize = 10;
    private boolean isLoading = false;
    private boolean hasMoreData = true;
    private String currentKeyword = "";

    private ActivityResultLauncher<Intent> editActivityLauncher;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityComponentManagementBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        initializeActivityLauncher();
        setupToolbar();
        setupRecyclerView();
        setupSearchFunctionality();
        setupButtons();
        setupToolbarToggle();

        loadComponents();
    }

    private void initializeActivityLauncher() {
        editActivityLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (result.getResultCode() == RESULT_OK) {
                        // 刷新列表
                        refreshComponents();
                    }
                }
        );
    }

    private void setupToolbar() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("部件管理");
        }
    }

    private void setupRecyclerView() {
        allComponents = new ArrayList<>();
        filteredComponents = new ArrayList<>();
        adapter = new ComponentAdapter(this);
        adapter.setOnComponentClickListener(this);

        binding.rvComponents.setLayoutManager(new LinearLayoutManager(this));
        binding.rvComponents.setAdapter(adapter);

        // 设置下拉刷新
        binding.swipeRefresh.setOnRefreshListener(this::refreshComponents);
    }

    private void setupSearchFunctionality() {
        binding.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                // 实时搜索功能
                performSearch();
            }
        });
    }

    private void setupButtons() {
        // 浮动操作按钮
        binding.fabAdd.setOnClickListener(v -> openAddComponentPage());

        // 清除搜索按钮
        binding.btnClearSearch.setOnClickListener(v -> clearSearch());
    }

    private void setupToolbarToggle() {
        // 默认收起工具栏
        binding.cardExpandableTools.setVisibility(View.GONE);
        binding.btnToggleToolbar.setIcon(getDrawable(R.drawable.ic_expand_more));
        isToolbarExpanded = false;

        binding.btnToggleToolbar.setOnClickListener(v -> toggleToolbar());
    }

    private void toggleToolbar() {
        if (isToolbarExpanded) {
            // 收起工具栏
            binding.cardExpandableTools.setVisibility(View.GONE);
            binding.btnToggleToolbar.setIcon(getDrawable(R.drawable.ic_expand_more));
        } else {
            // 展开工具栏
            binding.cardExpandableTools.setVisibility(View.VISIBLE);
            binding.btnToggleToolbar.setIcon(getDrawable(R.drawable.ic_expand_less));
        }
        isToolbarExpanded = !isToolbarExpanded;
    }

    private void loadComponents() {
        if (isLoading) return;

        showLoading(true);
        isLoading = true;

        Call<ApiResponse<ComponentListResponse>> call = componentRepository.getComponentList(
                currentPage, pageSize, currentKeyword);

        call.enqueue(new Callback<ApiResponse<ComponentListResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<ComponentListResponse>> call,
                                   Response<ApiResponse<ComponentListResponse>> response) {
                showLoading(false);
                isLoading = false;
                binding.swipeRefresh.setRefreshing(false);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<ComponentListResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        ComponentListResponse componentListResponse = apiResponse.getData();
                        if (componentListResponse != null) {
                            List<ComponentResponse> newComponents = componentListResponse.getList();
                            if (newComponents != null) {
                                if (currentPage == 1) {
                                    allComponents.clear();
                                }
                                allComponents.addAll(newComponents);
                                filteredComponents.clear();
                                filteredComponents.addAll(allComponents);
                                adapter.setComponents(filteredComponents);

                                // 更新部件数量显示
                                updateComponentCount();

                                // 检查是否还有更多数据
                                hasMoreData = newComponents.size() == pageSize;
                            }
                        }
                    } else {
                        showError("获取部件列表失败: " + apiResponse.getMessage());
                    }
                } else {
                    showError("网络请求失败，请检查网络连接");
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<ComponentListResponse>> call, Throwable t) {
                showLoading(false);
                isLoading = false;
                binding.swipeRefresh.setRefreshing(false);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    private void refreshComponents() {
        currentPage = 1;
        hasMoreData = true;
        loadComponents();
    }

    private void performSearch() {
        currentKeyword = binding.etSearch.getText().toString().trim();
        currentPage = 1;
        hasMoreData = true;
        loadComponents();
    }

    private void openAddComponentPage() {
        Intent intent = new Intent(this, ComponentEditActivity.class);
        editActivityLauncher.launch(intent);
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }

    private void showLoading(boolean show) {
        binding.progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
    }

    private void showError(String message) {
        Snackbar.make(binding.getRoot(), message, Snackbar.LENGTH_LONG).show();
    }

    private void clearSearch() {
        binding.etSearch.setText("");
        currentKeyword = "";
        currentPage = 1;
        hasMoreData = true;
        loadComponents();
    }

    private void updateComponentCount() {
        int count = allComponents != null ? allComponents.size() : 0;
        binding.tvComponentCount.setText("共 " + count + " 个部件");

        // 显示或隐藏空状态视图
        if (count == 0) {
            binding.llEmpty.setVisibility(View.VISIBLE);
            binding.rvComponents.setVisibility(View.GONE);
        } else {
            binding.llEmpty.setVisibility(View.GONE);
            binding.rvComponents.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onComponentDelete(ComponentResponse component) {
        showDeleteConfirmDialog(component);
    }

    @Override
    public void onComponentEditInNewPage(ComponentResponse component) {
        Intent intent = new Intent(this, ComponentEditActivity.class);
        intent.putExtra("component_id", (int) component.getId());
        editActivityLauncher.launch(intent);
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }

    private void showDeleteConfirmDialog(ComponentResponse component) {
        new MaterialAlertDialogBuilder(this)
                .setTitle("删除确认")
                .setMessage("确定要删除部件 \"" + component.getName() + "\" 吗？")
                .setPositiveButton("删除", (dialog, which) -> deleteComponent(component))
                .setNegativeButton("取消", null)
                .show();
    }

    private void deleteComponent(ComponentResponse component) {
        ComponentRequest request = new ComponentRequest();
        request.setId((int) component.getId());

        showLoading(true);

        Call<ApiResponse<Void>> call = componentRepository.deleteComponent(request);
        call.enqueue(new Callback<ApiResponse<Void>>() {
            @Override
            public void onResponse(Call<ApiResponse<Void>> call, Response<ApiResponse<Void>> response) {
                showLoading(false);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<Void> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        Snackbar.make(binding.getRoot(), "部件删除成功", Snackbar.LENGTH_SHORT).show();
                        refreshComponents();
                    } else {
                        showError("删除失败: " + apiResponse.getMessage());
                    }
                } else {
                    showError("网络请求失败，请检查网络连接");
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<Void>> call, Throwable t) {
                showLoading(false);
                showError("网络错误: " + t.getMessage());
            }
        });
    }
}
