<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base application theme. -->
    <style name="Theme.OPMS" parent="Theme.MaterialComponents.Light.DarkActionBar">
        <!-- Primary brand color -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/text_white</item>

        <!-- Secondary brand color -->
        <item name="colorSecondary">@color/accent</item>
        <item name="colorSecondaryVariant">@color/accent</item>
        <item name="colorOnSecondary">@color/text_white</item>

        <!-- Status bar color -->
        <item name="android:statusBarColor">@color/primary_dark</item>

        <!-- Background colors -->
        <item name="android:colorBackground">@color/background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnSurface">@color/text_primary</item>

        <!-- Text colors -->
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        <item name="android:textColorHint">@color/text_hint</item>

        <!-- Customize your theme here -->
        <item name="materialButtonStyle">@style/Widget.OPMS.Button</item>
        <item name="textInputStyle">@style/Widget.OPMS.TextInputLayout</item>
    </style>

    <!-- No ActionBar theme -->
    <style name="Theme.OPMS.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <!-- Button style -->
    <style name="Widget.OPMS.Button" parent="Widget.MaterialComponents.Button">
        <item name="android:textColor">@color/text_white</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="cornerRadius">4dp</item>
    </style>

    <!-- TextInputLayout style -->
    <style name="Widget.OPMS.TextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/primary</item>
        <item name="hintTextColor">@color/primary</item>
    </style>

    <!-- TextInputLayout style without dropdown icon -->
    <style name="Widget.OPMS.TextInputLayout.NoDropdownIcon" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu">
        <item name="boxStrokeColor">@color/primary</item>
        <item name="hintTextColor">@color/primary</item>
        <item name="endIconMode">none</item>
    </style>

    <!-- Card style -->
    <style name="Widget.OPMS.CardView" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">8dp</item>
        <item name="cardElevation">2dp</item>
        <item name="contentPadding">16dp</item>
    </style>

    <!-- BottomNavigationView style -->
    <style name="Widget.OPMS.BottomNavigationView" parent="Widget.MaterialComponents.BottomNavigationView">
        <item name="itemIconTint">@color/primary</item>
        <item name="itemTextColor">@color/primary</item>
    </style>
</resources>