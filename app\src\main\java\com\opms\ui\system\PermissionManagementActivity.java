package com.opms.ui.system;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.google.android.material.snackbar.Snackbar;
import com.opms.R;
import com.opms.data.model.request.PermissionRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.PermissionResponse;
import com.opms.data.repository.PermissionRepository;
import com.opms.databinding.ActivityPermissionManagementBinding;
import com.opms.ui.system.adapter.PermissionAdapter;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class PermissionManagementActivity extends AppCompatActivity
        implements SwipeRefreshLayout.OnRefreshListener, PermissionAdapter.OnPermissionClickListener {

    private static final String TAG = "PermissionManagement";

    @Inject
    PermissionRepository permissionRepository;

    private ActivityPermissionManagementBinding binding;
    private PermissionAdapter adapter;
    private List<PermissionResponse> allPermissions;
    private List<PermissionResponse> filteredPermissions;
    private boolean isToolbarExpanded = false;

    private ActivityResultLauncher<Intent> editActivityLauncher;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityPermissionManagementBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setupActivityResultLauncher();
        setupToolbar();
        setupRecyclerView();
        setupButtons();
        loadPermissions();
    }

    private void setupActivityResultLauncher() {
        editActivityLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (result.getResultCode() == RESULT_OK) {
                        // 刷新列表
                        loadPermissions();
                    }
                }
        );
    }

    private void startAddActivity() {
        Intent intent = new Intent(this, PermissionEditActivity.class);
        editActivityLauncher.launch(intent);
        // 添加滑动动画效果
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }

    private void setupToolbar() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("权限管理");
        }
    }

    private void setupRecyclerView() {
        adapter = new PermissionAdapter(this);
        adapter.setOnPermissionClickListener(this);

        binding.rvPermissions.setLayoutManager(new LinearLayoutManager(this));
        binding.rvPermissions.setAdapter(adapter);

        binding.swipeRefresh.setOnRefreshListener(this);
        binding.swipeRefresh.setColorSchemeResources(R.color.primary);
    }

    private void setupButtons() {
        // 搜索按钮
        binding.btnToggleSearch.setOnClickListener(v -> toggleSearchToolbar());

        // 展开工具栏按钮
        binding.btnToggleToolbar.setOnClickListener(v -> toggleToolbar());

        // 搜索框文本变化监听
        binding.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                filterPermissions(s.toString());
            }

            @Override
            public void afterTextChanged(Editable s) {
            }
        });

        // 新增按钮
        binding.fabAdd.setOnClickListener(v -> startAddActivity());
    }

    private void toggleSearchToolbar() {
        if (binding.cardExpandableTools.getVisibility() == View.VISIBLE) {
            binding.cardExpandableTools.setVisibility(View.GONE);
            binding.btnToggleSearch.setText("搜索");
        } else {
            binding.cardExpandableTools.setVisibility(View.VISIBLE);
            binding.btnToggleSearch.setText("收起");
            binding.etSearch.requestFocus();
        }
    }

    private void toggleToolbar() {
        isToolbarExpanded = !isToolbarExpanded;
        if (isToolbarExpanded) {
            binding.btnToggleToolbar.setIconResource(R.drawable.ic_expand_less);
            binding.cardExpandableTools.setVisibility(View.VISIBLE);
        } else {
            binding.btnToggleToolbar.setIconResource(R.drawable.ic_expand_more);
            binding.cardExpandableTools.setVisibility(View.GONE);
        }
    }

    private void filterPermissions(String query) {
        if (allPermissions == null) return;

        if (query.isEmpty()) {
            filteredPermissions = new ArrayList<>(allPermissions);
        } else {
            filteredPermissions = new ArrayList<>();
            String lowerQuery = query.toLowerCase();
            for (PermissionResponse permission : allPermissions) {
                if (permission.getName().toLowerCase().contains(lowerQuery) ||
                        permission.getCode().toLowerCase().contains(lowerQuery)) {
                    filteredPermissions.add(permission);
                }
            }
        }

        adapter.setPermissions(filteredPermissions);
        updatePermissionCount();
    }

    private void loadPermissions() {
        showLoading(true);
        permissionRepository.getPermissions().enqueue(new Callback<ApiResponse<List<PermissionResponse>>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<List<PermissionResponse>>> call,
                                   @NonNull Response<ApiResponse<List<PermissionResponse>>> response) {
                showLoading(false);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<List<PermissionResponse>> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        allPermissions = apiResponse.getData();
                        filteredPermissions = new ArrayList<>(allPermissions);
                        adapter.setPermissions(filteredPermissions);
                        updatePermissionCount();
                        hideEmptyView();
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "获取权限列表失败");
                        showEmptyView();
                    }
                } else {
                    showError("获取权限列表失败");
                    showEmptyView();
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<List<PermissionResponse>>> call, @NonNull Throwable t) {
                showLoading(false);
                Log.e(TAG, "获取权限列表失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
                showEmptyView();
            }
        });
    }

    private void updatePermissionCount() {
        int count = filteredPermissions != null ? filteredPermissions.size() : 0;
        binding.tvPermissionCount.setText("共 " + count + " 个权限");
    }

    private void showLoading(boolean show) {
        binding.progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
        binding.swipeRefresh.setRefreshing(false);
    }

    private void showError(String message) {
        Snackbar.make(binding.getRoot(), message, Snackbar.LENGTH_LONG).show();
    }

    private void showEmptyView() {
        binding.llEmpty.setVisibility(View.VISIBLE);
        binding.rvPermissions.setVisibility(View.GONE);
    }

    private void hideEmptyView() {
        binding.llEmpty.setVisibility(View.GONE);
        binding.rvPermissions.setVisibility(View.VISIBLE);
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onRefresh() {
        loadPermissions();
    }



    @Override
    public void onPermissionDelete(PermissionResponse permission) {
        showDeleteConfirmDialog(permission);
    }

    @Override
    public void onPermissionEditInNewPage(PermissionResponse permission) {
        Intent intent = new Intent(this, PermissionEditActivity.class);
        // 只传递权限ID，其他信息通过API获取
        intent.putExtra("permission_id", permission.getId());
        editActivityLauncher.launch(intent);
        // 添加滑动动画效果
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }



    private void showDeleteConfirmDialog(PermissionResponse permission) {
        new MaterialAlertDialogBuilder(this)
                .setTitle("删除确认")
                .setMessage("确定要删除权限 \"" + permission.getName() + "\" 吗？")
                .setPositiveButton("删除", (dialog, which) -> deletePermission(permission))
                .setNegativeButton("取消", null)
                .show();
    }

    private void deletePermission(PermissionResponse permission) {
        PermissionRequest request = new PermissionRequest();
        request.setId(permission.getId());
        request.setCode(permission.getCode());

        permissionRepository.deletePermission(request).enqueue(new Callback<ApiResponse<Void>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<Void>> call,
                                   @NonNull Response<ApiResponse<Void>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<Void> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        // 从本地数据中移除
                        if (allPermissions != null) {
                            allPermissions.removeIf(p -> p.getId() == permission.getId());
                            filterPermissions(binding.etSearch.getText().toString());
                        }
                        Snackbar.make(binding.getRoot(), "权限删除成功", Snackbar.LENGTH_SHORT).show();
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "删除权限失败");
                    }
                } else {
                    showError("删除权限失败");
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<Void>> call, @NonNull Throwable t) {
                Log.e(TAG, "删除权限失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    @Override
    public void finish() {
        super.finish();
        // 添加返回动画
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right);
    }
}
