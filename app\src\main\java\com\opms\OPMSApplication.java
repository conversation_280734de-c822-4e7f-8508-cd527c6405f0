package com.opms;

import android.app.Application;

import com.opms.common.utils.AvatarCacheUtils;
import com.opms.common.utils.ImageUtils;
import com.opms.data.repository.UserRepository;

import javax.inject.Inject;

import dagger.hilt.android.HiltAndroidApp;

@HiltAndroidApp
public class OPMSApplication extends Application {
    @Inject
    UserRepository userRepository;

    @Override
    public void onCreate() {
        super.onCreate();

        // 注意：在这里不要立即调用preloadUserAvatar，因为userRepository可能还未注入
        // 我们需要在注入完成后再调用
    }

    /**
     * 当依赖注入完成后，Hilt会自动调用这个方法
     * 这确保了userRepository已经被正确注入
     */
    @Inject
    void initializeApp() {
        // 设置UserRepository到ImageUtils
        ImageUtils.setUserRepository(userRepository);

        // 在应用启动时预加载用户头像
        preloadUserAvatar();
    }

    /**
     * 在应用启动时预加载用户头像
     * 使用智能加载方法：优先从本地缓存加载，缓存不存在时从服务器获取
     */
    private void preloadUserAvatar() {
        android.util.Log.d("OPMSApplication", "Preloading user avatar with smart loading");

        // 检查是否有缓存的头像
        if (!AvatarCacheUtils.hasCachedAvatar(this)) {
            android.util.Log.d("OPMSApplication", "No cached avatar found, will fetch from server when needed");
            // 不在这里主动获取，而是在实际需要显示头像时通过智能加载方法获取
        } else {
            android.util.Log.d("OPMSApplication", "Cached avatar found, ready for smart loading");
        }
    }
}