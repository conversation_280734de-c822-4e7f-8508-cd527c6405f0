rootProject.name = 'OrderProductionManagementSystem'
include ':app'

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.PREFER_SETTINGS)
    repositories {
        // 阿里云镜像
        maven { url 'https://maven.aliyun.com/repository/public/' }
        maven { url 'https://maven.aliyun.com/repository/google/' }
        maven { url 'https://maven.aliyun.com/repository/jcenter/' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin/' }
        // 华为云镜像
        maven { url 'https://repo.huaweicloud.com/repository/maven/' }
        // 腾讯云镜像
        maven { url 'https://mirrors.cloud.tencent.com/nexus/repository/maven-public/' }
        // 网易镜像
        maven { url 'https://mirrors.163.com/maven/repository/maven-public/' }
        google()
        mavenCentral()
    }
} 