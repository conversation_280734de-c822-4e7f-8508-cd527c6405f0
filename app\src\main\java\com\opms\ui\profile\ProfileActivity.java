package com.opms.ui.profile;

import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.util.Log;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AppCompatActivity;

import com.opms.R;
import com.opms.common.enums.Gender;
import com.opms.common.enums.RoleType;
import com.opms.common.utils.AvatarCacheUtils;
import com.opms.common.utils.ImageUtils;
import com.opms.common.utils.SharedPreferencesUtils;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.UserResponse;
import com.opms.data.repository.UserRepository;
import com.opms.ui.login.LoginActivity;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class ProfileActivity extends AppCompatActivity {
    @Inject
    UserRepository userRepository;
    private ImageView ivAvatar;
    private TextView tvUsername;
    private TextView tvName;
    private TextView tvGender;
    private TextView tvBirthday;
    private TextView tvIdCard;
    private TextView tvPhone;
    private TextView tvEmployeeId;
    private TextView tvRole;
    private TextView tvDepartment;
    private TextView tvPosition;
    private TextView tvJob;
    private TextView tvPermissionTemplate;
    private TextView tvRemark;
    private TextView tvRegisterTime;
    private Button btnLogout;

    // 定义头像编辑结果处理器
    private final ActivityResultLauncher<Intent> avatarEditLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            result -> {
                if (result.getResultCode() == RESULT_OK) {
                    // 刷新头像显示
                    if (ivAvatar != null) {
                        ImageUtils.loadUserAvatarFromCache(this, ivAvatar);
                    }
                }
            });

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_profile);

        initViews();
        setupListeners();
        loadUserProfile();
    }

    private void initViews() {
        ivAvatar = findViewById(R.id.iv_avatar);
        tvUsername = findViewById(R.id.tv_username);
        tvName = findViewById(R.id.tv_name);
        tvGender = findViewById(R.id.tv_gender);
        tvBirthday = findViewById(R.id.tv_birthday);
        tvIdCard = findViewById(R.id.tv_id_card);
        tvPhone = findViewById(R.id.tv_phone);
        tvEmployeeId = findViewById(R.id.tv_employee_id);
        tvRole = findViewById(R.id.tv_role);
        tvDepartment = findViewById(R.id.tv_department);
        tvPosition = findViewById(R.id.tv_position);
        tvJob = findViewById(R.id.tv_job);
        tvPermissionTemplate = findViewById(R.id.tv_permission_template);
        tvRemark = findViewById(R.id.tv_remark);
        tvRegisterTime = findViewById(R.id.tv_register_time);
        btnLogout = findViewById(R.id.btn_logout);
    }

    private void setupListeners() {
        // 设置头像点击事件
        if (ivAvatar != null) {
            ivAvatar.setOnClickListener(v -> {
                // 打开头像查看/编辑页面，允许编辑
                Intent intent = AvatarViewActivity.createIntent(this, true);
                avatarEditLauncher.launch(intent);
            });
        }

        btnLogout.setOnClickListener(v -> logout());
    }


    private void loadUserProfile() {
        userRepository.getUserProfile().enqueue(new Callback<ApiResponse<UserResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<UserResponse>> call, Response<ApiResponse<UserResponse>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<UserResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        UserResponse userResponse = apiResponse.getData();
                        updateUI(userResponse);
                    } else {
                        Toast.makeText(ProfileActivity.this, apiResponse.getMessage(), Toast.LENGTH_SHORT).show();
                    }
                } else {
                    Toast.makeText(ProfileActivity.this, "获取用户信息失败", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<UserResponse>> call, Throwable t) {
                Toast.makeText(ProfileActivity.this, "获取用户信息失败：" + t.getMessage(), Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void updateUI(UserResponse user) {
        Log.d("ProfileActivity", "Updating UI for user: " + user.getUsername());
        Log.d("ProfileActivity", "User info - Role: " + user.getRole() +
                                ", Department: " + user.getDepartment() +
                                ", DepartmentName: " + user.getDepartmentName() +
                                ", Position: " + user.getPosition() +
                                ", PositionName: " + user.getPositionName() +
                                ", Job: " + user.getJob() +
                                ", JobName: " + user.getJobName() +
                                ", PermissionTemplate: " + user.getPermissionTemplate());

        // 加载头像
        handleAvatarLoading(user, ivAvatar);

        // 更新用户信息
        tvUsername.setText("用户名：" + user.getUsername());
        tvName.setText("姓名：" + user.getName());
        tvGender.setText("性别：" + (Gender.MALE.getCode().equals(user.getGender()) ? "男" : "女"));
        tvBirthday.setText("出生日期：" + user.getBirthday());
        tvIdCard.setText("身份证号：" + user.getIdCard());
        tvPhone.setText("电话：" + user.getPhone());
        tvEmployeeId.setText("工号：" + user.getEmployeeId());

        // 角色显示 - 使用格式化显示文本
        String roleDisplay = getRoleTypeDisplayText(user);
        tvRole.setText("角色：" + roleDisplay);
        Log.d("ProfileActivity", "Set role: " + roleDisplay);

        // 部门显示 - 优先使用名称，如果为空则使用code
        String departmentDisplay = getDepartmentDisplayText(user);
        tvDepartment.setText("部门：" + departmentDisplay);
        Log.d("ProfileActivity", "Set department: " + departmentDisplay);

        // 职务显示 - 优先使用名称，如果为空则使用code
        String positionDisplay = getPositionDisplayText(user);
        tvPosition.setText("职位：" + positionDisplay);
        Log.d("ProfileActivity", "Set position: " + positionDisplay);

        // 岗位显示 - 优先使用名称，如果为空则使用code
        String jobDisplay = getJobDisplayText(user);
        tvJob.setText("岗位：" + jobDisplay);
        Log.d("ProfileActivity", "Set job: " + jobDisplay);

        // 权限模板显示
        String permissionDisplay = getPermissionTemplateDisplayText(user);
        tvPermissionTemplate.setText("权限模板：" + permissionDisplay);
        Log.d("ProfileActivity", "Set permission template: " + permissionDisplay);

        tvRemark.setText("备注：" + user.getRemark());
        tvRegisterTime.setText("注册时间：" + user.getRegisterTime());
    }

    /**
     * 获取角色类型显示文本
     */
    private String getRoleTypeDisplayText(UserResponse user) {
        // 如果用户的角色字段为空，直接返回
        if (user.getRole() == null || user.getRole().isEmpty()) {
            return "未设置";
        }

        // 尝试从RoleType枚举中查找匹配的角色类型
        for (RoleType roleType : RoleType.values()) {
            // 先尝试按code匹配
            if (roleType.getCode().equals(user.getRole())) {
                return roleType.getName();
            }
            // 再尝试按name匹配（以防服务器返回的是name）
            if (roleType.getName().equals(user.getRole())) {
                return roleType.getName();
            }
        }

        // 如果没有找到匹配的角色类型，返回原始值
        Log.w("ProfileActivity", "No matching role type found for: " + user.getRole());
        return user.getRole();
    }

    /**
     * 获取部门显示文本
     */
    private String getDepartmentDisplayText(UserResponse user) {
        // 优先使用部门名称
        if (user.getDepartmentName() != null && !user.getDepartmentName().isEmpty()) {
            return user.getDepartmentName();
        }

        // 如果部门名称为空，使用部门代码
        if (user.getDepartment() != null && !user.getDepartment().isEmpty()) {
            return user.getDepartment();
        }

        return "未设置";
    }

    /**
     * 获取职务显示文本
     */
    private String getPositionDisplayText(UserResponse user) {
        // 优先使用职务名称
        if (user.getPositionName() != null && !user.getPositionName().isEmpty()) {
            return user.getPositionName();
        }

        // 如果职务名称为空，使用职务代码
        if (user.getPosition() != null && !user.getPosition().isEmpty()) {
            return user.getPosition();
        }

        return "未设置";
    }

    /**
     * 获取岗位显示文本
     */
    private String getJobDisplayText(UserResponse user) {
        // 优先使用岗位名称
        if (user.getJobName() != null && !user.getJobName().isEmpty()) {
            return user.getJobName();
        }

        // 如果岗位名称为空，使用岗位代码
        if (user.getJob() != null && !user.getJob().isEmpty()) {
            return user.getJob();
        }

        return "未设置";
    }

    /**
     * 获取权限模板显示文本
     */
    private String getPermissionTemplateDisplayText(UserResponse user) {
        // 直接返回权限模板，如果为空则显示"未设置"
        if (user.getPermissionTemplate() != null && !user.getPermissionTemplate().isEmpty()) {
            return user.getPermissionTemplate();
        }

        return "未设置";
    }

    /**
     * 处理头像加载逻辑
     *
     * @param user      用户信息
     * @param imageView 目标ImageView
     */
    private void handleAvatarLoading(UserResponse user, ImageView imageView) {
        if (imageView == null) {
            Log.e("ProfileActivity", "ImageView is null in handleAvatarLoading");
            return;
        }

        Log.d("ProfileActivity", "Starting to handle avatar loading");

        // 首先尝试从本地缓存加载
        if (AvatarCacheUtils.hasCachedAvatar(this)) {
            Log.d("ProfileActivity", "Loading avatar from cache");
            ImageUtils.loadUserAvatarFromCache(this, imageView);
            return;
        }

        // 如果没有缓存，检查avatarUrl是否为Base64编码的图像
        String avatarUrl = user.getAvatarUrl();
        Log.d("ProfileActivity", "No cached avatar found, checking avatarUrl: " +
                (avatarUrl != null ? (avatarUrl.length() > 50 ? avatarUrl.substring(0, 50) + "..." : avatarUrl) : "null"));

        if (avatarUrl != null && !avatarUrl.isEmpty()) {
            // 检查是否为Base64编码的图像
            boolean isBase64 = isBase64Image(avatarUrl);
            Log.d("ProfileActivity", "Is avatarUrl Base64 encoded: " + isBase64);

            if (isBase64) {
                // 直接尝试解码并显示
                Bitmap bitmap = AvatarCacheUtils.decodeBase64ToBitmap(avatarUrl);
                if (bitmap != null) {
                    Log.d("ProfileActivity", "Successfully decoded Base64 to bitmap, displaying");
                    imageView.setImageBitmap(bitmap);

                    // 异步缓存图像
                    new Thread(() -> {
                        String cachedPath = AvatarCacheUtils.cacheBase64Avatar(this, avatarUrl);
                        Log.d("ProfileActivity", "Cached avatar path: " + cachedPath);
                    }).start();
                } else {
                    Log.e("ProfileActivity", "Failed to decode Base64 to bitmap");

                    // 尝试缓存Base64图像并显示
                    String cachedPath = AvatarCacheUtils.cacheBase64Avatar(this, avatarUrl);
                    if (cachedPath != null) {
                        Log.d("ProfileActivity", "Successfully cached Base64 image, loading from cache");
                        // 缓存成功，从缓存加载
                        ImageUtils.loadUserAvatarFromCache(this, imageView);
                    } else {
                        Log.e("ProfileActivity", "Failed to cache Base64 image, showing default avatar");
                        // 解码和缓存都失败，显示默认头像
                        imageView.setImageResource(R.drawable.ic_default_avatar);
                    }
                }
            } else {
                // 不是Base64编码，当作URL处理
                Log.d("ProfileActivity", "Loading avatar from URL");
                ImageUtils.loadUserAvatar(this, avatarUrl, imageView);
            }
        } else {
            // avatarUrl为空，尝试从服务器获取
            Log.d("ProfileActivity", "AvatarUrl is empty, fetching from server");
            ImageUtils.fetchAndCacheAvatarFromServer(this, imageView);
        }
    }

    /**
     * 判断字符串是否为Base64编码的图像
     *
     * @param str 要检查的字符串
     * @return 如果是Base64编码的图像则返回true，否则返回false
     */
    private boolean isBase64Image(String str) {
        // 检查是否以data:image开头（常见的Base64图像格式）
        if (str.startsWith("data:image")) {
            return true;
        }

        // 检查是否包含逗号（Base64数据通常有前缀和数据部分，用逗号分隔）
        if (str.contains(",")) {
            return true;
        }

        // 检查长度（Base64编码的图像通常很长）
        if (str.length() > 100 && !str.contains("http://") && !str.contains("https://")) {
            // 检查是否包含Base64字符集中的字符
            return str.matches("^[A-Za-z0-9+/=]+$");
        }

        return false;
    }

    private void logout() {
        // 清除本地存储的用户信息
        SharedPreferencesUtils.clearUserInfo(this);

        // 清除缓存的头像
        AvatarCacheUtils.clearCachedAvatar(this);

        // 跳转到登录页面
        Intent intent = new Intent(this, LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }
}