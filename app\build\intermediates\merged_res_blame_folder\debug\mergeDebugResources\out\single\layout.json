[{"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_image_preview.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_image_preview.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_process_template_management.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_process_template_management.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\item_loading.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\item_loading.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\dialog_department_edit.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\dialog_department_edit.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_profile.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_profile.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_customer_management.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_customer_management.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\fragment_user_audit.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\fragment_user_audit.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_password_change.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_password_change.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\fragment_profile_new.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\fragment_profile_new.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_user_audit.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_user_audit.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_process_post_mapping_edit.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_process_post_mapping_edit.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\item_department_tree.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\item_department_tree.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_position_edit.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_position_edit.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\item_business_module_checkbox.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\item_business_module_checkbox.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\item_position.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\item_position.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\item_component.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\item_component.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_permission_edit.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_permission_edit.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_login.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_login.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_post_management.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_post_management.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\item_permission.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\item_permission.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\fragment_system_management.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\fragment_system_management.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\item_product.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\item_product.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_component_management.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_component_management.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\fragment_todo.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\fragment_todo.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_position_management.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_position_management.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\fragment_business.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\fragment_business.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\item_pending_user.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\item_pending_user.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_post_edit.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_post_edit.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\item_process_post_mapping.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\item_process_post_mapping.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_process_post_mapping_management.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_process_post_mapping_management.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_department_management.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_department_management.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_order_management.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_order_management.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_product_management.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_product_management.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\item_user.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\item_user.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_main.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_main.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\item_customer.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\item_customer.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\item_registration.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\item_registration.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_user_audit_list.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_user_audit_list.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_user_management.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_user_management.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_customer_edit.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_customer_edit.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_product_edit.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_product_edit.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\item_process_template.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\item_process_template.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\item_product_component.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\item_product_component.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\item_multi_image.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\item_multi_image.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\item_post.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\item_post.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_register.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_register.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_component_edit.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_component_edit.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_profile_edit.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_profile_edit.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\fragment_profile_compact.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\fragment_profile_compact.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\layout_user_assignment.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\layout_user_assignment.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_process_template_edit.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_process_template_edit.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_splash.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_splash.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/item_product.xml", "source": "com.opms.app-main-73:/layout/item_product.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\fragment_profile.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\fragment_profile.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\fragment_system_manage.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\fragment_system_manage.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_user_edit.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_user_edit.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_registration_detail.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_registration_detail.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_permission_management.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_permission_management.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-mergeDebugResources-70:\\layout\\activity_avatar_view.xml", "source": "D:\\Android\\GradleRepository\\daemon\\8.11.1\\com.opms.app-main-73:\\layout\\activity_avatar_view.xml"}]