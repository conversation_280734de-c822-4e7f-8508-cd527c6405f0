package com.opms.common.enums;

public enum Gender {
    MALE("male", "男"),
    FEMALE("female", "女");

    private final String code;
    private final String name;

    Gender(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        for (Gender sde : Gender.values()) {
            if (code.equalsIgnoreCase(sde.getCode())) {
                return sde.getName();
            }
        }
        return null;
    }
}
