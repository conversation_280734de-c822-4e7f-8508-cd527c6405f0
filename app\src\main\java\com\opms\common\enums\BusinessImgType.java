package com.opms.common.enums;

/**
 * 业务图片分类
 */
public enum BusinessImgType {
    USER("user", "用户图片"),
    CUSTOMER("customer", "客户图片"),
    COMPONENT("component", "部件图片"),
    PRODUCT("product", "产品图片"),
    ORDER("order", "订单图片"),
    PROCESS("follow", "流程图片");

    private final String code;
    private final String name;

    BusinessImgType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        for (BusinessImgType sde : BusinessImgType.values()) {
            if (code.equalsIgnoreCase(sde.getCode())) {
                return sde.getName();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
