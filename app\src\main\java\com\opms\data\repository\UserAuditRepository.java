package com.opms.data.repository;

import com.opms.data.model.request.UserAuditRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.UserResponse;

import java.util.List;

import retrofit2.Call;

/**
 * 用户审核Repository接口
 */
public interface UserAuditRepository {

    /**
     * 获取所有审核用户列表（包括待审核、已通过、已拒绝）
     */
    Call<ApiResponse<List<UserResponse>>> getAllAuditUsers();

    /**
     * 获取待审核用户列表
     */
    Call<ApiResponse<List<UserResponse>>> getPendingUsers();

    /**
     * 根据ID获取用户详情（包括已审核的用户）
     */
    Call<ApiResponse<UserResponse>> getUserById(int id);

    /**
     * 根据ID获取待审核用户详情
     */
    Call<ApiResponse<UserResponse>> getPendingUserById(int id);

    /**
     * 审核用户
     */
    Call<ApiResponse<UserResponse>> auditUser(UserAuditRequest request);
}
