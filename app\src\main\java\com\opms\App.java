package com.opms;

import android.app.Application;
import android.util.Log;

import com.opms.common.constants.EnvironmentConfig;
import com.opms.common.utils.PreferenceManager;

public class App extends Application {
    private static final String TAG = "App";
    private static App instance;
    private PreferenceManager preferenceManager;

    @Override
    public void onCreate() {
        super.onCreate();
        instance = this;
        preferenceManager = PreferenceManager.getInstance(this);

        // 检查服务器连接
        checkServerConnection();
    }

    private void checkServerConnection() {
        if (!EnvironmentConfig.isServerReachable()) {
            Log.e(TAG, "Warning: Server is not reachable!");
            // 可以在这里添加重试逻辑或显示提示
        } else {
            Log.d(TAG, "Server is reachable");
        }
    }

    public static App getInstance() {
        return instance;
    }

    public PreferenceManager getPreferenceManager() {
        return preferenceManager;
    }

    public boolean isAuthenticated() {
        return preferenceManager.isLoggedIn() && 
               preferenceManager.getToken() != null && 
               preferenceManager.getUsername() != null;
    }

    public String getToken() {
        return preferenceManager.getToken();
    }

    public String getUsername() {
        return preferenceManager.getUsername();
    }

    public String getUserRole() {
        return preferenceManager.getUserRole();
    }

    public void logout() {
        preferenceManager.clearAuthData();
    }
} 