package com.opms.data.repository;

import com.opms.data.local.dao.ProcessDao;
import com.opms.data.local.entity.Process;
import com.opms.data.model.request.ProcessRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.ProcessResponse;
import com.opms.data.remote.ApiService;

import java.util.Collections;
import java.util.List;

import javax.inject.Inject;
import javax.inject.Singleton;

import retrofit2.Call;

@Singleton
public class ProcessRepositoryImpl implements ProcessRepository {
    private final ProcessDao dao;
    private final ApiService api;

    @Inject
    public ProcessRepositoryImpl(ProcessDao processDao, ApiService apiService) {
        this.dao = processDao;
        this.api = apiService;
    }

    public Call<ApiResponse<List<ProcessResponse>>> getProcesses() {
        return api.getProcesses();
    }

    public Call<ApiResponse<ProcessResponse>> createProcess(ProcessRequest request) {
        return api.createProcess(request);
    }

    public Call<ApiResponse<ProcessResponse>> updateProcess(int id, ProcessRequest request) {
        return api.updateProcess(id, request);
    }

    public Call<ApiResponse<Void>> deleteProcess(ProcessRequest request) {
        return api.deleteProcess(request);
    }

    public long insert(Process process) {
        return 0;
    }

    public void update(Process process) {

    }

    public void delete(Process process) {

    }

    public Process findByCode(String code) {
        return null;
    }

    public Process findById(int id) {
        return null;
    }

    public List<Process> getAllProcesses() {
        return Collections.emptyList();
    }
}