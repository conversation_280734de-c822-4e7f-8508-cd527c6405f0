<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_component"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:strokeColor="@color/divider"
    app:strokeWidth="1dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp">

        <!-- 组件图片 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_marginEnd="12dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <ImageView
                android:id="@+id/iv_component_image"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:src="@drawable/ic_component_management" />

        </com.google.android.material.card.MaterialCardView>

        <!-- 组件信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 组件名称和状态 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_component_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="组件名称"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_component_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:background="@drawable/bg_status_active"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="2dp"
                    android:text="启用"
                    android:textColor="@android:color/white"
                    android:textSize="12sp" />

            </LinearLayout>

            <!-- 组件编号 -->
            <TextView
                android:id="@+id/tv_component_code"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="编号: CMP001"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

            <!-- 型号和规格 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_component_model"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="型号: Model-A"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_component_standard"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="规格: 标准"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp" />

            </LinearLayout>

            <!-- 数量和操作按钮 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <!-- 数量显示/编辑 -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="数量: "
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <!-- 查看模式下的数量显示 -->
                    <TextView
                        android:id="@+id/tv_component_quantity"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="1"
                        android:textColor="@color/colorPrimary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <!-- 编辑模式下的数量输入 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_component_quantity"
                        android:layout_width="80dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:visibility="gone"
                        app:boxBackgroundMode="outline"
                        app:boxCornerRadiusBottomEnd="4dp"
                        app:boxCornerRadiusBottomStart="4dp"
                        app:boxCornerRadiusTopEnd="4dp"
                        app:boxCornerRadiusTopStart="4dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_component_quantity"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:maxLines="1"
                            android:text="1"
                            android:textSize="14sp" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

                <!-- 编辑按钮 -->
                <ImageView
                    android:id="@+id/btn_edit_component"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_marginEnd="8dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:clickable="true"
                    android:focusable="true"
                    android:padding="6dp"
                    android:src="@drawable/ic_edit"
                    android:visibility="gone"
                    app:tint="@color/colorPrimary" />

                <!-- 删除按钮 -->
                <ImageView
                    android:id="@+id/btn_delete_component"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:clickable="true"
                    android:focusable="true"
                    android:padding="6dp"
                    android:src="@drawable/ic_delete"
                    android:visibility="gone"
                    app:tint="@color/colorError" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
