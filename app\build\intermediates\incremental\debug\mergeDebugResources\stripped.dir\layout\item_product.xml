<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_product"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <!-- 产品信息 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- 左列：基本信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:paddingEnd="8dp">

            <!-- 产品名称和状态 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_product_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="产品名称"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_product_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:background="@drawable/bg_status_active"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="2dp"
                    android:text="启用"
                    android:textColor="@android:color/white"
                    android:textSize="12sp" />

            </LinearLayout>

            <!-- 产品编号 -->
            <TextView
                android:id="@+id/tv_product_code"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="编号: PRD001"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

            <!-- 型号 -->
            <TextView
                android:id="@+id/tv_product_model"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:text="型号: Model-A"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

        </LinearLayout>

        <!-- 右列：规格、价格和操作 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:paddingStart="8dp">

            <!-- 规格 -->
            <TextView
                android:id="@+id/tv_product_standard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="规格: 标准规格"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

            <!-- 价格 -->
            <TextView
                android:id="@+id/tv_product_price"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="￥999.00"
                android:textColor="@color/colorPrimary"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- 操作按钮 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="end"
                android:orientation="horizontal">

                <!-- 编辑按钮 -->
                <ImageView
                    android:id="@+id/btn_edit"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_marginEnd="8dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:clickable="true"
                    android:focusable="true"
                    android:padding="6dp"
                    android:src="@drawable/ic_edit"
                    app:tint="@color/colorPrimary" />

                <!-- 删除按钮 -->
                <ImageView
                    android:id="@+id/btn_delete"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:clickable="true"
                    android:focusable="true"
                    android:padding="6dp"
                    android:src="@drawable/ic_delete"
                    app:tint="@color/colorError" />

            </LinearLayout>

        </LinearLayout>

        </LinearLayout>

</com.google.android.material.card.MaterialCardView>
