package com.opms.ui.system.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.opms.R;
import com.opms.ui.system.model.DepartmentNode;

import java.util.ArrayList;
import java.util.List;

/**
 * 部门树形结构适配器
 */
public class DepartmentTreeAdapter extends RecyclerView.Adapter<DepartmentTreeAdapter.ViewHolder> {
    private final Context context;
    private List<DepartmentNode> visibleNodes;
    private List<DepartmentNode> rootNodes;
    private OnNodeClickListener onNodeClickListener;
    private DepartmentNode selectedNode;

    public DepartmentTreeAdapter(Context context) {
        this.context = context;
        this.visibleNodes = new ArrayList<>();
        this.rootNodes = new ArrayList<>();
    }

    public void setOnNodeClickListener(OnNodeClickListener listener) {
        this.onNodeClickListener = listener;
    }

    public void setRootNodes(List<DepartmentNode> rootNodes) {
        this.rootNodes = rootNodes;
        refreshVisibleNodes();
    }

    public DepartmentNode getSelectedNode() {
        return selectedNode;
    }

    public void setSelectedNode(DepartmentNode node) {
        this.selectedNode = node;
        notifyDataSetChanged();
    }

    /**
     * 刷新可见节点列表
     */
    private void refreshVisibleNodes() {
        visibleNodes.clear();
        for (DepartmentNode rootNode : rootNodes) {
            visibleNodes.addAll(rootNode.getVisibleNodes());
        }
        notifyDataSetChanged();
    }

    /**
     * 展开/折叠节点
     */
    public void toggleNode(DepartmentNode node) {
        if (node.hasChildren()) {
            node.toggleExpanded();
            refreshVisibleNodes();

            if (onNodeClickListener != null) {
                onNodeClickListener.onExpandToggle(node);
            }
        }
    }

    /**
     * 添加新节点
     */
    public void addNode(DepartmentNode parentNode, DepartmentNode newNode) {
        if (parentNode == null) {
            // 添加根节点
            rootNodes.add(newNode);
        } else {
            // 添加子节点
            parentNode.addChild(newNode);
            // 如果父节点未展开，则展开它
            if (!parentNode.isExpanded()) {
                parentNode.setExpanded(true);
            }
        }
        refreshVisibleNodes();
    }

    /**
     * 删除节点
     */
    public void removeNode(DepartmentNode node) {
        if (node.getParent() == null) {
            // 删除根节点
            rootNodes.remove(node);
        } else {
            // 删除子节点
            node.getParent().removeChild(node);
        }

        // 如果删除的是选中节点，清除选中状态
        if (selectedNode == node) {
            selectedNode = null;
        }

        refreshVisibleNodes();
    }

    /**
     * 更新节点
     */
    public void updateNode(DepartmentNode node) {
        refreshVisibleNodes();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_department_tree, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        DepartmentNode node = visibleNodes.get(position);
        holder.bind(node);
    }

    @Override
    public int getItemCount() {
        return visibleNodes.size();
    }

    public interface OnNodeClickListener {
        void onNodeClick(DepartmentNode node);

        void onNodeLongClick(DepartmentNode node);

        void onExpandToggle(DepartmentNode node);
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        private final View itemView;
        private final View indentView;
        private final ImageView ivExpand;
        private final TextView tvName;
        private final TextView tvCode;
        private final View selectedIndicator;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            this.itemView = itemView;
            this.indentView = itemView.findViewById(R.id.view_indent);
            this.ivExpand = itemView.findViewById(R.id.iv_expand);
            this.tvName = itemView.findViewById(R.id.tv_name);
            this.tvCode = itemView.findViewById(R.id.tv_code);
            this.selectedIndicator = itemView.findViewById(R.id.view_selected_indicator);
        }

        public void bind(DepartmentNode node) {
            // 设置缩进
            ViewGroup.LayoutParams params = indentView.getLayoutParams();
            params.width = node.getLevel() * 40; // 每级缩进40dp
            indentView.setLayoutParams(params);

            // 设置展开/折叠图标
            if (node.hasChildren()) {
                ivExpand.setVisibility(View.VISIBLE);
                ivExpand.setImageResource(node.isExpanded() ?
                        R.drawable.ic_expand_less : R.drawable.ic_expand_more);
                ivExpand.setOnClickListener(v -> toggleNode(node));
            } else {
                ivExpand.setVisibility(View.INVISIBLE);
            }

            // 设置部门信息
            tvName.setText(node.getDepartment().getName());
            tvCode.setText(node.getDepartment().getCode());

            // 设置选中状态
            boolean isSelected = node == selectedNode;
            selectedIndicator.setVisibility(isSelected ? View.VISIBLE : View.GONE);
            itemView.setBackgroundResource(isSelected ?
                    R.drawable.bg_item_selected : R.drawable.bg_item_normal);

            // 设置点击事件
            itemView.setOnClickListener(v -> {
                if (onNodeClickListener != null) {
                    setSelectedNode(node);
                    onNodeClickListener.onNodeClick(node);
                }
            });

            itemView.setOnLongClickListener(v -> {
                if (onNodeClickListener != null) {
                    setSelectedNode(node);
                    onNodeClickListener.onNodeLongClick(node);
                }
                return true;
            });
        }
    }
}
