package com.opms.ui.system.model;

import com.opms.data.model.response.DepartmentResponse;

import java.util.ArrayList;
import java.util.List;

/**
 * 部门树节点数据模型
 */
public class DepartmentNode {
    private DepartmentResponse department;
    private List<DepartmentNode> children;
    private DepartmentNode parent;
    private boolean isExpanded;
    private int level;

    public DepartmentNode(DepartmentResponse department) {
        this.department = department;
        this.children = new ArrayList<>();
        this.isExpanded = false;
        this.level = 0;
    }

    public DepartmentResponse getDepartment() {
        return department;
    }

    public void setDepartment(DepartmentResponse department) {
        this.department = department;
    }

    public List<DepartmentNode> getChildren() {
        return children;
    }

    public void setChildren(List<DepartmentNode> children) {
        this.children = children;
    }

    public void addChild(DepartmentNode child) {
        child.setParent(this);
        child.setLevel(this.level + 1);
        this.children.add(child);
    }

    public void removeChild(DepartmentNode child) {
        this.children.remove(child);
        child.setParent(null);
    }

    public DepartmentNode getParent() {
        return parent;
    }

    public void setParent(DepartmentNode parent) {
        this.parent = parent;
    }

    public boolean isExpanded() {
        return isExpanded;
    }

    public void setExpanded(boolean expanded) {
        isExpanded = expanded;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
        // 递归设置子节点的层级
        for (DepartmentNode child : children) {
            child.setLevel(level + 1);
        }
    }

    public boolean hasChildren() {
        return !children.isEmpty();
    }

    public boolean isRoot() {
        return parent == null;
    }

    public boolean isLeaf() {
        return children.isEmpty();
    }

    /**
     * 获取所有可见的节点（展开状态下的节点）
     */
    public List<DepartmentNode> getVisibleNodes() {
        List<DepartmentNode> visibleNodes = new ArrayList<>();
        visibleNodes.add(this);

        if (isExpanded) {
            for (DepartmentNode child : children) {
                visibleNodes.addAll(child.getVisibleNodes());
            }
        }

        return visibleNodes;
    }

    /**
     * 切换展开/折叠状态
     */
    public void toggleExpanded() {
        this.isExpanded = !this.isExpanded;
    }

    /**
     * 递归查找指定代码的节点
     */
    public DepartmentNode findNodeByCode(String code) {
        if (department != null && code.equals(department.getCode())) {
            return this;
        }

        for (DepartmentNode child : children) {
            DepartmentNode found = child.findNodeByCode(code);
            if (found != null) {
                return found;
            }
        }

        return null;
    }

    /**
     * 获取从根节点到当前节点的路径
     */
    public List<DepartmentNode> getPath() {
        List<DepartmentNode> path = new ArrayList<>();
        DepartmentNode current = this;

        while (current != null) {
            path.add(0, current);
            current = current.getParent();
        }

        return path;
    }

    @Override
    public String toString() {
        return department != null ? department.getName() : "Unknown";
    }
}
