<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- 标题 -->
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        android:text="添加部门"
        android:textColor="@color/text_primary"
        android:textSize="20sp"
        android:textStyle="bold" />

    <!-- 部门名称 -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/til_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="部门名称"
        app:boxStrokeColor="@color/primary"
        app:hintTextColor="@color/primary">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:maxLength="50" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- 部门代码 -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/til_code"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="部门代码"
        app:boxStrokeColor="@color/primary"
        app:hintTextColor="@color/primary">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_code"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textNoSuggestions"
            android:maxLength="20" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- 上级部门 -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/til_parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        android:hint="上级部门"
        app:boxStrokeColor="@color/primary"
        app:hintTextColor="@color/primary">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_parent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clickable="false"
            android:focusable="false"
            android:inputType="none"
            android:text="无（根部门）" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:orientation="horizontal">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_cancel"
            style="@style/Widget.MaterialComponents.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:text="取消" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_save"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="保存" />

    </LinearLayout>

</LinearLayout>
