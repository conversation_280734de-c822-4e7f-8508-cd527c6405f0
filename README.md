# 订单生产管理系统 (Order Production Management System)

## 项目结构
```
app/
├── build.gradle                 # 应用级别构建配置
├── proguard-rules.pro          # 混淆规则配置
└── src/
    └── main/
        ├── java/com/opms/      # 主要源代码目录
        │   ├── App.java        # 应用入口类
        │   ├── api/            # API接口定义
        │   ├── common/         # 公共工具类
        │   │   ├── constants/  # 常量定义
        │   │   ├── utils/      # 工具类
        │   │   └── widgets/    # 自定义控件
        │   ├── data/           # 数据层
        │   │   ├── local/      # 本地数据源
        │   │   ├── remote/     # 远程数据源
        │   │   ├── model/      # 数据模型
        │   │   └── repository/ # 数据仓库
        │   ├── di/             # 依赖注入
        │   ├── ui/             # 界面层
        │   │   ├── auth/       # 认证相关界面
        │   │   ├── admin/      # 管理员界面
        │   │   ├── user/       # 普通用户界面
        │   │   └── common/     # 公共界面组件
        │   └── viewmodel/      # ViewModel层
        └── res/                # 资源文件目录
            ├── layout/         # 布局文件
            ├── values/         # 资源值
            ├── drawable/       # 图片资源
            └── mipmap/        # 应用图标

build.gradle                    # 项目级别构建配置
settings.gradle                # 项目设置
gradle.properties              # Gradle配置
```

## 技术栈
- 开发语言：Java
- 最低支持：Android 12
- 目标SDK：35
- 架构模式：MVVM

## 主要功能模块
1. 认证模块
   - 登录
   - 注册
   - 用户认证状态管理

2. 管理员模块
   - 用户审核
   - 系统管理
     - 用户管理
     - 部门管理
     - 职位管理
     - 岗位管理
     - 流程管理
     - 权限管理
   - 个人信息管理

3. 用户模块
   - 待办事项
   - 业务处理
     - 订单列表
     - 录入订单
     - 订单排期
     - 订单跟进
     - 订单确认
     - 商品信息管理
     - 客户管理
   - 个人信息管理

## 数据模型
1. User (用户)
2. Department (部门)
3. Position (职位)
4. Post (岗位)
5. Process (流程)
6. Permission (权限)
7. Order (订单)
8. Product (商品)
9. Customer (客户)
10. ProcessNode (流程节点)
11. OrderTracking (订单跟进)

## 开发进度
- [ ] 项目初始化设置
- [ ] 基础架构搭建
- [ ] 认证模块开发
- [ ] 管理员模块开发
- [ ] 用户模块开发
- [ ] 测试与优化
- [ ] 文档完善

## 注意事项
1. 所有网络请求需要携带token和userName
2. 注意处理token过期情况
3. 注意数据验证和错误处理
4. 遵循Material Design设计规范
5. 注意代码规范和注释规范 