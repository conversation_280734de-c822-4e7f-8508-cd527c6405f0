package com.opms.data.repository;

import com.opms.data.local.entity.User;
import com.opms.data.model.request.ChangePasswordRequest;
import com.opms.data.model.request.LoginRequest;
import com.opms.data.model.request.RegisterRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.AvatarResponse;
import com.opms.data.model.response.LoginResponse;
import com.opms.data.model.response.RegisterResponse;
import com.opms.data.model.response.UserResponse;

import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import retrofit2.Call;

public interface UserRepository {
    Call<ApiResponse<LoginResponse>> login(LoginRequest request);

    Call<ApiResponse<RegisterResponse>> register(RegisterRequest request);

    Call<ApiResponse<RegisterResponse>> registerWithAvatar(
            RegisterRequest registerRequest,
            RequestBody imgRequest,
            MultipartBody.Part avatar);

    Call<ApiResponse<UserResponse>> getUserProfile();

    Call<ApiResponse<AvatarResponse>> getUserAvatar();

    Call<ApiResponse<UserResponse>> updateUserProfile(UserResponse request);

    Call<ApiResponse<Void>> changePassword(ChangePasswordRequest request);

    Call<ApiResponse<UserResponse>> checkUsernameExists(String username);

    long insert(User user);

    void update(User user);

    void delete(User user);

    UserResponse findByUsername(String username);
}