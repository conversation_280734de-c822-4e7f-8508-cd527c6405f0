package com.opms.data.local.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.opms.data.local.entity.OrderItem;

import java.util.List;

@Dao
public interface OrderItemDao {
    @Insert
    long insert(OrderItem orderItem);

    @Insert
    List<Long> insertAll(List<OrderItem> orderItems);

    @Update
    void update(OrderItem orderItem);

    @Delete
    void delete(OrderItem orderItem);

    @Query("SELECT * FROM order_items WHERE id = :id")
    OrderItem findById(long id);

    @Query("SELECT * FROM order_items WHERE orderId = :orderId")
    List<OrderItem> findByOrderId(long orderId);

    @Query("SELECT * FROM order_items WHERE productId = :productId")
    List<OrderItem> findByProductId(long productId);

    @Query("SELECT * FROM order_items")
    List<OrderItem> getAllOrderItems();

    @Query("DELETE FROM order_items WHERE orderId = :orderId")
    void deleteByOrderId(long orderId);
} 