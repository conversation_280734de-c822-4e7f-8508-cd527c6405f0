package com.opms.data.repository;

import com.opms.data.model.request.ProcessTemplateRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.ProcessTemplateResponse;
import com.opms.data.remote.ApiService;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Singleton;

import retrofit2.Call;

/**
 * 流程模板仓库实现类
 */
@Singleton
public class ProcessTemplateRepositoryImpl implements ProcessTemplateRepository {

    private final ApiService apiService;

    @Inject
    public ProcessTemplateRepositoryImpl(ApiService apiService) {
        this.apiService = apiService;
    }

    @Override
    public Call<ApiResponse<List<ProcessTemplateResponse>>> getProcessTemplates() {
        return apiService.getProcessTemplates();
    }

    @Override
    public Call<ApiResponse<ProcessTemplateResponse>> createProcessTemplate(ProcessTemplateRequest request) {
        return apiService.createProcessTemplate(request);
    }

    @Override
    public Call<ApiResponse<ProcessTemplateResponse>> updateProcessTemplate(int id, ProcessTemplateRequest request) {
        return apiService.updateProcessTemplate(id, request);
    }

    @Override
    public Call<ApiResponse<Void>> deleteProcessTemplate(ProcessTemplateRequest request) {
        return apiService.deleteProcessTemplate(request);
    }
}
