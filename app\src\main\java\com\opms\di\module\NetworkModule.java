package com.opms.di.module;

import android.content.Context;

import com.opms.common.constants.ApiConstants;
import com.opms.data.local.PreferencesManager;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.remote.ApiService;
import com.opms.data.remote.AuthInterceptor;
import com.opms.data.remote.interceptor.ResponseInterceptor;

import java.lang.annotation.Annotation;
import java.util.concurrent.TimeUnit;

import javax.inject.Singleton;

import dagger.Module;
import dagger.Provides;
import dagger.hilt.InstallIn;
import dagger.hilt.android.qualifiers.ApplicationContext;
import dagger.hilt.components.SingletonComponent;
import okhttp3.OkHttpClient;
import okhttp3.ResponseBody;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Converter;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

@Module
@InstallIn(SingletonComponent.class)
public class NetworkModule {

    @Provides
    @Singleton
    HttpLoggingInterceptor provideLoggingInterceptor() {
        HttpLoggingInterceptor interceptor = new HttpLoggingInterceptor();
        interceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
        return interceptor;
    }

    @Provides
    @Singleton
    AuthInterceptor provideAuthInterceptor(PreferencesManager preferencesManager) {
        return new AuthInterceptor(preferencesManager);
    }

    @Provides
    @Singleton
    @SuppressWarnings("unchecked")
    Converter<ResponseBody, ApiResponse<?>> provideResponseConverter() {
        return (Converter<ResponseBody, ApiResponse<?>>) GsonConverterFactory.create().responseBodyConverter(
                ApiResponse.class,
                new Annotation[0],
                null
        );
    }

    @Provides
    @Singleton
    ResponseInterceptor provideResponseInterceptor(
            @ApplicationContext Context context,
            Converter<ResponseBody, ApiResponse<?>> converter) {
        return new ResponseInterceptor(context, converter);
    }

    @Provides
    @Singleton
    OkHttpClient provideOkHttpClient(
            @ApplicationContext Context context,
            HttpLoggingInterceptor loggingInterceptor,
            AuthInterceptor authInterceptor,
            ResponseInterceptor responseInterceptor) {
        return new OkHttpClient.Builder()
                .addInterceptor(loggingInterceptor)
                .addInterceptor(authInterceptor)
                .addInterceptor(responseInterceptor)
                .connectTimeout(ApiConstants.TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(ApiConstants.TIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(ApiConstants.TIMEOUT, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .build();
    }

    @Provides
    @Singleton
    Retrofit provideRetrofit(OkHttpClient okHttpClient) {
        return new Retrofit.Builder()
                .baseUrl(ApiConstants.BASE_URL)
                .client(okHttpClient)
                .addConverterFactory(GsonConverterFactory.create())
                .build();
    }

    @Provides
    @Singleton
    ApiService provideApiService(Retrofit retrofit) {
        return retrofit.create(ApiService.class);
    }
} 