package com.opms.ui.common;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.google.android.material.snackbar.Snackbar;
import com.opms.R;
import com.opms.databinding.ActivityImagePreviewBinding;

/**
 * 图片预览Activity
 * 支持URL和URI两种图片源
 */
public class ImagePreviewActivity extends AppCompatActivity {
    private static final String TAG = "ImagePreviewActivity";
    private static final String EXTRA_IMAGE_URL = "image_url";
    private static final String EXTRA_IMAGE_URI = "image_uri";
    private static final String EXTRA_TITLE = "title";

    private ActivityImagePreviewBinding binding;
    private String imageUrl;
    private Uri imageUri;
    private String title;

    /**
     * 启动图片预览Activity（URL方式）
     */
    public static void startWithUrl(Context context, String imageUrl, String title) {
        Intent intent = new Intent(context, ImagePreviewActivity.class);
        intent.putExtra(EXTRA_IMAGE_URL, imageUrl);
        intent.putExtra(EXTRA_TITLE, title);
        context.startActivity(intent);
    }

    /**
     * 启动图片预览Activity（URI方式）
     */
    public static void startWithUri(Context context, Uri imageUri, String title) {
        Intent intent = new Intent(context, ImagePreviewActivity.class);
        intent.putExtra(EXTRA_IMAGE_URI, imageUri.toString());
        intent.putExtra(EXTRA_TITLE, title);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityImagePreviewBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // 获取传入的参数
        getIntentData();

        // 设置工具栏
        setupToolbar();

        // 加载图片
        loadImage();

        // 设置点击事件
        setupClickListeners();
    }

    private void getIntentData() {
        Intent intent = getIntent();
        imageUrl = intent.getStringExtra(EXTRA_IMAGE_URL);
        String uriString = intent.getStringExtra(EXTRA_IMAGE_URI);
        if (uriString != null) {
            imageUri = Uri.parse(uriString);
        }
        title = intent.getStringExtra(EXTRA_TITLE);

        Log.d(TAG, "getIntentData: imageUrl=" + imageUrl + ", imageUri=" + imageUri + ", title=" + title);
    }

    private void setupToolbar() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle(title != null ? title : "图片预览");
        }
    }

    private void loadImage() {
        binding.progressBar.setVisibility(View.VISIBLE);

        try {
            if (imageUrl != null) {
                // 加载网络图片
                Log.d(TAG, "loadImage: 加载网络图片: " + imageUrl);
                Glide.with(this)
                        .load(imageUrl)
                        .diskCacheStrategy(DiskCacheStrategy.ALL)
                        .placeholder(R.drawable.ic_image_placeholder)
                        .error(R.drawable.ic_image_error)
                        .into(binding.photoView);
            } else if (imageUri != null) {
                // 加载本地图片
                Log.d(TAG, "loadImage: 加载本地图片: " + imageUri);
                Glide.with(this)
                        .load(imageUri)
                        .diskCacheStrategy(DiskCacheStrategy.NONE)
                        .placeholder(R.drawable.ic_image_placeholder)
                        .error(R.drawable.ic_image_error)
                        .into(binding.photoView);
            } else {
                Log.e(TAG, "loadImage: 没有有效的图片源");
                showError("没有有效的图片源");
                finish();
                return;
            }

            // 隐藏进度条
            binding.progressBar.setVisibility(View.GONE);

        } catch (Exception e) {
            Log.e(TAG, "loadImage: 加载图片失败", e);
            binding.progressBar.setVisibility(View.GONE);
            showError("加载图片失败: " + e.getMessage());
        }
    }

    private void setupClickListeners() {
        // 点击图片区域隐藏/显示工具栏
        binding.photoView.setOnPhotoTapListener((view, x, y) -> {
            toggleToolbarVisibility();
        });

        // 点击背景区域关闭预览
        binding.getRoot().setOnClickListener(v -> {
            finish();
        });
    }

    private void toggleToolbarVisibility() {
        if (binding.toolbar.getVisibility() == View.VISIBLE) {
            // 隐藏工具栏
            binding.toolbar.setVisibility(View.GONE);
            // 隐藏状态栏
            getWindow().getDecorView().setSystemUiVisibility(
                    View.SYSTEM_UI_FLAG_FULLSCREEN |
                            View.SYSTEM_UI_FLAG_HIDE_NAVIGATION |
                            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            );
        } else {
            // 显示工具栏
            binding.toolbar.setVisibility(View.VISIBLE);
            // 显示状态栏
            getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_VISIBLE);
        }
    }

    private void showError(String message) {
        Snackbar.make(binding.getRoot(), message, Snackbar.LENGTH_LONG).show();
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}
