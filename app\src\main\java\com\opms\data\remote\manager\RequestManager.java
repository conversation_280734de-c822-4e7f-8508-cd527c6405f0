package com.opms.data.remote.manager;

import android.content.Context;

import com.opms.data.model.response.ApiResponse;
import com.opms.data.remote.callback.ApiCallback;
import com.opms.data.remote.exception.ApiException;
import com.opms.data.remote.exception.NetworkException;
import com.opms.data.remote.util.NetworkUtils;

import java.io.IOException;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class RequestManager {
    private final Context context;

    public RequestManager(Context context) {
        this.context = context.getApplicationContext();
    }

    public <T> void execute(Call<ApiResponse<T>> call, ApiCallback<T> callback) {
        if (!NetworkUtils.isNetworkAvailable(context)) {
            callback.onError(-1, "网络不可用");
            return;
        }

        call.enqueue(new Callback<ApiResponse<T>>() {
            @Override
            public void onResponse(Call<ApiResponse<T>> call, Response<ApiResponse<T>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<T> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        callback.onSuccess(apiResponse.getData());
                    } else {
                        callback.onError(apiResponse.getCode(), apiResponse.getMessage());
                    }
                } else {
                    callback.onError(response.code(), "请求失败");
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<T>> call, Throwable t) {
                if (t instanceof IOException) {
                    callback.onError(-1, "网络连接失败");
                } else if (t instanceof ApiException) {
                    ApiException apiException = (ApiException) t;
                    callback.onError(apiException.getCode(), apiException.getMessage());
                } else if (t instanceof NetworkException) {
                    NetworkException networkException = (NetworkException) t;
                    callback.onError(networkException.getCode(), networkException.getMessage());
                } else {
                    callback.onError(-1, "未知错误");
                }
            }
        });
    }

    public void cancel(Call<?> call) {
        if (call != null && !call.isCanceled()) {
            call.cancel();
        }
    }
} 