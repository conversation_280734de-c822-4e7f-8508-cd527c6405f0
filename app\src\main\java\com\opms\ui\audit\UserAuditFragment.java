package com.opms.ui.audit;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.opms.R;
import com.opms.databinding.FragmentUserAuditBinding;
import com.opms.ui.system.UserAuditListActivity;

import dagger.hilt.android.AndroidEntryPoint;

@AndroidEntryPoint
public class UserAuditFragment extends Fragment {
    private FragmentUserAuditBinding binding;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentUserAuditBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        // 直接启动用户审核列表Activity
        startUserAuditListActivity();
    }

    private void startUserAuditListActivity() {
        if (getActivity() != null) {
            Intent intent = new Intent(getActivity(), UserAuditListActivity.class);
            startActivity(intent);
            // 添加淡入动画效果
            getActivity().overridePendingTransition(R.anim.fade_in, R.anim.fade_out);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}