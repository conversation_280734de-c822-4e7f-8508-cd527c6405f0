package com.opms.data.model.request;

import com.google.gson.annotations.SerializedName;

/**
 * 流程岗位映射请求模型
 */
public class ProcessPostMappingRequest {
    @SerializedName("id")
    private int id;

    @SerializedName("processId")
    private int processId;

    @SerializedName("postId")
    private int postId;

    @SerializedName("status")
    private String status;

    @SerializedName("remark")
    private String remark;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getProcessId() {
        return processId;
    }

    public void setProcessId(int processId) {
        this.processId = processId;
    }

    public int getPostId() {
        return postId;
    }

    public void setPostId(int postId) {
        this.postId = postId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "ProcessPostMappingRequest{" +
                "id=" + id +
                ", processId=" + processId +
                ", postId=" + postId +
                ", status='" + status + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
}
