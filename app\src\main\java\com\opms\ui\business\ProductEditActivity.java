package com.opms.ui.business;

import android.Manifest;
import android.content.ClipData;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import com.bumptech.glide.Glide;
import com.google.android.material.snackbar.Snackbar;
import com.opms.R;
import com.opms.common.enums.BusinessImgType;
import com.opms.common.utils.ImageUploadUtils;
import com.opms.common.utils.MultiImageManager;
import com.opms.data.model.request.ProductRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.ProductCompleteResponse;
import com.opms.data.model.response.ProductCompositionResponse;
import com.opms.data.model.response.ProductResponse;
import com.opms.data.repository.ComponentRepository;
import com.opms.data.repository.ImageUploadRepository;
import com.opms.data.repository.ProductRepository;
import com.opms.databinding.ActivityProductEditBinding;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class ProductEditActivity extends AppCompatActivity {

    private static final String TAG = "ProductEditActivity";

    @Inject
    ProductRepository productRepository;

    @Inject
    ImageUploadRepository imageUploadRepository;

    @Inject
    ComponentRepository componentRepository;

    private ActivityProductEditBinding binding;
    private int productId = -1;
    private boolean isEditMode = false;
    private MultiImageManager imageManager;
    private ActivityResultLauncher<Intent> imagePickerLauncher;
    private ActivityResultLauncher<String> permissionLauncher;

    // 组件相关
    private List<ProductCompositionResponse> componentList = new ArrayList<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityProductEditBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        getIntentData();
        setupImagePicker();
        setupToolbar();
        setupStatusSpinner();
        setupButtons();
        setupImageManager();

        if (isEditMode) {
            loadProductDetail();
        }
    }

    private void getIntentData() {
        Intent intent = getIntent();
        if (intent != null && intent.hasExtra("product_id")) {
            productId = intent.getIntExtra("product_id", -1);
            isEditMode = productId != -1;
        }
    }

    private void setupImagePicker() {
        Log.d(TAG, "setupImagePicker: 初始化图片选择器");

        // 权限请求launcher
        permissionLauncher = registerForActivityResult(
                new ActivityResultContracts.RequestPermission(),
                isGranted -> {
                    Log.d(TAG, "权限请求结果: " + isGranted);
                    if (isGranted) {
                        openImagePicker();
                    } else {
                        showError("需要存储权限才能选择图片");
                    }
                }
        );

        // 图片选择launcher
        imagePickerLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    Log.d(TAG, "图片选择器返回结果: " + result.getResultCode());
                    if (result.getResultCode() == RESULT_OK && result.getData() != null) {
                        List<Uri> imageUris = new ArrayList<>();

                        if (result.getData().getClipData() != null) {
                            // 多选
                            ClipData clipData = result.getData().getClipData();
                            for (int i = 0; i < clipData.getItemCount(); i++) {
                                Uri imageUri = clipData.getItemAt(i).getUri();
                                if (isValidImageUri(imageUri)) {
                                    imageUris.add(imageUri);
                                }
                            }
                        } else if (result.getData().getData() != null) {
                            // 单选
                            Uri imageUri = result.getData().getData();
                            if (isValidImageUri(imageUri)) {
                                imageUris.add(imageUri);
                            }
                        }

                        if (!imageUris.isEmpty()) {
                            Log.d(TAG, "选中 " + imageUris.size() + " 张图片");
                            imageManager.addImages(imageUris);
                        } else {
                            Log.w(TAG, "未选择有效的图片");
                            showError("未选择有效的图片");
                        }
                    } else {
                        Log.w(TAG, "图片选择被取消或失败");
                    }
                }
        );
    }

    private void setupToolbar() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle(isEditMode ? "编辑产品" : "新增产品");
        }
    }

    private void setupStatusSpinner() {
        String[] statusOptions = {"启用", "禁用"};
        ArrayAdapter<String> adapter = new ArrayAdapter<>(this,
                android.R.layout.simple_dropdown_item_1line, statusOptions);
        binding.spinnerStatus.setAdapter(adapter);
        binding.spinnerStatus.setText("启用", false);
    }

    private void setupButtons() {
        binding.btnSave.setOnClickListener(v -> saveProduct());
        binding.btnCancel.setOnClickListener(v -> finish());
        binding.btnAddComponent.setOnClickListener(v -> showComponentSelectionDialog());
    }

    private void setupImageManager() {
        Log.d(TAG, "setupImageManager: 设置多图片管理器，编辑模式: " + isEditMode);

        // 初始化多图片管理器
        imageManager = new MultiImageManager(this, binding.rvProductImages);

        // 设置配置
        imageManager.setup(
                imageUploadRepository,
                BusinessImgType.PRODUCT,
                String.valueOf(productId),
                getCurrentUser(),
                isEditMode
        );

        // 设置监听器
        imageManager.setOnImageActionListener(new MultiImageManager.OnImageActionListener() {
            @Override
            public void onAddImageClick() {
                Log.d(TAG, "点击添加图片按钮");
                checkPermissionAndOpenImagePicker();
            }

            @Override
            public void onImageClick(MultiImageManager.ImageItem item, int position) {
                Log.d(TAG, "点击图片: " + position);
                showImagePreview(item);
            }

            @Override
            public void onImageLongClick(MultiImageManager.ImageItem item, int position) {
                Log.d(TAG, "长按图片: " + position);
                showImageOptions(item, position);
            }

            @Override
            public void onImageDelete(MultiImageManager.ImageItem item, int position) {
                Log.d(TAG, "删除图片: " + position);
                confirmDeleteImage(item, position);
            }

            @Override
            public void onImageUploadProgress(int uploadedCount, int totalCount) {
                Log.d(TAG, "上传进度: " + uploadedCount + "/" + totalCount);
                updateUploadProgress(uploadedCount, totalCount);
            }

            @Override
            public void onImageUploadComplete(List<String> successUrls, List<String> failureMessages) {
                Log.d(TAG, "上传完成，成功: " + successUrls.size() + ", 失败: " + failureMessages.size());
                handleUploadComplete(successUrls, failureMessages);
            }
        });

        Log.d(TAG, "setupImageManager: 多图片管理器设置完成");
    }

    private void loadProductDetail() {
        showLoading(true);

        Call<ApiResponse<ProductCompleteResponse>> call = productRepository.getProductDetail(productId);
        call.enqueue(new Callback<ApiResponse<ProductCompleteResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<ProductCompleteResponse>> call,
                                   Response<ApiResponse<ProductCompleteResponse>> response) {
                showLoading(false);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<ProductCompleteResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        ProductCompleteResponse productComplete = apiResponse.getData();
                        ProductResponse product = productComplete.getProduct();
                        List<ProductCompositionResponse> componentList = productComplete.getComponentList();
                        if (product != null) {
                            fillProductData(product);
                        }
                        // 在编辑模式下总是显示组件信息，即使为空
                        if (isEditMode) {
                            fillProductComponentList(componentList);
                        }
                        // 加载产品图片
                        imageManager.loadImages();
                    } else {
                        showError("获取产品详情失败: " + apiResponse.getMessage());
                    }
                } else {
                    showError("网络请求失败，请检查网络连接");
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<ProductCompleteResponse>> call, Throwable t) {
                showLoading(false);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    private void fillProductData(ProductResponse product) {
        binding.etProductName.setText(product.getName());
        binding.etProductCode.setText(product.getCode());
        binding.etProductModel.setText(product.getModel());
        binding.etProductStandard.setText(product.getStandard());
        binding.etProductPrice.setText(String.valueOf(product.getPrice()));
        binding.spinnerStatus.setText(product.getStatus(), false);
        binding.etProductRemark.setText(product.getRemark());

        // 图片现在由 MultiImageManager 管理，不在这里处理
    }

    private void fillProductComponentList(List<ProductCompositionResponse> componentList) {
        Log.d(TAG, "fillProductComponentList: 填充组件列表，数量: " + (componentList != null ? componentList.size() : 0));

        this.componentList.clear();
        if (componentList != null) {
            this.componentList.addAll(componentList);
        }

        // 显示组件信息卡片
        binding.cardComponentInfo.setVisibility(View.VISIBLE);

        // 清空现有的组件视图
        binding.llComponentList.removeAllViews();

        if (this.componentList.isEmpty()) {
            // 显示空状态
            binding.llComponentEmpty.setVisibility(View.VISIBLE);
            binding.llComponentList.setVisibility(View.GONE);
        } else {
            // 隐藏空状态，显示组件列表
            binding.llComponentEmpty.setVisibility(View.GONE);
            binding.llComponentList.setVisibility(View.VISIBLE);

            // 为每个组件创建视图
            for (int i = 0; i < this.componentList.size(); i++) {
                ProductCompositionResponse composition = this.componentList.get(i);
                View componentView = createComponentItemView(composition, i);
                binding.llComponentList.addView(componentView);
            }
        }

        Log.d(TAG, "fillProductComponentList: 组件列表填充完成");
    }



    private View createComponentItemView(ProductCompositionResponse composition, int position) {
        View itemView = getLayoutInflater().inflate(R.layout.item_product_component, null);

        // 绑定视图
        TextView tvComponentName = itemView.findViewById(R.id.tv_component_name);
        TextView tvComponentCode = itemView.findViewById(R.id.tv_component_code);
        TextView tvComponentModel = itemView.findViewById(R.id.tv_component_model);
        TextView tvComponentStandard = itemView.findViewById(R.id.tv_component_standard);
        TextView tvComponentStatus = itemView.findViewById(R.id.tv_component_status);
        TextView tvComponentQuantity = itemView.findViewById(R.id.tv_component_quantity);

        // 编辑模式的控件
        View tilComponentQuantity = itemView.findViewById(R.id.til_component_quantity);
        EditText etComponentQuantity = itemView.findViewById(R.id.et_component_quantity);
        ImageView btnEditComponent = itemView.findViewById(R.id.btn_edit_component);
        ImageView btnDeleteComponent = itemView.findViewById(R.id.btn_delete_component);

        // 填充数据
        tvComponentName.setText(composition.getName());
        tvComponentCode.setText("编号: " + composition.getCode());
        tvComponentModel.setText("型号: " + (TextUtils.isEmpty(composition.getModel()) ? "无" : composition.getModel()));
        tvComponentStandard.setText("规格: " + (TextUtils.isEmpty(composition.getStandard()) ? "无" : composition.getStandard()));
        tvComponentQuantity.setText(String.valueOf(composition.getNumber()));

        // 设置状态
        String status = composition.getStatus();
        if ("启用".equals(status)) {
            tvComponentStatus.setBackgroundResource(R.drawable.bg_status_active);
            tvComponentStatus.setText("启用");
        } else {
            tvComponentStatus.setBackgroundResource(R.drawable.bg_status_inactive);
            tvComponentStatus.setText("禁用");
        }



        // 编辑模式下显示操作按钮
        if (isEditMode) {
            btnEditComponent.setVisibility(View.VISIBLE);
            btnDeleteComponent.setVisibility(View.VISIBLE);

            // 编辑数量
            btnEditComponent.setOnClickListener(v -> toggleQuantityEdit(itemView, composition, position));

            // 删除组件
            btnDeleteComponent.setOnClickListener(v -> showDeleteComponentDialog(composition, position));
        }

        return itemView;
    }

    private void saveProduct() {
        if (!validateInput()) {
            return;
        }

        ProductRequest request = createProductRequest();
        showLoading(true);

        Call<ApiResponse<ProductResponse>> call;
        if (isEditMode) {
            call = productRepository.updateProduct(productId, request);
        } else {
            call = productRepository.createProduct(request);
        }

        call.enqueue(new Callback<ApiResponse<ProductResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<ProductResponse>> call,
                                   Response<ApiResponse<ProductResponse>> response) {
                showLoading(false);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<ProductResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        String message = isEditMode ? "产品更新成功" : "产品创建成功";
                        Snackbar.make(binding.getRoot(), message, Snackbar.LENGTH_SHORT).show();

                        // 返回结果
                        setResult(RESULT_OK);
                        finish();
                    } else {
                        showError("保存失败: " + apiResponse.getMessage());
                    }
                } else {
                    showError("网络请求失败，请检查网络连接");
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<ProductResponse>> call, Throwable t) {
                showLoading(false);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    private boolean validateInput() {
        String name = binding.etProductName.getText().toString().trim();
        String code = binding.etProductCode.getText().toString().trim();
        String priceStr = binding.etProductPrice.getText().toString().trim();

        if (TextUtils.isEmpty(name)) {
            binding.etProductName.setError("产品名称不能为空");
            binding.etProductName.requestFocus();
            return false;
        }

        if (TextUtils.isEmpty(code)) {
            binding.etProductCode.setError("产品编码不能为空");
            binding.etProductCode.requestFocus();
            return false;
        }

        if (TextUtils.isEmpty(priceStr)) {
            binding.etProductPrice.setError("价格不能为空");
            binding.etProductPrice.requestFocus();
            return false;
        }

        try {
            double price = Double.parseDouble(priceStr);
            if (price < 0) {
                binding.etProductPrice.setError("价格不能为负数");
                binding.etProductPrice.requestFocus();
                return false;
            }
        } catch (NumberFormatException e) {
            binding.etProductPrice.setError("请输入有效的价格");
            binding.etProductPrice.requestFocus();
            return false;
        }

        return true;
    }

    private ProductRequest createProductRequest() {
        ProductRequest request = new ProductRequest();
        request.setId(productId);
        request.setName(binding.etProductName.getText().toString().trim());
        request.setCode(binding.etProductCode.getText().toString().trim());
        request.setModel(binding.etProductModel.getText().toString().trim());
        request.setStandard(binding.etProductStandard.getText().toString().trim());
        request.setPrice(Double.parseDouble(binding.etProductPrice.getText().toString().trim()));
        request.setStatus(binding.spinnerStatus.getText().toString());
        request.setRemark(binding.etProductRemark.getText().toString().trim());

        // 新增时不设置image字段，编辑时也不在这里设置（单独上传）
        // request.setImage(null);

        return request;
    }

    private void checkPermissionAndOpenImagePicker() {
        Log.d(TAG, "checkPermissionAndOpenImagePicker: 检查权限并打开图片选择器");

        // 检查权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 使用READ_MEDIA_IMAGES权限
            if (checkSelfPermission(Manifest.permission.READ_MEDIA_IMAGES) != PackageManager.PERMISSION_GRANTED) {
                Log.d(TAG, "请求READ_MEDIA_IMAGES权限");
                permissionLauncher.launch(Manifest.permission.READ_MEDIA_IMAGES);
                return;
            }
        } else {
            // Android 12及以下使用READ_EXTERNAL_STORAGE权限
            if (checkSelfPermission(Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                Log.d(TAG, "请求READ_EXTERNAL_STORAGE权限");
                permissionLauncher.launch(Manifest.permission.READ_EXTERNAL_STORAGE);
                return;
            }
        }

        openImagePicker();
    }

    private void openImagePicker() {
        Log.d(TAG, "openImagePicker: 启动多图片选择器");
        try {
            Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
            intent.setType("image/*");
            intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true);
            intent.addCategory(Intent.CATEGORY_OPENABLE);

            Intent chooser = Intent.createChooser(intent, "选择图片");
            imagePickerLauncher.launch(chooser);
        } catch (Exception e) {
            Log.e(TAG, "openImagePicker: 启动图片选择器失败", e);
            showError("无法打开图片选择器: " + e.getMessage());
        }
    }

    /**
     * 验证图片URI是否有效
     */
    private boolean isValidImageUri(Uri uri) {
        try {
            String mimeType = getContentResolver().getType(uri);
            return mimeType != null && mimeType.startsWith("image/");
        } catch (Exception e) {
            Log.e(TAG, "验证图片URI失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 显示图片预览
     */
    private void showImagePreview(MultiImageManager.ImageItem item) {
        Log.d(TAG, "显示图片预览");

        try {
            if (item.getImageUrl() != null) {
                // 预览服务器图片
                Log.d(TAG, "预览服务器图片: " + item.getImageUrl());
                com.opms.ui.common.ImagePreviewActivity.startWithUrl(this, item.getImageUrl(), "产品图片");
            } else if (item.getImageUri() != null) {
                // 预览本地图片
                Log.d(TAG, "预览本地图片: " + item.getImageUri());
                com.opms.ui.common.ImagePreviewActivity.startWithUri(this, item.getImageUri(), "产品图片");
            } else {
                Log.w(TAG, "图片项没有有效的URL或URI");
                showError("无法预览图片：图片源无效");
            }
        } catch (Exception e) {
            Log.e(TAG, "启动图片预览失败", e);
            showError("启动图片预览失败: " + e.getMessage());
        }
    }

    /**
     * 显示图片操作选项
     */
    private void showImageOptions(MultiImageManager.ImageItem item, int position) {
        String[] options = {"预览", "删除"};

        new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("图片操作")
                .setItems(options, (dialog, which) -> {
                    switch (which) {
                        case 0: // 预览
                            showImagePreview(item);
                            break;
                        case 1: // 删除
                            confirmDeleteImage(item, position);
                            break;
                    }
                })
                .show();
    }

    /**
     * 确认删除图片
     */
    private void confirmDeleteImage(MultiImageManager.ImageItem item, int position) {
        new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("删除图片")
                .setMessage("确定要删除这张图片吗？")
                .setPositiveButton("删除", (dialog, which) -> {
                    imageManager.deleteImage(item, position);
                })
                .setNegativeButton("取消", null)
                .show();
    }

    /**
     * 更新上传进度
     */
    private void updateUploadProgress(int uploadedCount, int totalCount) {
        String message = String.format("正在上传图片 %d/%d", uploadedCount, totalCount);
        Log.d(TAG, message);
        // 可以在这里显示进度条或状态信息
    }

    /**
     * 处理上传完成
     */
    private void handleUploadComplete(List<String> successUrls, List<String> failureMessages) {
        StringBuilder message = new StringBuilder();

        if (!successUrls.isEmpty()) {
            message.append("成功上传 ").append(successUrls.size()).append(" 张图片");
            Log.d(TAG, "上传成功的图片URLs: " + successUrls);
        }

        if (!failureMessages.isEmpty()) {
            if (message.length() > 0) {
                message.append("，");
            }
            message.append(failureMessages.size()).append(" 张图片上传失败");
            Log.e(TAG, "上传失败的图片: " + String.join(", ", failureMessages));
        }

        if (message.length() > 0) {
            Snackbar.make(binding.getRoot(), message.toString(), Snackbar.LENGTH_LONG).show();
        }

        // 上传完成后，无需手动重新加载，MultiImageManager会自动处理
        Log.d(TAG, "图片上传处理完成");
    }

    /**
     * 获取当前用户
     */
    private String getCurrentUser() {
        // 使用ImageUploadUtils中的统一方法获取当前用户
        return ImageUploadUtils.getCurrentUser(this);
    }

    private void showLoading(boolean show) {
        binding.progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
        binding.btnSave.setEnabled(!show);
    }

    private void showError(String message) {
        Snackbar.make(binding.getRoot(), message, Snackbar.LENGTH_LONG).show();
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    private void toggleQuantityEdit(View itemView, ProductCompositionResponse composition, int position) {
        TextView tvQuantity = itemView.findViewById(R.id.tv_component_quantity);
        View tilQuantity = itemView.findViewById(R.id.til_component_quantity);
        EditText etQuantity = itemView.findViewById(R.id.et_component_quantity);

        if (tilQuantity.getVisibility() == View.GONE) {
            // 切换到编辑模式
            tvQuantity.setVisibility(View.GONE);
            tilQuantity.setVisibility(View.VISIBLE);
            etQuantity.setText(String.valueOf(composition.getNumber()));
            etQuantity.requestFocus();

            // 设置完成编辑的监听
            etQuantity.setOnEditorActionListener((v, actionId, event) -> {
                saveQuantityEdit(itemView, composition, position);
                return true;
            });
        } else {
            // 保存编辑
            saveQuantityEdit(itemView, composition, position);
        }
    }

    private void saveQuantityEdit(View itemView, ProductCompositionResponse composition, int position) {
        TextView tvQuantity = itemView.findViewById(R.id.tv_component_quantity);
        View tilQuantity = itemView.findViewById(R.id.til_component_quantity);
        EditText etQuantity = itemView.findViewById(R.id.et_component_quantity);

        try {
            int newQuantity = Integer.parseInt(etQuantity.getText().toString().trim());
            if (newQuantity <= 0) {
                showError("数量必须大于0");
                return;
            }

            // 更新数据
            composition.setNumber(newQuantity);
            componentList.set(position, composition);

            // 更新显示
            tvQuantity.setText(String.valueOf(newQuantity));
            tvQuantity.setVisibility(View.VISIBLE);
            tilQuantity.setVisibility(View.GONE);

            Log.d(TAG, "saveQuantityEdit: 更新组件数量，位置: " + position + ", 新数量: " + newQuantity);
        } catch (NumberFormatException e) {
            showError("请输入有效的数量");
        }
    }

    private void showDeleteComponentDialog(ProductCompositionResponse composition, int position) {
        String componentName = composition != null ? composition.getName() : "未知组件";

        new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("删除确认")
                .setMessage("确定要删除组件 \"" + componentName + "\" 吗？")
                .setPositiveButton("删除", (dialog, which) -> removeComponent(position))
                .setNegativeButton("取消", null)
                .show();
    }

    private void removeComponent(int position) {
        if (position >= 0 && position < componentList.size()) {
            componentList.remove(position);
            // 重新填充组件列表
            fillProductComponentList(componentList);
            Log.d(TAG, "removeComponent: 删除组件，位置: " + position);
        }
    }

    private void showComponentSelectionDialog() {
        // TODO: 实现组件选择对话框
        // 这里可以显示一个对话框让用户选择要添加的组件
        showError("组件选择功能待实现");
    }

    @Override
    public void finish() {
        super.finish();
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right);
    }
}
