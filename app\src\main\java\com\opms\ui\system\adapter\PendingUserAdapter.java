package com.opms.ui.system.adapter;

import android.content.Context;
import android.graphics.Bitmap;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.google.android.material.button.MaterialButton;
import com.opms.R;
import com.opms.common.utils.AvatarCacheUtils;
import com.opms.common.utils.AvatarDebugUtils;
import com.opms.data.model.response.UserResponse;

import java.util.ArrayList;
import java.util.List;

/**
 * 待审核用户适配器
 */
public class PendingUserAdapter extends RecyclerView.Adapter<PendingUserAdapter.ViewHolder> {
    private final Context context;
    private List<UserResponse> users;
    private OnUserClickListener onUserClickListener;

    public PendingUserAdapter(Context context) {
        this.context = context;
        this.users = new ArrayList<>();
    }

    public void setOnUserClickListener(OnUserClickListener listener) {
        this.onUserClickListener = listener;
    }

    public void setUsers(List<UserResponse> users) {
        this.users = users != null ? users : new ArrayList<>();
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_pending_user, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        UserResponse user = users.get(position);
        holder.bind(user, position);
    }

    @Override
    public int getItemCount() {
        return users.size();
    }

    /**
     * 用户点击监听器
     */
    public interface OnUserClickListener {
        void onUserClick(UserResponse user, int position);

        void onAuditClick(UserResponse user, int position);
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        private final ImageView ivAvatar;
        private final TextView tvUsername;
        private final TextView tvName;
        private final TextView tvGender;
        private final TextView tvPhone;
        private final TextView tvRegisterTime;
        private final TextView tvStatus;
        private final MaterialButton btnAudit;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            ivAvatar = itemView.findViewById(R.id.iv_avatar);
            tvUsername = itemView.findViewById(R.id.tv_username);
            tvName = itemView.findViewById(R.id.tv_name);
            tvGender = itemView.findViewById(R.id.tv_gender);
            tvPhone = itemView.findViewById(R.id.tv_phone);
            tvRegisterTime = itemView.findViewById(R.id.tv_register_time);
            tvStatus = itemView.findViewById(R.id.tv_status);
            btnAudit = itemView.findViewById(R.id.btn_audit);
        }

        public void bind(UserResponse user, int position) {
            // 设置用户头像
            loadUserAvatar(user.getAvatarUrl());

            // 设置用户信息
            tvUsername.setText(user.getUsername());
            tvName.setText(user.getName());

            // 设置性别
            String gender = user.getGender();
            if ("MALE".equals(gender) || "男".equals(gender)) {
                tvGender.setText("男");
                tvGender.setTextColor(context.getColor(R.color.primary));
            } else if ("FEMALE".equals(gender) || "女".equals(gender)) {
                tvGender.setText("女");
                tvGender.setTextColor(context.getColor(R.color.error));
            } else {
                tvGender.setText("未知");
                tvGender.setTextColor(context.getColor(R.color.text_secondary));
            }

            // 设置手机号（脱敏显示）
            String phone = user.getPhone();
            if (phone != null && phone.length() >= 11) {
                String maskedPhone = phone.substring(0, 3) + "****" + phone.substring(7);
                tvPhone.setText(maskedPhone);
            } else {
                tvPhone.setText(phone);
            }

            // 根据状态显示不同的信息
            String status = user.getStatus();
            if ("1".equals(status)) { // 已通过，显示审核结果信息
                showAuditResultInfo(user);
            } else {
                // 设置注册时间
                String registerTime = user.getRegisterTime();
                if (registerTime != null && !registerTime.isEmpty()) {
                    tvRegisterTime.setText("注册时间：" + registerTime);
                } else {
                    tvRegisterTime.setText("注册时间：未知");
                }
            }

            // 设置状态
            switch (status) {
                case "0": // 待审核
                    tvStatus.setText("待审核");
                    tvStatus.setTextColor(context.getColor(R.color.warning));
                    tvStatus.setBackgroundResource(R.drawable.bg_status_pending);
                    btnAudit.setVisibility(View.VISIBLE);
                    btnAudit.setText("审核");
                    break;
                case "1": // 已通过
                    tvStatus.setText("已通过");
                    tvStatus.setTextColor(context.getColor(R.color.success));
                    tvStatus.setBackgroundResource(R.drawable.bg_status_active);
                    btnAudit.setVisibility(View.VISIBLE);
                    btnAudit.setText("查看");
                    break;
                case "-1": // 已拒绝
                    tvStatus.setText("已拒绝");
                    tvStatus.setTextColor(context.getColor(R.color.error));
                    tvStatus.setBackgroundResource(R.drawable.bg_status_inactive);
                    btnAudit.setVisibility(View.VISIBLE);
                    btnAudit.setText("查看");
                    break;
                default:
                    tvStatus.setText("未知");
                    tvStatus.setTextColor(context.getColor(R.color.text_secondary));
                    btnAudit.setVisibility(View.GONE);
                    break;
            }

            // 设置点击事件
            itemView.setOnClickListener(v -> {
                if (onUserClickListener != null) {
                    onUserClickListener.onUserClick(user, position);
                }
            });

            btnAudit.setOnClickListener(v -> {
                if (onUserClickListener != null) {
                    onUserClickListener.onAuditClick(user, position);
                }
            });
        }

        private void loadUserAvatar(String avatarData) {
            if (avatarData == null || avatarData.isEmpty()) {
                Log.d("PendingUserAdapter", "Avatar data is null or empty, using default avatar");
                ivAvatar.setImageResource(R.drawable.ic_person);
                return;
            }

            // 添加详细的调试信息
            AvatarDebugUtils.analyzeAvatarData(avatarData, "PendingUserAdapter");
            AvatarDebugUtils.testBase64Decode(avatarData, "PendingUserAdapter");

            Log.d("PendingUserAdapter", "Loading user avatar, data length: " + avatarData.length());
            Log.d("PendingUserAdapter", "Avatar data preview: " + (avatarData.length() > 50 ? avatarData.substring(0, 50) + "..." : avatarData));

            // 使用改进的Base64检测逻辑
            if (AvatarCacheUtils.isBase64Image(avatarData)) {
                Log.d("PendingUserAdapter", "Detected Base64 avatar, attempting to decode");
                // 直接解码并显示Base64图像
                Bitmap bitmap = AvatarCacheUtils.decodeBase64ToBitmap(avatarData);
                if (bitmap != null) {
                    Log.d("PendingUserAdapter", "Successfully decoded Base64 avatar, applying to ImageView");
                    try {
                        // 确保在主线程中设置ImageView
                        final Bitmap finalBitmap = bitmap;
                        if (android.os.Looper.myLooper() == android.os.Looper.getMainLooper()) {
                            // 已经在主线程中
                            setBitmapToImageView(finalBitmap);
                        } else {
                            // 切换到主线程
                            ivAvatar.post(new Runnable() {
                                @Override
                                public void run() {
                                    setBitmapToImageView(finalBitmap);
                                }
                            });
                        }
                    } catch (Exception e) {
                        Log.e("PendingUserAdapter", "Error setting bitmap to ImageView: " + e.getMessage(), e);
                        // 如果直接设置失败，尝试使用Glide
                        try {
                            Glide.with(context)
                                    .load(bitmap)
                                    .placeholder(R.drawable.ic_person)
                                    .error(R.drawable.ic_person)
                                    .circleCrop()
                                    .into(ivAvatar);
                            Log.d("PendingUserAdapter", "Successfully loaded bitmap with Glide");
                        } catch (Exception e2) {
                            Log.e("PendingUserAdapter", "Error loading bitmap with Glide: " + e2.getMessage(), e2);
                            ivAvatar.setImageResource(R.drawable.ic_person);
                        }
                    }
                } else {
                    Log.e("PendingUserAdapter", "Failed to decode Base64 avatar, using default");
                    ivAvatar.setImageResource(R.drawable.ic_person);
                }
            } else {
                Log.d("PendingUserAdapter", "Not Base64 format, treating as URL: " + avatarData);

                Log.d("PendingUserAdapter", "Processing avatar URL: " + avatarData);
                // 使用ImageUtils处理URL
                String processedImageUrl = com.opms.common.utils.ImageUtils.processImageUrl(avatarData);
                Log.d("PendingUserAdapter", "Processed image URL: " + processedImageUrl);

                // 使用Glide加载处理后的URL
                Glide.with(context)
                        .load(processedImageUrl)
                        .placeholder(R.drawable.ic_person)
                        .error(R.drawable.ic_person)
                        .circleCrop()
                        .listener(new com.bumptech.glide.request.RequestListener<android.graphics.drawable.Drawable>() {
                            @Override
                            public boolean onLoadFailed(@Nullable com.bumptech.glide.load.engine.GlideException e, Object model, com.bumptech.glide.request.target.Target<android.graphics.drawable.Drawable> target, boolean isFirstResource) {
                                Log.e("PendingUserAdapter", "Failed to load avatar from URL: " + model);
                                if (e != null) {
                                    Log.e("PendingUserAdapter", "Glide error details: " + e.getMessage());
                                    for (Throwable cause : e.getRootCauses()) {
                                        Log.e("PendingUserAdapter", "Root cause: " + cause.getMessage());
                                    }
                                }
                                return false;
                            }

                            @Override
                            public boolean onResourceReady(android.graphics.drawable.Drawable resource, Object model, com.bumptech.glide.request.target.Target<android.graphics.drawable.Drawable> target, com.bumptech.glide.load.DataSource dataSource, boolean isFirstResource) {
                                Log.d("PendingUserAdapter", "Successfully loaded avatar from URL: " + model + ", data source: " + dataSource);
                                return false;
                            }
                        })
                        .into(ivAvatar);
            }
        }

        private void setBitmapToImageView(Bitmap bitmap) {
            try {
                // 检查ImageView是否已经完成布局
                if (ivAvatar.getWidth() == 0 || ivAvatar.getHeight() == 0) {
                    Log.d("PendingUserAdapter", "ImageView not yet laid out, waiting for layout...");
                    // 等待ImageView完成布局后再设置图像
                    ivAvatar.getViewTreeObserver().addOnGlobalLayoutListener(new android.view.ViewTreeObserver.OnGlobalLayoutListener() {
                        @Override
                        public void onGlobalLayout() {
                            ivAvatar.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                            Log.d("PendingUserAdapter", "ImageView layout completed, setting bitmap");
                            setBitmapToImageViewInternal(bitmap);
                        }
                    });
                } else {
                    // ImageView已经完成布局，直接设置
                    setBitmapToImageViewInternal(bitmap);
                }
            } catch (Exception e) {
                Log.e("PendingUserAdapter", "Error in setBitmapToImageView: " + e.getMessage(), e);
                ivAvatar.setImageResource(R.drawable.ic_person);
            }
        }

        private void setBitmapToImageViewInternal(Bitmap bitmap) {
            try {
                // 获取ImageView的实际尺寸
                int viewWidth = ivAvatar.getWidth();
                int viewHeight = ivAvatar.getHeight();
                Log.d("PendingUserAdapter", "ImageView size: " + viewWidth + "x" + viewHeight);

                // 根据布局文件，ImageView在60dp的MaterialCardView中
                // 转换dp到px
                float density = context.getResources().getDisplayMetrics().density;
                int targetSizePx = (int) (60 * density); // 60dp转换为px

                // 如果ImageView尺寸仍然为0，使用布局定义的尺寸
                if (viewWidth == 0 || viewHeight == 0) {
                    viewWidth = viewHeight = targetSizePx;
                    Log.d("PendingUserAdapter", "Using layout defined size: " + viewWidth + "x" + viewHeight + " (60dp)");
                } else {
                    // 使用实际测量的尺寸，但不超过目标尺寸
                    targetSizePx = Math.min(Math.min(viewWidth, viewHeight), targetSizePx);
                    Log.d("PendingUserAdapter", "Using measured size: " + targetSizePx + "x" + targetSizePx);
                }

                // 缩放Bitmap到合适的尺寸
                Bitmap scaledBitmap = Bitmap.createScaledBitmap(bitmap, targetSizePx, targetSizePx, true);

                // 清除可能的tint滤镜（布局文件中设置的）
                Log.d("PendingUserAdapter", "Clearing tint and color filters");
                ivAvatar.setImageTintList(null);
                ivAvatar.setColorFilter(null);

                // 设置正确的scaleType - 由于我们已经缩放到正确尺寸，使用FIT_XY确保完全显示
                ivAvatar.setScaleType(android.widget.ImageView.ScaleType.FIT_XY);
                Log.d("PendingUserAdapter", "Set scaleType to FIT_XY");

                // 创建圆形Bitmap
                Bitmap circleBitmap = AvatarCacheUtils.createCircleBitmap(scaledBitmap);
                if (circleBitmap != null) {
                    ivAvatar.setImageBitmap(circleBitmap);
                    Log.d("PendingUserAdapter", "Successfully set circle bitmap to ImageView");

                    // 强制刷新ImageView
                    ivAvatar.invalidate();
                    ivAvatar.requestLayout();

                    // 验证ImageView状态
                    AvatarDebugUtils.verifyImageViewState(ivAvatar, "PendingUserAdapter");
                } else {
                    // 如果圆形裁剪失败，直接设置缩放后的图
                    ivAvatar.setImageBitmap(scaledBitmap);
                    Log.d("PendingUserAdapter", "Successfully set scaled bitmap to ImageView");

                    // 强制刷新ImageView
                    ivAvatar.invalidate();
                    ivAvatar.requestLayout();

                    // 验证ImageView状态
                    AvatarDebugUtils.verifyImageViewState(ivAvatar, "PendingUserAdapter");
                }

                // 释放临时Bitmap
                if (scaledBitmap != bitmap && scaledBitmap != circleBitmap) {
                    scaledBitmap.recycle();
                }
            } catch (Exception e) {
                Log.e("PendingUserAdapter", "Error in setBitmapToImageViewInternal: " + e.getMessage(), e);
                ivAvatar.setImageResource(R.drawable.ic_person);
            }
        }

        /**
         * 显示审核结果信息（仅对已通过的用户）
         */
        private void showAuditResultInfo(UserResponse user) {
            StringBuilder auditInfo = new StringBuilder();

            // 显示工号
            if (user.getEmployeeId() != null && !user.getEmployeeId().isEmpty()) {
                auditInfo.append("工号：").append(user.getEmployeeId());
            }

            // 显示部门信息
            if (user.getDepartmentDisplay() != null && !user.getDepartmentDisplay().isEmpty()) {
                if (auditInfo.length() > 0) auditInfo.append(" | ");
                auditInfo.append("部门：").append(user.getDepartmentDisplay());
            }

            // 显示职位信息
            if (user.getPositionDisplay() != null && !user.getPositionDisplay().isEmpty()) {
                if (auditInfo.length() > 0) auditInfo.append(" | ");
                auditInfo.append("职位：").append(user.getPositionDisplay());
            }

            // 如果有审核信息，显示在注册时间位置
            if (auditInfo.length() > 0) {
                tvRegisterTime.setText(auditInfo.toString());
            } else {
                // 如果没有审核信息，显示注册时间
                String registerTime = user.getRegisterTime();
                if (registerTime != null && !registerTime.isEmpty()) {
                    tvRegisterTime.setText("注册时间：" + registerTime);
                } else {
                    tvRegisterTime.setText("注册时间：未知");
                }
            }
        }
    }
}
