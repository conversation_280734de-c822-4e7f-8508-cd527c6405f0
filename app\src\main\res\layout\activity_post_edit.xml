<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".ui.system.PostEditActivity">

    <!-- 工具栏 -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        android:elevation="4dp"
        app:layout_constraintTop_toTopOf="parent"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:title="添加岗位"
        app:titleTextColor="@color/white" />

    <!-- 表单内容 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/ll_buttons"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- 岗位名称 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="岗位名称"
                app:boxStrokeColor="@color/primary"
                app:hintTextColor="@color/primary">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLength="50" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 岗位代码 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_code"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="岗位代码"
                app:boxStrokeColor="@color/primary"
                app:hintTextColor="@color/primary">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_code"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textNoSuggestions"
                    android:maxLength="20" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 状态开关 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="状态"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="启用后该岗位可以被使用"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp" />

                    </LinearLayout>

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/switch_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 说明文本 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/bg_info_card"
                android:padding="16dp"
                android:text="• 岗位代码只能包含大写字母、数字和下划线\n• 岗位代码创建后不可修改\n• 岗位名称和代码不能重复"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

        </LinearLayout>

    </ScrollView>

    <!-- 底部按钮 -->
    <LinearLayout
        android:id="@+id/ll_buttons"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface"
        android:elevation="4dp"
        android:orientation="horizontal"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_cancel"
            style="@style/Widget.MaterialComponents.Button.OutlinedButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:text="取消" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_save"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:text="保存" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
