package com.opms.common.utils;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.bumptech.glide.Glide;
import com.bumptech.glide.RequestBuilder;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.resource.bitmap.CircleCrop;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;
import com.opms.R;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.AvatarResponse;
import com.opms.data.model.response.UserResponse;
import com.opms.data.repository.UserRepository;

import java.io.File;
import java.io.IOException;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * 图像工具类，提供加载和处理图像的方法
 */
public class ImageUtils {
    private static final String TAG = "ImageUtils";
    private static UserRepository userRepository;

    /**
     * 设置UserRepository实例
     *
     * @param repository UserRepository实例
     */
    public static void setUserRepository(UserRepository repository) {
        userRepository = repository;
    }

    /**
     * 智能加载用户头像 - 优先从本地缓存加载，缓存不存在时从服务器获取
     * 这是推荐的用户头像加载方法
     *
     * @param context   上下文
     * @param imageView 目标ImageView
     */
    public static void loadUserAvatarSmart(@NonNull Context context, @Nullable ImageView imageView) {
        loadUserAvatarSmart(context, imageView, false);
    }

    /**
     * 智能加载用户头像（圆形） - 优先从本地缓存加载，缓存不存在时从服务器获取
     * 这是推荐的用户头像加载方法
     *
     * @param context   上下文
     * @param imageView 目标ImageView
     */
    public static void loadUserAvatarSmartCircle(@NonNull Context context, @Nullable ImageView imageView) {
        loadUserAvatarSmart(context, imageView, true);
    }

    /**
     * 智能加载用户头像 - Fragment版本
     *
     * @param fragment  Fragment
     * @param imageView 目标ImageView
     */
    public static void loadUserAvatarSmart(@NonNull Fragment fragment, @Nullable ImageView imageView) {
        loadUserAvatarSmart(fragment, imageView, false);
    }

    /**
     * 智能加载用户头像（圆形） - Fragment版本
     *
     * @param fragment  Fragment
     * @param imageView 目标ImageView
     */
    public static void loadUserAvatarSmartCircle(@NonNull Fragment fragment, @Nullable ImageView imageView) {
        loadUserAvatarSmart(fragment, imageView, true);
    }

    /**
     * 智能加载用户头像的核心实现 - Context版本
     * 优先从本地缓存加载，缓存不存在时从服务器获取用户信息并使用avatarUrl
     *
     * @param context   上下文
     * @param imageView 目标ImageView
     * @param isCircle  是否应用圆形裁剪
     */
    private static void loadUserAvatarSmart(@NonNull Context context, @Nullable ImageView imageView, boolean isCircle) {
        if (imageView == null) {
            Log.e(TAG, "loadUserAvatarSmart: ImageView is null");
            return;
        }

        Log.d(TAG, "Smart loading user avatar, circle: " + isCircle);

        // 第一步：尝试从本地缓存加载
        String cachedAvatarPath = AvatarCacheUtils.getCachedAvatarPath(context);
        if (cachedAvatarPath != null) {
            Log.d(TAG, "Found cached avatar, loading from: " + cachedAvatarPath);
            try {
                RequestBuilder<Drawable> requestBuilder = Glide.with(context)
                        .load(new File(cachedAvatarPath))
                        .placeholder(R.drawable.ic_default_avatar)
                        .error(R.drawable.ic_default_avatar)
                        .diskCacheStrategy(DiskCacheStrategy.NONE);

                if (isCircle) {
                    requestBuilder = requestBuilder.apply(RequestOptions.bitmapTransform(new CircleCrop()));
                }

                requestBuilder.into(imageView);
                return;
            } catch (Exception e) {
                Log.e(TAG, "Error loading cached avatar: " + e.getMessage());
                // 缓存加载失败，继续尝试从服务器获取
            }
        }

        // 第二步：缓存不存在或加载失败，从服务器获取用户信息
        Log.d(TAG, "No cached avatar found, fetching user profile from server");
        fetchUserProfileAndLoadAvatar(context, imageView, isCircle);
    }

    /**
     * 智能加载用户头像的核心实现 - Fragment版本
     *
     * @param fragment  Fragment
     * @param imageView 目标ImageView
     * @param isCircle  是否应用圆形裁剪
     */
    private static void loadUserAvatarSmart(@NonNull Fragment fragment, @Nullable ImageView imageView, boolean isCircle) {
        if (imageView == null) {
            Log.e(TAG, "loadUserAvatarSmart: ImageView is null");
            return;
        }

        Context context = fragment.getContext();
        if (context == null) {
            Log.e(TAG, "Fragment context is null");
            imageView.setImageResource(R.drawable.ic_default_avatar);
            return;
        }

        Log.d(TAG, "Smart loading user avatar in fragment, circle: " + isCircle);

        // 第一步：尝试从本地缓存加载
        String cachedAvatarPath = AvatarCacheUtils.getCachedAvatarPath(context);
        if (cachedAvatarPath != null) {
            Log.d(TAG, "Found cached avatar, loading from: " + cachedAvatarPath);
            try {
                RequestBuilder<Drawable> requestBuilder = Glide.with(fragment)
                        .load(new File(cachedAvatarPath))
                        .placeholder(R.drawable.ic_default_avatar)
                        .error(R.drawable.ic_default_avatar)
                        .diskCacheStrategy(DiskCacheStrategy.NONE);

                if (isCircle) {
                    requestBuilder = requestBuilder.apply(RequestOptions.bitmapTransform(new CircleCrop()));
                }

                requestBuilder.into(imageView);
                return;
            } catch (Exception e) {
                Log.e(TAG, "Error loading cached avatar in fragment: " + e.getMessage());
                // 缓存加载失败，继续尝试从服务器获取
            }
        }

        // 第二步：缓存不存在或加载失败，从服务器获取用户信息
        Log.d(TAG, "No cached avatar found, fetching user profile from server");
        if (fragment.isAdded()) {
            fetchUserProfileAndLoadAvatarInFragment(fragment, imageView, isCircle);
        } else {
            imageView.setImageResource(R.drawable.ic_default_avatar);
        }
    }

    /**
     * 从服务器获取用户信息并加载头像 - Context版本
     *
     * @param context   上下文
     * @param imageView 目标ImageView
     * @param isCircle  是否应用圆形裁剪
     */
    private static void fetchUserProfileAndLoadAvatar(@NonNull Context context, @Nullable ImageView imageView, boolean isCircle) {
        if (userRepository == null) {
            Log.e(TAG, "UserRepository not set. Call setUserRepository first.");
            if (imageView != null) {
                imageView.setImageResource(R.drawable.ic_default_avatar);
            }
            return;
        }

        Log.d(TAG, "Fetching user profile from server");

        userRepository.getUserProfile().enqueue(new Callback<ApiResponse<UserResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<UserResponse>> call, Response<ApiResponse<UserResponse>> response) {
                Log.d(TAG, "User profile fetch response received: " + response.code());

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<UserResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        UserResponse user = apiResponse.getData();
                        String avatarUrl = user.getAvatarUrl();

                        Log.d(TAG, "Received user profile, avatarUrl: " +
                                (avatarUrl != null ? (avatarUrl.length() > 50 ? avatarUrl.substring(0, 50) + "..." : avatarUrl) : "null"));

                        if (imageView != null) {
                            loadAvatarFromUrl(context, avatarUrl, imageView, isCircle);
                        }
                    } else {
                        Log.e(TAG, "Failed to get user profile: " + apiResponse.getMessage());
                        if (imageView != null) {
                            imageView.setImageResource(R.drawable.ic_default_avatar);
                        }
                    }
                } else {
                    Log.e(TAG, "Server response not successful: " + response.code());
                    if (imageView != null) {
                        imageView.setImageResource(R.drawable.ic_default_avatar);
                    }
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<UserResponse>> call, Throwable t) {
                Log.e(TAG, "Network error when fetching user profile: " + t.getMessage(), t);
                if (imageView != null) {
                    imageView.setImageResource(R.drawable.ic_default_avatar);
                }
            }
        });
    }

    /**
     * 从服务器获取用户信息并加载头像 - Fragment版本
     *
     * @param fragment  Fragment
     * @param imageView 目标ImageView
     * @param isCircle  是否应用圆形裁剪
     */
    private static void fetchUserProfileAndLoadAvatarInFragment(@NonNull Fragment fragment, @Nullable ImageView imageView, boolean isCircle) {
        if (userRepository == null) {
            Log.e(TAG, "UserRepository not set. Call setUserRepository first.");
            if (imageView != null) {
                imageView.setImageResource(R.drawable.ic_default_avatar);
            }
            return;
        }

        Context context = fragment.getContext();
        if (context == null) {
            Log.e(TAG, "Fragment context is null");
            if (imageView != null) {
                imageView.setImageResource(R.drawable.ic_default_avatar);
            }
            return;
        }

        Log.d(TAG, "Fetching user profile from server in fragment");

        userRepository.getUserProfile().enqueue(new Callback<ApiResponse<UserResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<UserResponse>> call, Response<ApiResponse<UserResponse>> response) {
                if (!fragment.isAdded()) {
                    Log.w(TAG, "Fragment not attached when user profile response received");
                    return;
                }

                Log.d(TAG, "User profile fetch response received in fragment: " + response.code());

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<UserResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        UserResponse user = apiResponse.getData();
                        String avatarUrl = user.getAvatarUrl();

                        Log.d(TAG, "Received user profile in fragment, avatarUrl: " +
                                (avatarUrl != null ? (avatarUrl.length() > 50 ? avatarUrl.substring(0, 50) + "..." : avatarUrl) : "null"));

                        if (imageView != null) {
                            loadAvatarFromUrlInFragment(fragment, avatarUrl, imageView, isCircle);
                        }
                    } else {
                        Log.e(TAG, "Failed to get user profile in fragment: " + apiResponse.getMessage());
                        if (imageView != null) {
                            imageView.setImageResource(R.drawable.ic_default_avatar);
                        }
                    }
                } else {
                    Log.e(TAG, "Server response not successful in fragment: " + response.code());
                    if (imageView != null) {
                        imageView.setImageResource(R.drawable.ic_default_avatar);
                    }
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<UserResponse>> call, Throwable t) {
                if (!fragment.isAdded()) {
                    Log.w(TAG, "Fragment not attached when user profile request failed");
                    return;
                }

                Log.e(TAG, "Network error when fetching user profile in fragment: " + t.getMessage(), t);
                if (imageView != null) {
                    imageView.setImageResource(R.drawable.ic_default_avatar);
                }
            }
        });
    }

    /**
     * 从URL加载头像 - Context版本
     *
     * @param context   上下文
     * @param avatarUrl 头像URL
     * @param imageView 目标ImageView
     * @param isCircle  是否应用圆形裁剪
     */
    private static void loadAvatarFromUrl(@NonNull Context context, @Nullable String avatarUrl, @NonNull ImageView imageView, boolean isCircle) {
        if (avatarUrl != null && !avatarUrl.isEmpty()) {
            Log.d(TAG, "Loading avatar from URL: " + avatarUrl);
            try {
                String processedUrl = processImageUrl(avatarUrl);
                RequestBuilder<Drawable> requestBuilder = Glide.with(context)
                        .load(processedUrl)
                        .placeholder(R.drawable.ic_default_avatar)
                        .error(R.drawable.ic_default_avatar)
                        .diskCacheStrategy(DiskCacheStrategy.ALL);

                if (isCircle) {
                    requestBuilder = requestBuilder.apply(RequestOptions.bitmapTransform(new CircleCrop()));
                }

                requestBuilder.into(imageView);
            } catch (Exception e) {
                Log.e(TAG, "Error loading avatar from URL: " + e.getMessage());
                imageView.setImageResource(R.drawable.ic_default_avatar);
            }
        } else {
            Log.d(TAG, "Avatar URL is empty, showing default avatar");
            imageView.setImageResource(R.drawable.ic_default_avatar);
        }
    }

    /**
     * 从URL加载头像 - Fragment版本
     *
     * @param fragment  Fragment
     * @param avatarUrl 头像URL
     * @param imageView 目标ImageView
     * @param isCircle  是否应用圆形裁剪
     */
    private static void loadAvatarFromUrlInFragment(@NonNull Fragment fragment, @Nullable String avatarUrl, @NonNull ImageView imageView, boolean isCircle) {
        if (avatarUrl != null && !avatarUrl.isEmpty()) {
            Log.d(TAG, "Loading avatar from URL in fragment: " + avatarUrl);
            try {
                String processedUrl = processImageUrl(avatarUrl);
                RequestBuilder<Drawable> requestBuilder = Glide.with(fragment)
                        .load(processedUrl)
                        .placeholder(R.drawable.ic_default_avatar)
                        .error(R.drawable.ic_default_avatar)
                        .diskCacheStrategy(DiskCacheStrategy.ALL);

                if (isCircle) {
                    requestBuilder = requestBuilder.apply(RequestOptions.bitmapTransform(new CircleCrop()));
                }

                requestBuilder.into(imageView);
            } catch (Exception e) {
                Log.e(TAG, "Error loading avatar from URL in fragment: " + e.getMessage());
                imageView.setImageResource(R.drawable.ic_default_avatar);
            }
        } else {
            Log.d(TAG, "Avatar URL is empty, showing default avatar");
            imageView.setImageResource(R.drawable.ic_default_avatar);
        }
    }

    /**
     * 加载用户头像
     *
     * @param context   上下文
     * @param imageUrl  头像URL
     * @param imageView 目标ImageView
     */
    public static void loadUserAvatar(@NonNull Context context, @Nullable String imageUrl, @Nullable ImageView imageView) {
        if (imageView == null) {
            Log.e(TAG, "loadUserAvatar: ImageView is null");
            return;
        }

        // 首先尝试从本地缓存加载
        String cachedAvatarPath = AvatarCacheUtils.getCachedAvatarPath(context);
        if (cachedAvatarPath != null) {
            try {
                Glide.with(context)
                        .load(new File(cachedAvatarPath))
                        .placeholder(R.drawable.ic_default_avatar)
                        .error(R.drawable.ic_default_avatar)
                        .diskCacheStrategy(DiskCacheStrategy.NONE) // 不使用Glide的缓存，我们自己管理缓存
                        .into(imageView);
                return;
            } catch (Exception e) {
                Log.e(TAG, "Error loading cached avatar: " + e.getMessage());
                // 如果加载缓存失败，继续尝试从URL加载
            }
        }

        // 如果没有缓存或缓存加载失败，尝试从URL加载
        try {
            RequestBuilder<?> requestBuilder;
            if (TextUtils.isEmpty(imageUrl)) {
                // 如果URL为空，尝试从服务器获取头像
                fetchAndCacheAvatarFromServer(context, imageView);
                return;
            } else {
                // 处理URL，确保它是有效的
                String processedUrl = processImageUrl(imageUrl);
                requestBuilder = Glide.with(context).load(processedUrl);
            }

            requestBuilder
                    .placeholder(R.drawable.ic_default_avatar)
                    .error(R.drawable.ic_default_avatar)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .into(imageView);
        } catch (Exception e) {
            Log.e(TAG, "Error loading avatar: " + e.getMessage());
            // 出错时直接设置默认头像
            imageView.setImageResource(R.drawable.ic_default_avatar);
        }
    }

    /**
     * 从服务器获取并缓存用户头像
     *
     * @param context   上下文
     * @param imageView 目标ImageView，可以为null
     */
    public static void fetchAndCacheAvatarFromServer(@NonNull Context context, @Nullable final ImageView imageView) {
        Log.d(TAG, "Fetching avatar from server");

        if (userRepository == null) {
            Log.e(TAG, "UserRepository not set. Call setUserRepository first.");
            if (imageView != null) {
                imageView.setImageResource(R.drawable.ic_default_avatar);
            }
            return;
        }

        userRepository.getUserAvatar().enqueue(new Callback<ApiResponse<AvatarResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<AvatarResponse>> call, Response<ApiResponse<AvatarResponse>> response) {
                Log.d(TAG, "Avatar fetch response received: " + response.code());

                if (response.isSuccessful()) {
                    Log.d(TAG, "Response is successful");

                    if (response.body() != null) {
                        Log.d(TAG, "Response body is not null");

                        if (response.body().isSuccess()) {
                            Log.d(TAG, "API response indicates success");

                            AvatarResponse avatarResponse = response.body().getData();
                            if (avatarResponse != null) {
                                String base64Avatar = avatarResponse.getBase64();
                                if (!TextUtils.isEmpty(base64Avatar)) {
                                    Log.d(TAG, "Received base64 avatar from server, length: " + base64Avatar.length());

                                    // 缓存头像
                                    String cachedPath = AvatarCacheUtils.cacheBase64Avatar(context, base64Avatar);
                                    if (cachedPath != null) {
                                        Log.d(TAG, "Avatar cached successfully: " + cachedPath);

                                        // 如果提供了ImageView，加载头像
                                        if (imageView != null) {
                                            try {
                                                Log.d(TAG, "Loading cached avatar into ImageView");
                                                Glide.with(context)
                                                        .load(new File(cachedPath))
                                                        .placeholder(R.drawable.ic_default_avatar)
                                                        .error(R.drawable.ic_default_avatar)
                                                        .diskCacheStrategy(DiskCacheStrategy.NONE)
                                                        .into(new SimpleTarget<Drawable>() {
                                                            @Override
                                                            public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
                                                                Log.d(TAG, "Avatar loaded successfully");
                                                                imageView.setImageDrawable(resource);
                                                            }

                                                            @Override
                                                            public void onLoadFailed(@Nullable Drawable errorDrawable) {
                                                                Log.e(TAG, "Failed to load avatar from cache");
                                                                imageView.setImageResource(R.drawable.ic_default_avatar);
                                                            }
                                                        });
                                            } catch (Exception e) {
                                                Log.e(TAG, "Error loading cached avatar after fetch: " + e.getMessage(), e);
                                                imageView.setImageResource(R.drawable.ic_default_avatar);
                                            }
                                        }
                                    } else {
                                        Log.e(TAG, "Failed to cache avatar");
                                        if (imageView != null) {
                                            imageView.setImageResource(R.drawable.ic_default_avatar);
                                        }
                                    }
                                } else {
                                    Log.e(TAG, "Received empty base64 avatar from server");
                                    if (imageView != null) {
                                        imageView.setImageResource(R.drawable.ic_default_avatar);
                                    }
                                }
                            } else {
                                Log.e(TAG, "Avatar response is null");
                                if (imageView != null) {
                                    imageView.setImageResource(R.drawable.ic_default_avatar);
                                }
                            }
                        } else {
                            Log.e(TAG, "API response indicates failure: " + response.body().getMessage());
                            if (imageView != null) {
                                imageView.setImageResource(R.drawable.ic_default_avatar);
                            }
                        }
                    } else {
                        Log.e(TAG, "Response body is null");
                        if (imageView != null) {
                            imageView.setImageResource(R.drawable.ic_default_avatar);
                        }
                    }
                } else {
                    try {
                        String errorBody = response.errorBody() != null ? response.errorBody().string() : "Unknown error";
                        Log.e(TAG, "Failed to get avatar from server: " + response.code() + ", Error: " + errorBody);
                    } catch (IOException e) {
                        Log.e(TAG, "Error reading error body", e);
                    }

                    if (imageView != null) {
                        imageView.setImageResource(R.drawable.ic_default_avatar);
                    }
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<AvatarResponse>> call, Throwable t) {
                Log.e(TAG, "Network error when fetching avatar: " + t.getMessage(), t);
                if (imageView != null) {
                    imageView.setImageResource(R.drawable.ic_default_avatar);
                }
            }
        });
    }

    /**
     * 处理图片URL，确保它是有效的（公共方法）
     *
     * @param imageUrl 原始图片URL
     * @return 处理后的URL
     */
    public static String processImageUrl(String imageUrl) {
        Log.d(TAG, "processImageUrl - Input: " + imageUrl);

        if (TextUtils.isEmpty(imageUrl)) {
            Log.d(TAG, "processImageUrl - Empty input, returning empty string");
            return "";
        }

        // 检查是否是本地文件路径格式（例如：image\avatar\avatar-1747896112559.jpg）
        boolean isLocalPath = imageUrl.contains("\\") ||
                             imageUrl.startsWith("image/") ||
                             imageUrl.startsWith("image\\") ||
                             imageUrl.startsWith("images/");

        Log.d(TAG, "processImageUrl - Is local path: " + isLocalPath);

        if (isLocalPath) {
            // 这是一个服务器上的文件路径，需要构建完整的URL
            // 使用环境配置中的服务器地址
            String serverUrl = com.opms.common.constants.EnvironmentConfig.getBaseUrl();
            Log.d(TAG, "processImageUrl - Server URL: " + serverUrl);

            // 替换反斜杠为正斜杠
            String normalizedPath = imageUrl.replace("\\", "/");
            Log.d(TAG, "processImageUrl - Normalized path: " + normalizedPath);

            // 如果路径已经以服务器URL开头，则直接返回
            if (normalizedPath.startsWith("http://") || normalizedPath.startsWith("https://")) {
                Log.d(TAG, "processImageUrl - Already full URL, returning: " + normalizedPath);
                return normalizedPath;
            }

            // 构建完整URL
            String fullUrl = serverUrl + normalizedPath;
            Log.d(TAG, "processImageUrl - Built full URL: " + fullUrl);
            return fullUrl;
        }

        // 如果URL不是以http或https开头，添加前缀
        if (!imageUrl.startsWith("http://") && !imageUrl.startsWith("https://")) {
            String httpUrl = "http://" + imageUrl;
            Log.d(TAG, "processImageUrl - Added http prefix: " + httpUrl);
            return httpUrl;
        }

        Log.d(TAG, "processImageUrl - URL unchanged: " + imageUrl);
        return imageUrl;
    }

    /**
     * 测试URL处理功能
     * 用于调试图像加载问题
     */
    public static void testUrlProcessing() {
        Log.d(TAG, "=== Testing URL Processing ===");

        // 测试用例
        String[] testUrls = {
            "images/avatar/1749024077257-741159693.jpg",
            "image/avatar/test.jpg",
            "image\\avatar\\test.jpg",
            "http://example.com/image.jpg",
            "https://example.com/image.jpg",
            "www.example.com/image.jpg",
            "",
            null
        };

        for (String testUrl : testUrls) {
            try {
                String result = processImageUrl(testUrl);
                Log.d(TAG, "Input: '" + testUrl + "' -> Output: '" + result + "'");
            } catch (Exception e) {
                Log.e(TAG, "Error processing URL: " + testUrl + ", Error: " + e.getMessage());
            }
        }

        Log.d(TAG, "=== URL Processing Test Complete ===");
    }

    /**
     * 从缓存加载用户头像
     *
     * @param context   上下文
     * @param imageView 目标ImageView
     */
    public static void loadUserAvatarFromCache(@NonNull Context context, @Nullable ImageView imageView) {
        if (imageView == null) {
            Log.e(TAG, "loadUserAvatarFromCache: ImageView is null");
            return;
        }

        String cachedAvatarPath = AvatarCacheUtils.getCachedAvatarPath(context);
        if (cachedAvatarPath != null) {
            try {
                Log.d(TAG, "Loading avatar from cache: " + cachedAvatarPath);

                // 清除 Glide 缓存
                Glide.get(context).clearMemory();

                // 在主线程中清除磁盘缓存
                new Thread(() -> {
                    try {
                        Glide.get(context).clearDiskCache();
                    } catch (Exception e) {
                        Log.e(TAG, "Error clearing Glide disk cache: " + e.getMessage(), e);
                    }
                }).start();

                // 使用 skipMemoryCache 确保不使用内存缓存
                Glide.with(context)
                        .load(new File(cachedAvatarPath))
                        .placeholder(R.drawable.ic_default_avatar)
                        .error(R.drawable.ic_default_avatar)
                        .diskCacheStrategy(DiskCacheStrategy.NONE)
                        .skipMemoryCache(true)
                        .into(new SimpleTarget<Drawable>() {
                            @Override
                            public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
                                Log.d(TAG, "Avatar loaded successfully from cache");
                                imageView.setImageDrawable(resource);
                            }

                            @Override
                            public void onLoadFailed(@Nullable Drawable errorDrawable) {
                                Log.e(TAG, "Failed to load avatar from cache");
                                imageView.setImageResource(R.drawable.ic_default_avatar);
                            }
                        });
            } catch (Exception e) {
                Log.e(TAG, "Error loading cached avatar: " + e.getMessage(), e);
                imageView.setImageResource(R.drawable.ic_default_avatar);
            }
        } else {
            // 如果没有缓存，尝试从服务器获取
            fetchAndCacheAvatarFromServer(context, imageView);
        }
    }

    /**
     * 从缓存加载用户头像（圆形）
     *
     * @param context   上下文
     * @param imageView 目标ImageView
     */
    public static void loadUserAvatarCircleFromCache(@NonNull Context context, @Nullable ImageView imageView) {
        if (imageView == null) {
            Log.e(TAG, "loadUserAvatarCircleFromCache: ImageView is null");
            return;
        }

        String cachedAvatarPath = AvatarCacheUtils.getCachedAvatarPath(context);
        if (cachedAvatarPath != null) {
            try {
                Glide.with(context)
                        .load(new File(cachedAvatarPath))
                        .placeholder(R.drawable.ic_default_avatar)
                        .error(R.drawable.ic_default_avatar)
                        .diskCacheStrategy(DiskCacheStrategy.NONE)
                        .apply(RequestOptions.bitmapTransform(new CircleCrop()))
                        .into(imageView);
            } catch (Exception e) {
                Log.e(TAG, "Error loading cached circle avatar: " + e.getMessage());
                imageView.setImageResource(R.drawable.ic_default_avatar);
            }
        } else {
            // 如果没有缓存，尝试从服务器获取
            fetchAndCacheAvatarFromServer(context, imageView);
        }
    }

    /**
     * 从缓存加载用户头像
     *
     * @param fragment  Fragment
     * @param imageView 目标ImageView
     */
    public static void loadUserAvatarFromCache(@NonNull Fragment fragment, @Nullable ImageView imageView) {
        if (imageView == null) {
            Log.e(TAG, "loadUserAvatarFromCache: ImageView is null");
            return;
        }

        Context context = fragment.getContext();
        if (context == null) {
            Log.e(TAG, "Fragment context is null");
            imageView.setImageResource(R.drawable.ic_default_avatar);
            return;
        }

        String cachedAvatarPath = AvatarCacheUtils.getCachedAvatarPath(context);
        if (cachedAvatarPath != null) {
            try {
                Log.d(TAG, "Loading avatar from cache in fragment: " + cachedAvatarPath);

                // 清除 Glide 缓存
                Glide.get(context).clearMemory();

                // 在主线程中清除磁盘缓存
                new Thread(() -> {
                    try {
                        Glide.get(context).clearDiskCache();
                    } catch (Exception e) {
                        Log.e(TAG, "Error clearing Glide disk cache in fragment: " + e.getMessage(), e);
                    }
                }).start();

                // 使用 skipMemoryCache 确保不使用内存缓存
                Glide.with(fragment)
                        .load(new File(cachedAvatarPath))
                        .placeholder(R.drawable.ic_default_avatar)
                        .error(R.drawable.ic_default_avatar)
                        .diskCacheStrategy(DiskCacheStrategy.NONE)
                        .skipMemoryCache(true)
                        .into(new SimpleTarget<Drawable>() {
                            @Override
                            public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
                                Log.d(TAG, "Avatar loaded successfully from cache in fragment");
                                imageView.setImageDrawable(resource);
                            }

                            @Override
                            public void onLoadFailed(@Nullable Drawable errorDrawable) {
                                Log.e(TAG, "Failed to load avatar from cache in fragment");
                                imageView.setImageResource(R.drawable.ic_default_avatar);
                            }
                        });
            } catch (Exception e) {
                Log.e(TAG, "Error loading cached avatar in fragment: " + e.getMessage(), e);
                imageView.setImageResource(R.drawable.ic_default_avatar);
            }
        } else if (fragment.isAdded()) {
            // 如果没有缓存，尝试从服务器获取
            fetchAndCacheAvatarFromServer(context, imageView);
        } else {
            imageView.setImageResource(R.drawable.ic_default_avatar);
        }
    }

    /**
     * 从缓存加载用户头像（圆形）
     *
     * @param fragment  Fragment
     * @param imageView 目标ImageView
     */
    public static void loadUserAvatarCircleFromCache(@NonNull Fragment fragment, @Nullable ImageView imageView) {
        if (imageView == null) {
            Log.e(TAG, "loadUserAvatarCircleFromCache: ImageView is null");
            return;
        }

        Context context = fragment.getContext();
        if (context == null) {
            Log.e(TAG, "Fragment context is null");
            imageView.setImageResource(R.drawable.ic_default_avatar);
            return;
        }

        String cachedAvatarPath = AvatarCacheUtils.getCachedAvatarPath(context);
        if (cachedAvatarPath != null) {
            try {
                Glide.with(fragment)
                        .load(new File(cachedAvatarPath))
                        .placeholder(R.drawable.ic_default_avatar)
                        .error(R.drawable.ic_default_avatar)
                        .diskCacheStrategy(DiskCacheStrategy.NONE)
                        .apply(RequestOptions.bitmapTransform(new CircleCrop()))
                        .into(imageView);
            } catch (Exception e) {
                Log.e(TAG, "Error loading cached circle avatar in fragment: " + e.getMessage());
                imageView.setImageResource(R.drawable.ic_default_avatar);
            }
        } else if (fragment.isAdded()) {
            // 如果没有缓存，尝试从服务器获取
            fetchAndCacheAvatarFromServer(context, imageView);
        } else {
            imageView.setImageResource(R.drawable.ic_default_avatar);
        }
    }

    /**
     * 加载用户头像（圆形）
     *
     * @param context   上下文
     * @param imageUrl  头像URL
     * @param imageView 目标ImageView
     */
    public static void loadUserAvatarCircle(@NonNull Context context, @Nullable String imageUrl, @Nullable ImageView imageView) {
        if (imageView == null) {
            Log.e(TAG, "loadUserAvatarCircle: ImageView is null");
            return;
        }

        // 首先尝试从本地缓存加载
        String cachedAvatarPath = AvatarCacheUtils.getCachedAvatarPath(context);
        if (cachedAvatarPath != null) {
            try {
                Glide.with(context)
                        .load(new File(cachedAvatarPath))
                        .placeholder(R.drawable.ic_default_avatar)
                        .error(R.drawable.ic_default_avatar)
                        .diskCacheStrategy(DiskCacheStrategy.NONE) // 不使用Glide的缓存，我们自己管理缓存
                        .apply(RequestOptions.bitmapTransform(new CircleCrop()))
                        .into(imageView);
                return;
            } catch (Exception e) {
                Log.e(TAG, "Error loading cached circle avatar: " + e.getMessage());
                // 如果加载缓存失败，继续尝试从URL加载
            }
        }

        // 如果没有缓存或缓存加载失败，尝试从URL加载
        try {
            RequestBuilder<?> requestBuilder;
            if (TextUtils.isEmpty(imageUrl)) {
                // 如果URL为空，尝试从服务器获取头像
                fetchAndCacheAvatarFromServer(context, imageView);
                return;
            } else {
                // 处理URL，确保它是有效的
                String processedUrl = processImageUrl(imageUrl);
                requestBuilder = Glide.with(context).load(processedUrl);
            }

            requestBuilder
                    .placeholder(R.drawable.ic_default_avatar)
                    .error(R.drawable.ic_default_avatar)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .apply(RequestOptions.bitmapTransform(new CircleCrop()))
                    .into(imageView);
        } catch (Exception e) {
            Log.e(TAG, "Error loading circle avatar: " + e.getMessage());
            // 出错时直接设置默认头像
            imageView.setImageResource(R.drawable.ic_default_avatar);
        }
    }

    /**
     * 从Fragment中加载用户头像
     *
     * @param fragment  Fragment
     * @param imageUrl  头像URL
     * @param imageView 目标ImageView
     */
    public static void loadUserAvatar(@NonNull Fragment fragment, @Nullable String imageUrl, @Nullable ImageView imageView) {
        if (imageView == null) {
            Log.e(TAG, "loadUserAvatar: ImageView is null");
            return;
        }

        Context context = fragment.getContext();
        if (context == null) {
            Log.e(TAG, "Fragment context is null");
            imageView.setImageResource(R.drawable.ic_default_avatar);
            return;
        }

        // 首先尝试从本地缓存加载
        String cachedAvatarPath = AvatarCacheUtils.getCachedAvatarPath(context);
        if (cachedAvatarPath != null) {
            try {
                Glide.with(fragment)
                        .load(new File(cachedAvatarPath))
                        .placeholder(R.drawable.ic_default_avatar)
                        .error(R.drawable.ic_default_avatar)
                        .diskCacheStrategy(DiskCacheStrategy.NONE) // 不使用Glide的缓存，我们自己管理缓存
                        .into(imageView);
                return;
            } catch (Exception e) {
                Log.e(TAG, "Error loading cached avatar in fragment: " + e.getMessage());
                // 如果加载缓存失败，继续尝试从URL加载
            }
        }

        // 如果没有缓存或缓存加载失败，尝试从URL加载
        try {
            RequestBuilder<?> requestBuilder;
            if (TextUtils.isEmpty(imageUrl)) {
                // 如果URL为空，尝试从服务器获取头像
                if (fragment.isAdded()) {
                    fetchAndCacheAvatarFromServer(context, imageView);
                } else {
                    imageView.setImageResource(R.drawable.ic_default_avatar);
                }
                return;
            } else {
                // 处理URL，确保它是有效的
                String processedUrl = processImageUrl(imageUrl);
                requestBuilder = Glide.with(fragment).load(processedUrl);
            }

            requestBuilder
                    .placeholder(R.drawable.ic_default_avatar)
                    .error(R.drawable.ic_default_avatar)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .into(imageView);
        } catch (Exception e) {
            Log.e(TAG, "Error loading avatar in fragment: " + e.getMessage());
            // 出错时直接设置默认头像
            imageView.setImageResource(R.drawable.ic_default_avatar);
        }
    }

    /**
     * 从Fragment中加载用户头像（圆形）
     *
     * @param fragment  Fragment
     * @param imageUrl  头像URL
     * @param imageView 目标ImageView
     */
    public static void loadUserAvatarCircle(@NonNull Fragment fragment, @Nullable String imageUrl, @Nullable ImageView imageView) {
        if (imageView == null) {
            Log.e(TAG, "loadUserAvatarCircle: ImageView is null");
            return;
        }

        Context context = fragment.getContext();
        if (context == null) {
            Log.e(TAG, "Fragment context is null");
            imageView.setImageResource(R.drawable.ic_default_avatar);
            return;
        }

        // 首先尝试从本地缓存加载
        String cachedAvatarPath = AvatarCacheUtils.getCachedAvatarPath(context);
        if (cachedAvatarPath != null) {
            try {
                Glide.with(fragment)
                        .load(new File(cachedAvatarPath))
                        .placeholder(R.drawable.ic_default_avatar)
                        .error(R.drawable.ic_default_avatar)
                        .diskCacheStrategy(DiskCacheStrategy.NONE) // 不使用Glide的缓存，我们自己管理缓存
                        .apply(RequestOptions.bitmapTransform(new CircleCrop()))
                        .into(imageView);
                return;
            } catch (Exception e) {
                Log.e(TAG, "Error loading cached circle avatar in fragment: " + e.getMessage());
                // 如果加载缓存失败，继续尝试从URL加载
            }
        }

        // 如果没有缓存或缓存加载失败，尝试从URL加载
        try {
            RequestBuilder<?> requestBuilder;
            if (TextUtils.isEmpty(imageUrl)) {
                // 如果URL为空，尝试从服务器获取头像
                if (fragment.isAdded()) {
                    fetchAndCacheAvatarFromServer(context, imageView);
                } else {
                    imageView.setImageResource(R.drawable.ic_default_avatar);
                }
                return;
            } else {
                // 处理URL，确保它是有效的
                String processedUrl = processImageUrl(imageUrl);
                requestBuilder = Glide.with(fragment).load(processedUrl);
            }

            requestBuilder
                    .placeholder(R.drawable.ic_default_avatar)
                    .error(R.drawable.ic_default_avatar)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .apply(RequestOptions.bitmapTransform(new CircleCrop()))
                    .into(imageView);
        } catch (Exception e) {
            Log.e(TAG, "Error loading circle avatar in fragment: " + e.getMessage());
            // 出错时直接设置默认头像
            imageView.setImageResource(R.drawable.ic_default_avatar);
        }
    }

    /**
     * 加载本地Uri头像
     *
     * @param context   上下文
     * @param imageUri  头像Uri
     * @param imageView 目标ImageView
     */
    public static void loadUserAvatar(@NonNull Context context, @Nullable Uri imageUri, @Nullable ImageView imageView) {
        if (imageView == null) {
            Log.e(TAG, "loadUserAvatar: ImageView is null");
            return;
        }

        try {
            RequestBuilder<?> requestBuilder;
            if (imageUri == null) {
                // 如果URI为空，直接加载默认头像
                requestBuilder = Glide.with(context).load(R.drawable.ic_default_avatar);
            } else {
                requestBuilder = Glide.with(context).load(imageUri);
            }

            requestBuilder
                    .placeholder(R.drawable.ic_default_avatar)
                    .error(R.drawable.ic_default_avatar)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .into(imageView);
        } catch (Exception e) {
            Log.e(TAG, "Error loading avatar from URI: " + e.getMessage());
            // 出错时直接设置默认头像
            imageView.setImageResource(R.drawable.ic_default_avatar);
        }
    }

    /**
     * 加载本地Uri头像（圆形）
     *
     * @param context   上下文
     * @param imageUri  头像Uri
     * @param imageView 目标ImageView
     */
    public static void loadUserAvatarCircle(@NonNull Context context, @Nullable Uri imageUri, @Nullable ImageView imageView) {
        if (imageView == null) {
            Log.e(TAG, "loadUserAvatarCircle: ImageView is null");
            return;
        }

        try {
            RequestBuilder<?> requestBuilder;
            if (imageUri == null) {
                // 如果URI为空，直接加载默认头像
                requestBuilder = Glide.with(context).load(R.drawable.ic_default_avatar);
            } else {
                requestBuilder = Glide.with(context).load(imageUri);
            }

            requestBuilder
                    .placeholder(R.drawable.ic_default_avatar)
                    .error(R.drawable.ic_default_avatar)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .apply(RequestOptions.bitmapTransform(new CircleCrop()))
                    .into(imageView);
        } catch (Exception e) {
            Log.e(TAG, "Error loading circle avatar from URI: " + e.getMessage());
            // 出错时直接设置默认头像
            imageView.setImageResource(R.drawable.ic_default_avatar);
        }
    }

    /**
     * 从Fragment中加载本地Uri头像
     *
     * @param fragment  Fragment
     * @param imageUri  头像Uri
     * @param imageView 目标ImageView
     */
    public static void loadUserAvatar(@NonNull Fragment fragment, @Nullable Uri imageUri, @Nullable ImageView imageView) {
        if (imageView == null) {
            Log.e(TAG, "loadUserAvatar: ImageView is null");
            return;
        }

        try {
            RequestBuilder<?> requestBuilder;
            if (imageUri == null) {
                // 如果URI为空，直接加载默认头像
                requestBuilder = Glide.with(fragment).load(R.drawable.ic_default_avatar);
            } else {
                requestBuilder = Glide.with(fragment).load(imageUri);
            }

            requestBuilder
                    .placeholder(R.drawable.ic_default_avatar)
                    .error(R.drawable.ic_default_avatar)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .into(imageView);
        } catch (Exception e) {
            Log.e(TAG, "Error loading avatar from URI in fragment: " + e.getMessage());
            // 出错时直接设置默认头像
            imageView.setImageResource(R.drawable.ic_default_avatar);
        }
    }

    /**
     * 从Fragment中加载本地Uri头像（圆形）
     *
     * @param fragment  Fragment
     * @param imageUri  头像Uri
     * @param imageView 目标ImageView
     */
    public static void loadUserAvatarCircle(@NonNull Fragment fragment, @Nullable Uri imageUri, @Nullable ImageView imageView) {
        if (imageView == null) {
            Log.e(TAG, "loadUserAvatarCircle: ImageView is null");
            return;
        }

        try {
            RequestBuilder<?> requestBuilder;
            if (imageUri == null) {
                // 如果URI为空，直接加载默认头像
                requestBuilder = Glide.with(fragment).load(R.drawable.ic_default_avatar);
            } else {
                requestBuilder = Glide.with(fragment).load(imageUri);
            }

            requestBuilder
                    .placeholder(R.drawable.ic_default_avatar)
                    .error(R.drawable.ic_default_avatar)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .apply(RequestOptions.bitmapTransform(new CircleCrop()))
                    .into(imageView);
        } catch (Exception e) {
            Log.e(TAG, "Error loading circle avatar from URI in fragment: " + e.getMessage());
            // 出错时直接设置默认头像
            imageView.setImageResource(R.drawable.ic_default_avatar);
        }
    }
}
