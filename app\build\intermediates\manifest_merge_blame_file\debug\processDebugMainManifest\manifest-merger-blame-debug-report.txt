1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.opms"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="31"
9        android:targetSdkVersion="34" />
10
11    <!-- 网络权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:6:5-67
12-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:7:5-79
13-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:7:22-76
14
15    <!-- 存储权限 -->
16    <uses-permission
16-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:10:5-12:38
17        android:name="android.permission.READ_EXTERNAL_STORAGE"
17-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:11:9-64
18        android:maxSdkVersion="32" />
18-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:12:9-35
19    <uses-permission
19-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:13:5-15:38
20        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
20-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:14:9-65
21        android:maxSdkVersion="32" />
21-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:15:9-35
22    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
22-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:16:5-76
22-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:16:22-73
23    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
23-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:17:5-75
23-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:17:22-72
24    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
24-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:18:5-75
24-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:18:22-72
25
26    <!-- 相机权限 -->
27    <uses-permission android:name="android.permission.CAMERA" />
27-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:21:5-65
27-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:21:22-62
28
29    <uses-feature
29-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:22:5-24:36
30        android:name="android.hardware.camera"
30-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:23:9-47
31        android:required="false" />
31-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:24:9-33
32
33    <permission
33-->[androidx.core:core:1.12.0] D:\Android\GradleRepository\caches\8.11.1\transforms\f5d6d0c532ad067d6a1644a9775866cb\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
34        android:name="com.opms.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
34-->[androidx.core:core:1.12.0] D:\Android\GradleRepository\caches\8.11.1\transforms\f5d6d0c532ad067d6a1644a9775866cb\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
35        android:protectionLevel="signature" />
35-->[androidx.core:core:1.12.0] D:\Android\GradleRepository\caches\8.11.1\transforms\f5d6d0c532ad067d6a1644a9775866cb\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
36
37    <uses-permission android:name="com.opms.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
37-->[androidx.core:core:1.12.0] D:\Android\GradleRepository\caches\8.11.1\transforms\f5d6d0c532ad067d6a1644a9775866cb\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
37-->[androidx.core:core:1.12.0] D:\Android\GradleRepository\caches\8.11.1\transforms\f5d6d0c532ad067d6a1644a9775866cb\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
38
39    <application
39-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:26:5-267:19
40        android:name="com.opms.OPMSApplication"
40-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:27:9-40
41        android:allowBackup="true"
41-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:28:9-35
42        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
42-->[androidx.core:core:1.12.0] D:\Android\GradleRepository\caches\8.11.1\transforms\f5d6d0c532ad067d6a1644a9775866cb\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
43        android:dataExtractionRules="@xml/data_extraction_rules"
43-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:29:9-65
44        android:debuggable="true"
45        android:extractNativeLibs="false"
46        android:fullBackupContent="@xml/backup_rules"
46-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:30:9-54
47        android:icon="@mipmap/ic_launcher"
47-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:31:9-43
48        android:label="@string/app_name"
48-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:32:9-41
49        android:requestLegacyExternalStorage="true"
49-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:37:9-52
50        android:roundIcon="@mipmap/ic_launcher_round"
50-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:33:9-54
51        android:supportsRtl="true"
51-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:34:9-35
52        android:testOnly="true"
53        android:theme="@style/Theme.OPMS"
53-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:35:9-42
54        android:usesCleartextTraffic="true" >
54-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:36:9-44
55
56        <!-- 启动页 -->
57        <activity
57-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:41:9-49:20
58            android:name="com.opms.ui.splash.SplashActivity"
58-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:42:13-53
59            android:exported="true"
59-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:43:13-36
60            android:theme="@style/Theme.OPMS.NoActionBar" >
60-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:44:13-58
61            <intent-filter>
61-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:45:13-48:29
62                <action android:name="android.intent.action.MAIN" />
62-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:46:17-69
62-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:46:25-66
63
64                <category android:name="android.intent.category.LAUNCHER" />
64-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:47:17-77
64-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:47:27-74
65            </intent-filter>
66        </activity>
67
68        <!-- 登录页 -->
69        <activity
69-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:52:9-55:61
70            android:name="com.opms.ui.login.LoginActivity"
70-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:53:13-51
71            android:exported="false"
71-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:54:13-37
72            android:theme="@style/Theme.OPMS.NoActionBar" />
72-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:55:13-58
73
74        <!-- 注册页 -->
75        <activity
75-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:58:9-61:61
76            android:name="com.opms.ui.register.RegisterActivity"
76-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:59:13-57
77            android:exported="false"
77-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:60:13-37
78            android:theme="@style/Theme.OPMS.NoActionBar" />
78-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:61:13-58
79
80        <!-- 主页面 -->
81        <activity
81-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:64:9-67:61
82            android:name="com.opms.MainActivity"
82-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:65:13-41
83            android:exported="false"
83-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:66:13-37
84            android:theme="@style/Theme.OPMS.NoActionBar" />
84-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:67:13-58
85
86        <!-- 文件提供者 -->
87        <provider
88            android:name="androidx.core.content.FileProvider"
88-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:71:13-62
89            android:authorities="com.opms.fileprovider"
89-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:72:13-64
90            android:exported="false"
90-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:73:13-37
91            android:grantUriPermissions="true" >
91-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:74:13-47
92            <meta-data
92-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:75:13-77:54
93                android:name="android.support.FILE_PROVIDER_PATHS"
93-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:76:17-67
94                android:resource="@xml/file_paths" />
94-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:77:17-51
95        </provider>
96
97        <!--
98        这些功能尚未实现，暂时注释掉
99        <activity
100            android:name=".ui.UserAuditActivity"
101            android:exported="false"
102            android:label="用户审核" />
103
104        <activity
105            android:name=".ui.RegistrationDetailActivity"
106            android:exported="false"
107            android:label="注册申请详情" />
108        -->
109
110
111        <!-- 用户资料页面 -->
112        <activity
112-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:93:9-95:40
113            android:name="com.opms.ui.profile.ProfileActivity"
113-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:94:13-55
114            android:exported="false" />
114-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:95:13-37
115
116        <!-- 头像查看/编辑页面 -->
117        <activity
117-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:98:9-101:61
118            android:name="com.opms.ui.profile.AvatarViewActivity"
118-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:99:13-58
119            android:exported="false"
119-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:100:13-37
120            android:theme="@style/Theme.OPMS.NoActionBar" />
120-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:101:13-58
121
122        <!-- 个人信息编辑页面 -->
123        <activity
123-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:104:9-107:38
124            android:name="com.opms.ui.profile.ProfileEditActivity"
124-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:105:13-59
125            android:exported="false"
125-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:106:13-37
126            android:label="编辑个人信息" />
126-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:107:13-35
127
128        <!-- 修改密码页面 -->
129        <activity
129-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:110:9-113:36
130            android:name="com.opms.ui.profile.PasswordChangeActivity"
130-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:111:13-62
131            android:exported="false"
131-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:112:13-37
132            android:label="修改密码" />
132-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:113:13-33
133
134        <!-- 用户管理页面 -->
135        <activity
135-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:116:9-119:36
136            android:name="com.opms.ui.system.UserManagementActivity"
136-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:117:13-61
137            android:exported="false"
137-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:118:13-37
138            android:label="用户管理" />
138-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:119:13-33
139
140        <!-- 用户编辑页面 -->
141        <activity
141-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:122:9-125:38
142            android:name="com.opms.ui.system.UserEditActivity"
142-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:123:13-55
143            android:exported="false"
143-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:124:13-37
144            android:label="编辑用户信息" />
144-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:125:13-35
145
146        <!-- 部门管理页面 -->
147        <activity
147-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:128:9-132:61
148            android:name="com.opms.ui.system.DepartmentManagementActivity"
148-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:129:13-67
149            android:exported="false"
149-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:130:13-37
150            android:label="部门管理"
150-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:131:13-33
151            android:theme="@style/Theme.OPMS.NoActionBar" />
151-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:132:13-58
152
153        <!-- 职位管理页面 -->
154        <activity
154-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:135:9-139:61
155            android:name="com.opms.ui.system.PositionManagementActivity"
155-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:136:13-65
156            android:exported="false"
156-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:137:13-37
157            android:label="职位管理"
157-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:138:13-33
158            android:theme="@style/Theme.OPMS.NoActionBar" />
158-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:139:13-58
159
160        <!-- 职位编辑页面 -->
161        <activity
161-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:142:9-146:61
162            android:name="com.opms.ui.system.PositionEditActivity"
162-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:143:13-59
163            android:exported="false"
163-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:144:13-37
164            android:label="职位编辑"
164-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:145:13-33
165            android:theme="@style/Theme.OPMS.NoActionBar" />
165-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:146:13-58
166
167        <!-- 岗位管理页面 -->
168        <activity
168-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:149:9-153:61
169            android:name="com.opms.ui.system.PostManagementActivity"
169-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:150:13-61
170            android:exported="false"
170-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:151:13-37
171            android:label="岗位管理"
171-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:152:13-33
172            android:theme="@style/Theme.OPMS.NoActionBar" />
172-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:153:13-58
173
174        <!-- 岗位编辑页面 -->
175        <activity
175-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:156:9-160:61
176            android:name="com.opms.ui.system.PostEditActivity"
176-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:157:13-55
177            android:exported="false"
177-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:158:13-37
178            android:label="岗位编辑"
178-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:159:13-33
179            android:theme="@style/Theme.OPMS.NoActionBar" />
179-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:160:13-58
180
181        <!-- 流程管理页面 -->
182        <activity
182-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:163:9-167:61
183            android:name="com.opms.ui.system.ProcessTemplateManagementActivity"
183-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:164:13-72
184            android:exported="false"
184-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:165:13-37
185            android:label="流程管理"
185-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:166:13-33
186            android:theme="@style/Theme.OPMS.NoActionBar" />
186-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:167:13-58
187
188        <!-- 流程编辑页面 -->
189        <activity
189-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:170:9-174:61
190            android:name="com.opms.ui.system.ProcessTemplateEditActivity"
190-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:171:13-66
191            android:exported="false"
191-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:172:13-37
192            android:label="流程编辑"
192-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:173:13-33
193            android:theme="@style/Theme.OPMS.NoActionBar" />
193-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:174:13-58
194
195        <!-- 权限管理页面 -->
196        <activity
196-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:177:9-181:61
197            android:name="com.opms.ui.system.PermissionManagementActivity"
197-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:178:13-67
198            android:exported="false"
198-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:179:13-37
199            android:label="权限管理"
199-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:180:13-33
200            android:theme="@style/Theme.OPMS.NoActionBar" />
200-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:181:13-58
201
202        <!-- 权限编辑页面 -->
203        <activity
203-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:184:9-188:61
204            android:name="com.opms.ui.system.PermissionEditActivity"
204-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:185:13-61
205            android:exported="false"
205-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:186:13-37
206            android:label="权限编辑"
206-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:187:13-33
207            android:theme="@style/Theme.OPMS.NoActionBar" />
207-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:188:13-58
208
209        <!-- 流程岗位映射管理页面 -->
210        <activity
210-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:191:9-195:61
211            android:name="com.opms.ui.system.ProcessPostMappingManagementActivity"
211-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:192:13-75
212            android:exported="false"
212-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:193:13-37
213            android:label="流程岗位映射"
213-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:194:13-35
214            android:theme="@style/Theme.OPMS.NoActionBar" />
214-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:195:13-58
215
216        <!-- 流程岗位映射编辑页面 -->
217        <activity
217-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:198:9-202:61
218            android:name="com.opms.ui.system.ProcessPostMappingEditActivity"
218-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:199:13-69
219            android:exported="false"
219-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:200:13-37
220            android:label="流程岗位映射编辑"
220-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:201:13-37
221            android:theme="@style/Theme.OPMS.NoActionBar" />
221-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:202:13-58
222
223        <!-- 用户审核列表页面 -->
224        <activity
224-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:205:9-209:61
225            android:name="com.opms.ui.system.UserAuditListActivity"
225-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:206:13-60
226            android:exported="false"
226-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:207:13-37
227            android:label="用户审核"
227-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:208:13-33
228            android:theme="@style/Theme.OPMS.NoActionBar" />
228-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:209:13-58
229
230        <!-- 用户审核详情页面 -->
231        <activity
231-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:212:9-216:61
232            android:name="com.opms.ui.system.UserAuditActivity"
232-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:213:13-56
233            android:exported="false"
233-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:214:13-37
234            android:label="用户审核"
234-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:215:13-33
235            android:theme="@style/Theme.OPMS.NoActionBar" />
235-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:216:13-58
236
237        <!-- 客户管理页面 -->
238        <activity
238-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:219:9-223:61
239            android:name="com.opms.ui.business.CustomerManagementActivity"
239-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:220:13-67
240            android:exported="false"
240-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:221:13-37
241            android:label="客户管理"
241-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:222:13-33
242            android:theme="@style/Theme.OPMS.NoActionBar" />
242-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:223:13-58
243
244        <!-- 客户编辑页面 -->
245        <activity
245-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:226:9-230:61
246            android:name="com.opms.ui.business.CustomerEditActivity"
246-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:227:13-61
247            android:exported="false"
247-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:228:13-37
248            android:label="客户编辑"
248-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:229:13-33
249            android:theme="@style/Theme.OPMS.NoActionBar" />
249-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:230:13-58
250
251        <!-- 产品管理页面 -->
252        <activity
252-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:233:9-237:61
253            android:name="com.opms.ui.business.ProductManagementActivity"
253-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:234:13-66
254            android:exported="false"
254-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:235:13-37
255            android:label="产品管理"
255-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:236:13-33
256            android:theme="@style/Theme.OPMS.NoActionBar" />
256-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:237:13-58
257
258        <!-- 产品编辑页面 -->
259        <activity
259-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:240:9-244:61
260            android:name="com.opms.ui.business.ProductEditActivity"
260-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:241:13-60
261            android:exported="false"
261-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:242:13-37
262            android:label="产品编辑"
262-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:243:13-33
263            android:theme="@style/Theme.OPMS.NoActionBar" />
263-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:244:13-58
264
265        <!-- 订单管理页面 -->
266        <activity
266-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:247:9-251:61
267            android:name="com.opms.ui.business.OrderManagementActivity"
267-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:248:13-64
268            android:exported="false"
268-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:249:13-37
269            android:label="订单管理"
269-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:250:13-33
270            android:theme="@style/Theme.OPMS.NoActionBar" />
270-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:251:13-58
271
272        <!-- 部件管理页面 -->
273        <activity
273-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:254:9-258:61
274            android:name="com.opms.ui.business.ComponentManagementActivity"
274-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:255:13-68
275            android:exported="false"
275-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:256:13-37
276            android:label="部件管理"
276-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:257:13-33
277            android:theme="@style/Theme.OPMS.NoActionBar" />
277-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:258:13-58
278
279        <!-- 部件编辑页面 -->
280        <activity
280-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:261:9-265:61
281            android:name="com.opms.ui.business.ComponentEditActivity"
281-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:262:13-62
282            android:exported="false"
282-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:263:13-37
283            android:label="部件编辑"
283-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:264:13-33
284            android:theme="@style/Theme.OPMS.NoActionBar" />
284-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:265:13-58
285
286        <provider
286-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\Android\GradleRepository\caches\8.11.1\transforms\d2d730939c9267fb637b0c082d2bc5d3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
287            android:name="androidx.startup.InitializationProvider"
287-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\Android\GradleRepository\caches\8.11.1\transforms\d2d730939c9267fb637b0c082d2bc5d3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
288            android:authorities="com.opms.androidx-startup"
288-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\Android\GradleRepository\caches\8.11.1\transforms\d2d730939c9267fb637b0c082d2bc5d3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
289            android:exported="false" >
289-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\Android\GradleRepository\caches\8.11.1\transforms\d2d730939c9267fb637b0c082d2bc5d3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
290            <meta-data
290-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\Android\GradleRepository\caches\8.11.1\transforms\d2d730939c9267fb637b0c082d2bc5d3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
291                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
291-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\Android\GradleRepository\caches\8.11.1\transforms\d2d730939c9267fb637b0c082d2bc5d3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
292                android:value="androidx.startup" />
292-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\Android\GradleRepository\caches\8.11.1\transforms\d2d730939c9267fb637b0c082d2bc5d3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
293            <meta-data
293-->[androidx.emoji2:emoji2:1.2.0] D:\Android\GradleRepository\caches\8.11.1\transforms\99c1f6faa94b27b88ccd35ada499447a\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
294                android:name="androidx.emoji2.text.EmojiCompatInitializer"
294-->[androidx.emoji2:emoji2:1.2.0] D:\Android\GradleRepository\caches\8.11.1\transforms\99c1f6faa94b27b88ccd35ada499447a\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
295                android:value="androidx.startup" />
295-->[androidx.emoji2:emoji2:1.2.0] D:\Android\GradleRepository\caches\8.11.1\transforms\99c1f6faa94b27b88ccd35ada499447a\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
296            <meta-data
296-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
297                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
297-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
298                android:value="androidx.startup" />
298-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
299        </provider>
300
301        <uses-library
301-->[androidx.window:window:1.0.0] D:\Android\GradleRepository\caches\8.11.1\transforms\a71b722315b7fe0b1a8423709a320bad\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
302            android:name="androidx.window.extensions"
302-->[androidx.window:window:1.0.0] D:\Android\GradleRepository\caches\8.11.1\transforms\a71b722315b7fe0b1a8423709a320bad\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
303            android:required="false" />
303-->[androidx.window:window:1.0.0] D:\Android\GradleRepository\caches\8.11.1\transforms\a71b722315b7fe0b1a8423709a320bad\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
304        <uses-library
304-->[androidx.window:window:1.0.0] D:\Android\GradleRepository\caches\8.11.1\transforms\a71b722315b7fe0b1a8423709a320bad\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
305            android:name="androidx.window.sidecar"
305-->[androidx.window:window:1.0.0] D:\Android\GradleRepository\caches\8.11.1\transforms\a71b722315b7fe0b1a8423709a320bad\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
306            android:required="false" />
306-->[androidx.window:window:1.0.0] D:\Android\GradleRepository\caches\8.11.1\transforms\a71b722315b7fe0b1a8423709a320bad\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
307
308        <service
308-->[androidx.room:room-runtime:2.6.1] D:\Android\GradleRepository\caches\8.11.1\transforms\145b37cfffdd8979d8b3e20a1994d707\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
309            android:name="androidx.room.MultiInstanceInvalidationService"
309-->[androidx.room:room-runtime:2.6.1] D:\Android\GradleRepository\caches\8.11.1\transforms\145b37cfffdd8979d8b3e20a1994d707\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
310            android:directBootAware="true"
310-->[androidx.room:room-runtime:2.6.1] D:\Android\GradleRepository\caches\8.11.1\transforms\145b37cfffdd8979d8b3e20a1994d707\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
311            android:exported="false" />
311-->[androidx.room:room-runtime:2.6.1] D:\Android\GradleRepository\caches\8.11.1\transforms\145b37cfffdd8979d8b3e20a1994d707\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
312
313        <receiver
313-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
314            android:name="androidx.profileinstaller.ProfileInstallReceiver"
314-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
315            android:directBootAware="false"
315-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
316            android:enabled="true"
316-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
317            android:exported="true"
317-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
318            android:permission="android.permission.DUMP" >
318-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
319            <intent-filter>
319-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
320                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
320-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
320-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
321            </intent-filter>
322            <intent-filter>
322-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
323                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
323-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
323-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
324            </intent-filter>
325            <intent-filter>
325-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
326                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
326-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
326-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
327            </intent-filter>
328            <intent-filter>
328-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
329                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
329-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
329-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
330            </intent-filter>
331        </receiver>
332    </application>
333
334</manifest>
