package com.opms.ui.system.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.opms.R;
import com.opms.data.model.response.ProcessPostMappingResponse;

import java.util.ArrayList;
import java.util.List;

/**
 * 流程岗位映射适配器
 */
public class ProcessPostMappingAdapter extends RecyclerView.Adapter<ProcessPostMappingAdapter.ViewHolder> {

    private final Context context;
    private List<ProcessPostMappingResponse> mappings;
    private OnProcessPostMappingClickListener listener;

    public ProcessPostMappingAdapter(Context context) {
        this.context = context;
        this.mappings = new ArrayList<>();
    }

    public void setMappings(List<ProcessPostMappingResponse> mappings) {
        this.mappings = mappings != null ? mappings : new ArrayList<>();
        notifyDataSetChanged();
    }

    public void setOnProcessPostMappingClickListener(OnProcessPostMappingClickListener listener) {
        this.listener = listener;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_process_post_mapping, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        ProcessPostMappingResponse mapping = mappings.get(position);
        holder.bind(mapping);
    }

    @Override
    public int getItemCount() {
        return mappings.size();
    }

    public interface OnProcessPostMappingClickListener {
        void onProcessPostMappingClick(ProcessPostMappingResponse mapping);

        void onProcessPostMappingDelete(ProcessPostMappingResponse mapping);
    }

    class ViewHolder extends RecyclerView.ViewHolder {
        private final TextView tvProcessName;
        private final TextView tvProcessCode;
        private final TextView tvPostName;
        private final TextView tvPostCode;
        private final TextView tvRemark;
        private final View statusIndicator;
        private final TextView tvStatus;
        private final ImageButton btnDelete;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            tvProcessName = itemView.findViewById(R.id.tv_process_name);
            tvProcessCode = itemView.findViewById(R.id.tv_process_code);
            tvPostName = itemView.findViewById(R.id.tv_post_name);
            tvPostCode = itemView.findViewById(R.id.tv_post_code);
            tvRemark = itemView.findViewById(R.id.tv_remark);
            statusIndicator = itemView.findViewById(R.id.status_indicator);
            tvStatus = itemView.findViewById(R.id.tv_status);
            btnDelete = itemView.findViewById(R.id.btn_delete);

            // 设置点击事件
            itemView.setOnClickListener(v -> {
                if (listener != null && getAdapterPosition() != RecyclerView.NO_POSITION) {
                    listener.onProcessPostMappingClick(mappings.get(getAdapterPosition()));
                }
            });

            btnDelete.setOnClickListener(v -> {
                if (listener != null && getAdapterPosition() != RecyclerView.NO_POSITION) {
                    listener.onProcessPostMappingDelete(mappings.get(getAdapterPosition()));
                }
            });
        }

        public void bind(ProcessPostMappingResponse mapping) {
            // 设置流程信息
            tvProcessName.setText(mapping.getProcessName());
            tvProcessCode.setText(mapping.getProcessCode());

            // 设置岗位信息
            tvPostName.setText(mapping.getPostName());
            tvPostCode.setText(mapping.getPostCode());

            // 设置备注信息
            String remark = mapping.getRemark();
            if (remark != null && !remark.trim().isEmpty()) {
                tvRemark.setText("备注：" + remark);
                tvRemark.setVisibility(View.VISIBLE);
            } else {
                tvRemark.setVisibility(View.GONE);
            }

            // 设置状态
            boolean isActive = "1".equals(mapping.getStatus());
            if (isActive) {
                statusIndicator.setBackground(ContextCompat.getDrawable(context, R.drawable.circle_green));
                tvStatus.setText("启用");
                tvStatus.setTextColor(ContextCompat.getColor(context, R.color.success));
            } else {
                statusIndicator.setBackground(ContextCompat.getDrawable(context, R.drawable.circle_red));
                tvStatus.setText("禁用");
                tvStatus.setTextColor(ContextCompat.getColor(context, R.color.error));
            }
        }
    }
}
