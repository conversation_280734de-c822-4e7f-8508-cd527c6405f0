package com.opms.ui.user;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.google.android.material.snackbar.Snackbar;
import com.opms.R;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.UserResponse;
import com.opms.data.repository.UserAuditRepository;
import com.opms.databinding.FragmentUserAuditBinding;
import com.opms.ui.system.UserAuditActivity;
import com.opms.ui.system.adapter.PendingUserAdapter;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class UserAuditFragment extends Fragment implements SwipeRefreshLayout.OnRefreshListener, PendingUserAdapter.OnUserClickListener {

    private static final String TAG = "UserAuditFragment";

    @Inject
    UserAuditRepository userAuditRepository;

    private FragmentUserAuditBinding binding;
    private PendingUserAdapter adapter;
    private List<UserResponse> allUsers;
    private List<UserResponse> filteredUsers;
    private String currentStatus = "0";
    private String currentKeyword = "";

    private ActivityResultLauncher<Intent> auditActivityLauncher;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentUserAuditBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        setupActivityResultLauncher();
        setupRecyclerView();
        setupSearchAndFilter();
        loadPendingUsers();
    }

    private void setupActivityResultLauncher() {
        auditActivityLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (result.getResultCode() == getActivity().RESULT_OK) {
                        // 刷新列表
                        loadPendingUsers();
                    }
                }
        );
    }

    private void setupRecyclerView() {
        adapter = new PendingUserAdapter(requireContext());
        adapter.setOnUserClickListener(this);

        binding.rvUsers.setLayoutManager(new LinearLayoutManager(requireContext()));
        binding.rvUsers.setAdapter(adapter);

        // 设置下拉刷新
        binding.swipeRefresh.setOnRefreshListener(this);
        binding.swipeRefresh.setColorSchemeResources(R.color.primary);
    }

    private void setupSearchAndFilter() {
        // 搜索功能
        binding.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                currentKeyword = s.toString().trim();
                filterUsers();
            }
        });

        // 状态筛选
        binding.chipGroupStatus.setOnCheckedStateChangeListener((group, checkedIds) -> {
            if (!checkedIds.isEmpty()) {
                int checkedId = checkedIds.get(0);
                if (checkedId == R.id.chip_pending) {
                    currentStatus = "0";
                } else if (checkedId == R.id.chip_approved) {
                    currentStatus = "1";
                } else if (checkedId == R.id.chip_rejected) {
                    currentStatus = "-1";
                }
                filterUsers();
            }
        });
    }

    private void loadPendingUsers() {
        binding.swipeRefresh.setRefreshing(true);

        userAuditRepository.getPendingUsers().enqueue(new Callback<ApiResponse<List<UserResponse>>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<List<UserResponse>>> call,
                                   @NonNull Response<ApiResponse<List<UserResponse>>> response) {
                binding.swipeRefresh.setRefreshing(false);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<List<UserResponse>> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        allUsers = apiResponse.getData();
                        filterUsers();
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "获取用户列表失败");
                    }
                } else {
                    showError("获取用户列表失败");
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<List<UserResponse>>> call, @NonNull Throwable t) {
                binding.swipeRefresh.setRefreshing(false);
                Log.e(TAG, "获取用户列表失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    private void filterUsers() {
        if (allUsers == null) {
            return;
        }

        filteredUsers = new ArrayList<>();
        for (UserResponse user : allUsers) {
            // 状态筛选
            if (!currentStatus.equals(user.getStatus())) {
                continue;
            }

            // 关键字搜索
            if (!currentKeyword.isEmpty()) {
                String keyword = currentKeyword.toLowerCase();
                boolean matches = false;

                if (user.getUsername() != null && user.getUsername().toLowerCase().contains(keyword)) {
                    matches = true;
                }
                if (user.getName() != null && user.getName().toLowerCase().contains(keyword)) {
                    matches = true;
                }
                if (user.getPhone() != null && user.getPhone().contains(keyword)) {
                    matches = true;
                }

                if (!matches) {
                    continue;
                }
            }

            filteredUsers.add(user);
        }

        adapter.setUsers(filteredUsers);
        updateEmptyState();
    }

    private void updateEmptyState() {
        if (filteredUsers == null || filteredUsers.isEmpty()) {
            binding.layoutEmpty.setVisibility(View.VISIBLE);
            binding.rvUsers.setVisibility(View.GONE);
        } else {
            binding.layoutEmpty.setVisibility(View.GONE);
            binding.rvUsers.setVisibility(View.VISIBLE);
        }
    }

    private void showError(String message) {
        if (getView() != null) {
            Snackbar.make(getView(), message, Snackbar.LENGTH_LONG).show();
        }
    }

    @Override
    public void onRefresh() {
        loadPendingUsers();
    }

    @Override
    public void onUserClick(UserResponse user, int position) {
        // 点击用户项，跳转到审核页面
        startAuditActivity(user);
    }

    @Override
    public void onAuditClick(UserResponse user, int position) {
        // 点击审核按钮，跳转到审核页面
        startAuditActivity(user);
    }

    private void startAuditActivity(UserResponse user) {
        Intent intent = new Intent(requireContext(), UserAuditActivity.class);
        intent.putExtra("user_id", user.getId());
        auditActivityLauncher.launch(intent);
        // 添加滑动动画效果
        if (getActivity() != null) {
            getActivity().overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}