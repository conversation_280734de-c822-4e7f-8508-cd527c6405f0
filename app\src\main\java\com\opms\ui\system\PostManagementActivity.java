package com.opms.ui.system;

import android.app.AlertDialog;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.google.android.material.snackbar.Snackbar;
import com.opms.R;
import com.opms.data.model.request.PostRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.PostResponse;
import com.opms.data.repository.PostRepository;
import com.opms.databinding.ActivityPostManagementBinding;
import com.opms.ui.system.adapter.PostAdapter;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class PostManagementActivity extends AppCompatActivity
        implements SwipeRefreshLayout.OnRefreshListener, PostAdapter.OnPostClickListener {

    private static final String TAG = "PostManagement";

    @Inject
    PostRepository postRepository;

    private ActivityPostManagementBinding binding;
    private PostAdapter adapter;
    private List<PostResponse> allPosts;
    private List<PostResponse> filteredPosts;
    private boolean isToolbarExpanded = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityPostManagementBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setupToolbar();
        setupRecyclerView();
        setupButtons();
        loadPosts();
    }

    private void setupToolbar() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("岗位管理");
        }
    }

    private void setupRecyclerView() {
        adapter = new PostAdapter(this);
        adapter.setOnPostClickListener(this);

        binding.rvPosts.setLayoutManager(new LinearLayoutManager(this));
        binding.rvPosts.setAdapter(adapter);

        binding.swipeRefresh.setOnRefreshListener(this);
        binding.swipeRefresh.setColorSchemeResources(R.color.primary);
    }

    private void setupButtons() {
        binding.fabAdd.setOnClickListener(v -> startAddActivity());
        binding.btnToggleToolbar.setOnClickListener(v -> toggleToolbar());
        binding.btnToggleSearch.setOnClickListener(v -> {
            if (!isToolbarExpanded) {
                toggleToolbar();
            }
            binding.etSearch.requestFocus();
        });

        setupSearch();
    }

    private void setupSearch() {
        binding.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                filterPosts(s.toString().trim());
            }
        });
    }

    private void filterPosts(String query) {
        if (allPosts == null) return;

        if (query.isEmpty()) {
            filteredPosts = new ArrayList<>(allPosts);
        } else {
            filteredPosts = new ArrayList<>();
            String lowerQuery = query.toLowerCase();
            for (PostResponse post : allPosts) {
                if (post.getName().toLowerCase().contains(lowerQuery) ||
                        post.getCode().toLowerCase().contains(lowerQuery)) {
                    filteredPosts.add(post);
                }
            }
        }

        adapter.setPosts(filteredPosts);
        updatePostCount();
    }

    private void loadPosts() {
        showLoading(true);

        postRepository.getPosts().enqueue(new Callback<ApiResponse<List<PostResponse>>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<List<PostResponse>>> call,
                                   @NonNull Response<ApiResponse<List<PostResponse>>> response) {
                showLoading(false);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<List<PostResponse>> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        allPosts = apiResponse.getData();
                        filteredPosts = new ArrayList<>(allPosts);
                        adapter.setPosts(filteredPosts);
                        updatePostCount();
                        hideEmptyView();
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "获取岗位列表失败");
                        showEmptyView();
                    }
                } else {
                    showError("获取岗位列表失败");
                    showEmptyView();
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<List<PostResponse>>> call, @NonNull Throwable t) {
                showLoading(false);
                Log.e(TAG, "获取岗位列表失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
                showEmptyView();
            }
        });
    }

    private void startAddActivity() {
        Intent intent = new Intent(this, PostEditActivity.class);
        startActivity(intent);
        // 添加滑动动画效果
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }

    private void showLoading(boolean show) {
        binding.progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
        if (binding.swipeRefresh.isRefreshing() && !show) {
            binding.swipeRefresh.setRefreshing(false);
        }
    }

    private void showEmptyView() {
        binding.llEmpty.setVisibility(View.VISIBLE);
        binding.rvPosts.setVisibility(View.GONE);
    }

    private void hideEmptyView() {
        binding.llEmpty.setVisibility(View.GONE);
        binding.rvPosts.setVisibility(View.VISIBLE);
    }

    private void showError(String message) {
        Snackbar.make(binding.getRoot(), message, Snackbar.LENGTH_SHORT).show();
    }

    private void updatePostCount() {
        if (allPosts != null) {
            int totalCount = allPosts.size();
            binding.tvPostCount.setText("共 " + totalCount + " 个岗位");
        }
    }

    private void toggleToolbar() {
        if (isToolbarExpanded) {
            collapseToolbar();
        } else {
            expandToolbar();
        }
    }

    private void expandToolbar() {
        isToolbarExpanded = true;
        binding.btnToggleToolbar.setIcon(getDrawable(R.drawable.ic_expand_less));
        binding.btnToggleToolbar.setText("收起");
        binding.cardExpandableTools.setVisibility(View.VISIBLE);
    }

    private void collapseToolbar() {
        isToolbarExpanded = false;
        binding.btnToggleToolbar.setIcon(getDrawable(R.drawable.ic_expand_more));
        binding.btnToggleToolbar.setText("工具");
        binding.cardExpandableTools.setVisibility(View.GONE);
    }

    @Override
    public void onRefresh() {
        loadPosts();
    }

    @Override
    public void onPostEdit(PostResponse post, int itemPosition) {
        // 进入编辑模式
        adapter.setEditingPosition(itemPosition);
    }

    @Override
    public void onPostSave(PostResponse post, int itemPosition, String newName, boolean newStatus) {
        // 验证名称是否重复
        if (allPosts != null) {
            for (PostResponse p : allPosts) {
                if (p.getId() != post.getId() && newName.equals(p.getName())) {
                    Snackbar.make(binding.getRoot(), "岗位名称已存在", Snackbar.LENGTH_SHORT).show();
                    return;
                }
            }
        }

        // 保存更改
        int id = post.getId();
        PostRequest request = new PostRequest();
        request.setId(id);
        request.setName(newName);
        request.setCode(post.getCode()); // 代码不可修改
        request.setStatus(newStatus ? "1" : "0");

        updatePost(id, request, itemPosition);
    }

    @Override
    public void onPostCancel(int itemPosition) {
        // 取消编辑模式
        adapter.cancelEditing();
    }

    @Override
    public void onPostDelete(PostResponse post) {
        showDeleteConfirmDialog(post);
    }

    private void updatePost(int postId, PostRequest request, int itemPosition) {
        postRepository.updatePost(postId, request)
                .enqueue(new Callback<ApiResponse<PostResponse>>() {
                    @Override
                    public void onResponse(@NonNull Call<ApiResponse<PostResponse>> call,
                                           @NonNull Response<ApiResponse<PostResponse>> response) {
                        if (response.isSuccessful() && response.body() != null) {
                            ApiResponse<PostResponse> apiResponse = response.body();
                            if (apiResponse.isSuccess()) {
                                Snackbar.make(binding.getRoot(), "岗位更新成功", Snackbar.LENGTH_SHORT).show();

                                // 更新本地数据
                                PostResponse updatedPost = apiResponse.getData();
                                if (updatedPost != null && allPosts != null) {
                                    for (int i = 0; i < allPosts.size(); i++) {
                                        if (allPosts.get(i).getId() == postId) {
                                            allPosts.set(i, updatedPost);
                                            break;
                                        }
                                    }
                                    // 更新过滤后的列表
                                    filterPosts(binding.etSearch.getText().toString().trim());
                                }

                                // 退出编辑模式
                                adapter.cancelEditing();
                            } else {
                                showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "更新失败");
                            }
                        } else {
                            showError("更新失败");
                        }
                    }

                    @Override
                    public void onFailure(@NonNull Call<ApiResponse<PostResponse>> call, @NonNull Throwable t) {
                        Log.e(TAG, "更新岗位失败: " + t.getMessage(), t);
                        showError("网络错误: " + t.getMessage());
                    }
                });
    }

    private void showDeleteConfirmDialog(PostResponse post) {
        new AlertDialog.Builder(this)
                .setTitle("删除岗位")
                .setMessage("确定要删除岗位 \"" + post.getName() + "\" 吗？")
                .setPositiveButton("删除", (dialog, which) -> deletePost(post))
                .setNegativeButton("取消", null)
                .show();
    }

    private void deletePost(PostResponse post) {
        int id = post.getId();
        PostRequest request = new PostRequest();
        request.setId(id);
        postRepository.deletePost(request)
                .enqueue(new Callback<ApiResponse<Void>>() {
                    @Override
                    public void onResponse(@NonNull Call<ApiResponse<Void>> call,
                                           @NonNull Response<ApiResponse<Void>> response) {
                        if (response.isSuccessful() && response.body() != null) {
                            ApiResponse<Void> apiResponse = response.body();
                            if (apiResponse.isSuccess()) {
                                Snackbar.make(binding.getRoot(), "岗位删除成功", Snackbar.LENGTH_SHORT).show();
                                loadPosts();
                            } else {
                                showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "删除失败");
                            }
                        } else {
                            showError("删除失败");
                        }
                    }

                    @Override
                    public void onFailure(@NonNull Call<ApiResponse<Void>> call, @NonNull Throwable t) {
                        Log.e(TAG, "删除岗位失败: " + t.getMessage(), t);
                        showError("网络错误: " + t.getMessage());
                    }
                });
    }

    @Override
    protected void onResume() {
        super.onResume();
        loadPosts();
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void finish() {
        super.finish();
        // 添加返回动画
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right);
    }
}
