package com.opms.ui.system.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.checkbox.MaterialCheckBox;
import com.opms.R;
import com.opms.common.enums.BusinessModule;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 业务模块复选框适配器
 */
public class BusinessModuleAdapter extends RecyclerView.Adapter<BusinessModuleAdapter.ViewHolder> {
    private final Context context;
    private final List<BusinessModule> modules;
    private final Set<String> selectedModuleCodes;
    private OnModuleSelectionChangeListener onModuleSelectionChangeListener;

    public BusinessModuleAdapter(Context context) {
        this.context = context;
        this.modules = new ArrayList<>();
        this.selectedModuleCodes = new HashSet<>();

        // 添加所有业务模块
        for (BusinessModule module : BusinessModule.values()) {
            modules.add(module);
        }
    }

    public void setOnModuleSelectionChangeListener(OnModuleSelectionChangeListener listener) {
        this.onModuleSelectionChangeListener = listener;
    }

    public void setSelectedModules(Set<String> selectedCodes) {
        this.selectedModuleCodes.clear();
        if (selectedCodes != null) {
            this.selectedModuleCodes.addAll(selectedCodes);
        }
        notifyDataSetChanged();
    }

    public Set<String> getSelectedModuleCodes() {
        return new HashSet<>(selectedModuleCodes);
    }

    public void selectAll() {
        selectedModuleCodes.clear();
        for (BusinessModule module : modules) {
            selectedModuleCodes.add(module.getCode());
        }
        notifyDataSetChanged();
        if (onModuleSelectionChangeListener != null) {
            onModuleSelectionChangeListener.onSelectionChanged(getSelectedCount(), getTotalCount());
        }
    }

    public void clearAll() {
        selectedModuleCodes.clear();
        notifyDataSetChanged();
        if (onModuleSelectionChangeListener != null) {
            onModuleSelectionChangeListener.onSelectionChanged(getSelectedCount(), getTotalCount());
        }
    }

    public int getSelectedCount() {
        return selectedModuleCodes.size();
    }

    public int getTotalCount() {
        return modules.size();
    }

    public boolean isAllSelected() {
        return selectedModuleCodes.size() == modules.size();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_business_module_checkbox, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        BusinessModule module = modules.get(position);
        holder.bind(module);
    }

    @Override
    public int getItemCount() {
        return modules.size();
    }

    /**
     * 模块选择变化监听器
     */
    public interface OnModuleSelectionChangeListener {
        void onSelectionChanged(int selectedCount, int totalCount);
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        private final ImageView ivModuleIcon;
        private final TextView tvModuleName;
        private final TextView tvModuleCode;
        private final MaterialCheckBox cbModule;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            ivModuleIcon = itemView.findViewById(R.id.iv_module_icon);
            tvModuleName = itemView.findViewById(R.id.tv_module_name);
            tvModuleCode = itemView.findViewById(R.id.tv_module_code);
            cbModule = itemView.findViewById(R.id.cb_module);
        }

        public void bind(BusinessModule module) {
            tvModuleName.setText(module.getName());
            tvModuleCode.setText(module.getCode());

            // 设置图标
            setModuleIcon(module);

            // 设置选中状态
            boolean isSelected = selectedModuleCodes.contains(module.getCode());
            cbModule.setChecked(isSelected);

            // 设置点击事件
            itemView.setOnClickListener(v -> {
                boolean newState = !cbModule.isChecked();
                cbModule.setChecked(newState);

                if (newState) {
                    selectedModuleCodes.add(module.getCode());
                } else {
                    selectedModuleCodes.remove(module.getCode());
                }

                if (onModuleSelectionChangeListener != null) {
                    onModuleSelectionChangeListener.onSelectionChanged(getSelectedCount(), getTotalCount());
                }
            });
        }

        private void setModuleIcon(BusinessModule module) {
            int iconRes;
            switch (module) {
                case CUSTOMER:
                    iconRes = R.drawable.ic_person;
                    break;
                case PRODUCT:
                    iconRes = R.drawable.ic_business;
                    break;
                case ORDER:
                    iconRes = R.drawable.ic_business;
                    break;
                case ORDER_TRACKING:
                    iconRes = R.drawable.ic_business;
                    break;
                case PROCESS:
                    iconRes = R.drawable.ic_settings;
                    break;
                case USER:
                    iconRes = R.drawable.ic_person;
                    break;
                case DEPARTMENT:
                    iconRes = R.drawable.ic_business;
                    break;
                case POSITION:
                    iconRes = R.drawable.ic_business;
                    break;
                case POST:
                    iconRes = R.drawable.ic_business;
                    break;
                case PERMISSION:
                    iconRes = R.drawable.ic_security;
                    break;
                case SYSTEM:
                    iconRes = R.drawable.ic_settings;
                    break;
                default:
                    iconRes = R.drawable.ic_business;
                    break;
            }
            ivModuleIcon.setImageResource(iconRes);
        }
    }
}
