package com.opms.common.constants;

import android.util.Log;

public class EnvironmentConfig {
    // 环境类型
    public static final boolean IS_EMULATOR = true; // 设置为 true 表示在模拟器上运行，false 表示在真机上运行
    // 调试模式
    public static final boolean DEBUG = true;
    private static final String TAG = "EnvironmentConfig";

    // 获取基础URL
    public static String getBaseUrl() {
        String url;
        if (IS_EMULATOR) {
            // 尝试不同的模拟器地址
            if (DEBUG) {
                url = "http://********:3007/";
                Log.d(TAG, "Using emulator URL: " + url);
            } else {
                url = "http://localhost:3007/";
                Log.d(TAG, "Using localhost URL: " + url);
            }
        } else {
            url = "http://***********:3007/";
            Log.d(TAG, "Using real device URL: " + url);
        }
        return url;
    }

    // 检查服务器是否可访问
    public static boolean isServerReachable() {
        try {
            java.net.InetAddress.getByName(getBaseUrl().replace("http://", "").replace("/", ""));
            return true;
        } catch (Exception e) {
            Log.e(TAG, "Server not reachable: " + e.getMessage());
            return false;
        }
    }
} 