// Generated by view binder compiler. Do not edit!
package com.opms.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.opms.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemProductComponentBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final ImageView btnDeleteComponent;

  @NonNull
  public final ImageView btnEditComponent;

  @NonNull
  public final MaterialCardView cardComponent;

  @NonNull
  public final TextInputEditText etComponentQuantity;

  @NonNull
  public final ImageView ivComponentImage;

  @NonNull
  public final TextInputLayout tilComponentQuantity;

  @NonNull
  public final TextView tvComponentCode;

  @NonNull
  public final TextView tvComponentModel;

  @NonNull
  public final TextView tvComponentName;

  @NonNull
  public final TextView tvComponentQuantity;

  @NonNull
  public final TextView tvComponentStandard;

  @NonNull
  public final TextView tvComponentStatus;

  private ItemProductComponentBinding(@NonNull MaterialCardView rootView,
      @NonNull ImageView btnDeleteComponent, @NonNull ImageView btnEditComponent,
      @NonNull MaterialCardView cardComponent, @NonNull TextInputEditText etComponentQuantity,
      @NonNull ImageView ivComponentImage, @NonNull TextInputLayout tilComponentQuantity,
      @NonNull TextView tvComponentCode, @NonNull TextView tvComponentModel,
      @NonNull TextView tvComponentName, @NonNull TextView tvComponentQuantity,
      @NonNull TextView tvComponentStandard, @NonNull TextView tvComponentStatus) {
    this.rootView = rootView;
    this.btnDeleteComponent = btnDeleteComponent;
    this.btnEditComponent = btnEditComponent;
    this.cardComponent = cardComponent;
    this.etComponentQuantity = etComponentQuantity;
    this.ivComponentImage = ivComponentImage;
    this.tilComponentQuantity = tilComponentQuantity;
    this.tvComponentCode = tvComponentCode;
    this.tvComponentModel = tvComponentModel;
    this.tvComponentName = tvComponentName;
    this.tvComponentQuantity = tvComponentQuantity;
    this.tvComponentStandard = tvComponentStandard;
    this.tvComponentStatus = tvComponentStatus;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemProductComponentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemProductComponentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_product_component, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemProductComponentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_delete_component;
      ImageView btnDeleteComponent = ViewBindings.findChildViewById(rootView, id);
      if (btnDeleteComponent == null) {
        break missingId;
      }

      id = R.id.btn_edit_component;
      ImageView btnEditComponent = ViewBindings.findChildViewById(rootView, id);
      if (btnEditComponent == null) {
        break missingId;
      }

      MaterialCardView cardComponent = (MaterialCardView) rootView;

      id = R.id.et_component_quantity;
      TextInputEditText etComponentQuantity = ViewBindings.findChildViewById(rootView, id);
      if (etComponentQuantity == null) {
        break missingId;
      }

      id = R.id.iv_component_image;
      ImageView ivComponentImage = ViewBindings.findChildViewById(rootView, id);
      if (ivComponentImage == null) {
        break missingId;
      }

      id = R.id.til_component_quantity;
      TextInputLayout tilComponentQuantity = ViewBindings.findChildViewById(rootView, id);
      if (tilComponentQuantity == null) {
        break missingId;
      }

      id = R.id.tv_component_code;
      TextView tvComponentCode = ViewBindings.findChildViewById(rootView, id);
      if (tvComponentCode == null) {
        break missingId;
      }

      id = R.id.tv_component_model;
      TextView tvComponentModel = ViewBindings.findChildViewById(rootView, id);
      if (tvComponentModel == null) {
        break missingId;
      }

      id = R.id.tv_component_name;
      TextView tvComponentName = ViewBindings.findChildViewById(rootView, id);
      if (tvComponentName == null) {
        break missingId;
      }

      id = R.id.tv_component_quantity;
      TextView tvComponentQuantity = ViewBindings.findChildViewById(rootView, id);
      if (tvComponentQuantity == null) {
        break missingId;
      }

      id = R.id.tv_component_standard;
      TextView tvComponentStandard = ViewBindings.findChildViewById(rootView, id);
      if (tvComponentStandard == null) {
        break missingId;
      }

      id = R.id.tv_component_status;
      TextView tvComponentStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvComponentStatus == null) {
        break missingId;
      }

      return new ItemProductComponentBinding((MaterialCardView) rootView, btnDeleteComponent,
          btnEditComponent, cardComponent, etComponentQuantity, ivComponentImage,
          tilComponentQuantity, tvComponentCode, tvComponentModel, tvComponentName,
          tvComponentQuantity, tvComponentStandard, tvComponentStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
