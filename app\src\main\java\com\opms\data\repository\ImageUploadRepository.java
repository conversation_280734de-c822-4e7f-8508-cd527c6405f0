package com.opms.data.repository;

import android.content.Context;
import android.net.Uri;

import com.opms.common.enums.BusinessImgType;
import com.opms.common.utils.ImageUploadUtils;

import java.io.File;
import java.util.List;

/**
 * 图片上传Repository接口
 */
public interface ImageUploadRepository {

    /**
     * 上传图片（从URI）
     *
     * @param context      上下文
     * @param imageUri     图片URI
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @param operator     操作人
     * @param callback     回调接口
     */
    void uploadImage(Context context,
                     Uri imageUri,
                     BusinessImgType businessType,
                     String businessId,
                     String operator,
                     ImageUploadUtils.ImageUploadCallback callback);

    /**
     * 上传图片（从文件）
     *
     * @param imageFile    图片文件
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @param operator     操作人
     * @param callback     回调接口
     */
    void uploadImageFile(File imageFile,
                         BusinessImgType businessType,
                         String businessId,
                         String operator,
                         ImageUploadUtils.ImageUploadCallback callback);

    /**
     * 上传客户图片（兼容性方法）
     *
     * @param context    上下文
     * @param imageUri   图片URI
     * @param customerId 客户ID
     * @param operator   操作人
     * @param callback   回调接口
     */
    void uploadCustomerImage(Context context,
                             Uri imageUri,
                             int customerId,
                             String operator,
                             ImageUploadUtils.ImageUploadCallback callback);

    /**
     * 上传用户头像（兼容性方法）
     *
     * @param context  上下文
     * @param imageUri 图片URI
     * @param userId   用户ID
     * @param operator 操作人
     * @param callback 回调接口
     */
    void uploadUserAvatar(Context context,
                          Uri imageUri,
                          int userId,
                          String operator,
                          ImageUploadUtils.ImageUploadCallback callback);

    /**
     * 批量上传图片
     *
     * @param context      上下文
     * @param imageUris    图片URI列表
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @param operator     操作人
     * @param callback     回调接口
     */
    void uploadMultipleImages(Context context,
                              List<Uri> imageUris,
                              BusinessImgType businessType,
                              String businessId,
                              String operator,
                              ImageUploadUtils.MultiImageUploadCallback callback);

    /**
     * 删除图片
     *
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @param operator     操作人
     * @param imageUrl     要删除的图片URL
     * @param callback     回调接口
     */
    void deleteImage(BusinessImgType businessType,
                     String businessId,
                     String operator,
                     String imageUrl,
                     ImageUploadUtils.ImageDeleteCallback callback);

    /**
     * 获取业务对象的所有图片
     *
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @param callback     回调接口
     */
    void getImages(BusinessImgType businessType,
                   String businessId,
                   ImageUploadUtils.ImageListCallback callback);
}
