package com.opms.data.model.response;

import com.google.gson.annotations.SerializedName;
import com.opms.common.enums.Gender;
import com.opms.common.enums.RoleType;
import com.opms.common.utils.DateUtils;
import com.opms.common.utils.DisplayUtils;

public class UserResponse {
    @SerializedName("id")
    private int id;
    @SerializedName("username")
    private String username;

    @SerializedName("name")
    private String name;

    @SerializedName("gender")
    private String gender;

    @SerializedName("birthday")
    private String birthday;

    @SerializedName("idCard")
    private String idCard;

    @SerializedName("phone")
    private String phone;

    @SerializedName("employeeId")
    private String employeeId;

    @SerializedName("role")
    private String role;

    @SerializedName("departmentName")
    private String departmentName;

    /**
     * departmentCode
     */
    @SerializedName("department")
    private String department;

    @SerializedName("positionName")
    private String positionName;
    /**
     * positionCode
     */
    @SerializedName("position")
    private String position;

    @SerializedName("jobName")
    private String jobName;
    /**
     * jobCode
     */
    @SerializedName("job")
    private String job;

    @SerializedName("permissionTemplate")
    private String permissionTemplate;

    @SerializedName("remark")
    private String remark;

    @SerializedName("registerTime")
    private String registerTime;

    @SerializedName("avatarUrl")
    private String avatarUrl;

    @SerializedName("status")
    private String status;

    public int getId() {
        return id;
    }

    // Getters
    public String getUsername() {
        return username;
    }

    // Setters
    public void setUsername(String username) {
        this.username = username;
    }

    public String getGender() {
        return Gender.getNameByCode(gender);
    }

    public String getGenderCode() {
        return gender;
    }

    public String getBirthday() {
        return birthday;
    }

    public String getIdCard() {
        return idCard;
    }

    public String getPhone() {
        return phone;
    }

    public String getEmployeeId() {
        return employeeId;
    }

    public String getRole() {
        return RoleType.getNameByCode(role);
    }

    public String getRoleCode() {
        return role;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String department) {
        this.departmentName = departmentName;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    /**
     * 获取格式化的部门显示文本：部门名称（部门编码）
     */
    public String getDepartmentDisplay() {
        // 如果有明确的部门编码，使用它
        if (departmentName != null && department != null && !department.isEmpty()) {
            return DisplayUtils.formatDepartmentDisplay(departmentName, department);
        } else {
            return departmentName;
        }
    }

    public String getPositionName() {
        return positionName;
    }

    public String getName() {
        return name;
    }

    public String getPermissionTemplate() {
        return permissionTemplate;
    }

    public String getRegisterTime() {
        return DateUtils.convertISOTime(registerTime);
    }

    public String getRemark() {
        return remark;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setId(int id) {
        this.id = id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    public String getJob() {
        return job;
    }

    public String getPosition() {
        return position;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public void setEmployeeId(String employeeId) {
        this.employeeId = employeeId;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    /**
     * 获取格式化的职位显示文本：职位名称（职位编码）
     */
    public String getPositionDisplay() {
        // 如果有明确的职位编码，使用它
        if (positionName != null && position != null && !position.isEmpty()) {
            return DisplayUtils.formatPositionDisplay(positionName, position);
        } else {
            return positionName;
        }
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    /**
     * 获取格式化的岗位显示文本：岗位名称（岗位编码）
     */
    public String getJobDisplay() {
        // 如果有明确的部门编码，使用它
        if (jobName != null && job != null && !job.isEmpty()) {
            return DisplayUtils.formatDepartmentDisplay(jobName, job);
        } else {
            return jobName;
        }
    }

    public void setJob(String job) {
        this.job = job;
    }

    public void setPermissionTemplate(String permissionTemplate) {
        this.permissionTemplate = permissionTemplate;
    }

    public void setRegisterTime(String registerTime) {
        this.registerTime = registerTime;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}