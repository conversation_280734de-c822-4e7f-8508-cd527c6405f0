<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- 用户头像 -->
        <ImageView
            android:id="@+id/ivAvatar"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:contentDescription="用户头像"
            android:scaleType="centerCrop"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/ic_person" />

        <!-- 用户名 -->
        <TextView
            android:id="@+id/tvUsername"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="8dp"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            android:ellipsize="end"
            android:maxLines="1"
            app:layout_constraintStart_toEndOf="@id/ivAvatar"
            app:layout_constraintEnd_toStartOf="@id/tvName"
            app:layout_constraintTop_toTopOf="@id/ivAvatar"
            tools:text="username123" />

        <!-- 角色 -->
        <TextView
            android:id="@+id/tvRole"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            android:ellipsize="end"
            android:maxLines="1"
            app:layout_constraintStart_toEndOf="@id/ivAvatar"
            app:layout_constraintEnd_toStartOf="@id/tvName"
            app:layout_constraintTop_toBottomOf="@id/tvUsername"
            tools:text="管理员" />

        <!-- 姓名跨越两行，靠右对齐 -->
        <TextView
            android:id="@+id/tvName"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_marginEnd="16dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="2"
            android:minWidth="80dp"
            android:textColor="@color/primary"
            android:textSize="20sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@id/tvRole"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tvUsername"
            tools:text="张三" />

        <!-- 分隔线 -->
        <View
            android:id="@+id/divider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="16dp"
            android:background="@color/divider"
            app:layout_constraintTop_toBottomOf="@id/tvRole" />

        <!-- 详细信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="vertical"
            app:layout_constraintTop_toBottomOf="@id/divider">

            <!-- 第一行：工号和电话 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="工号："
                    android:textColor="@color/text_hint"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tvEmployeeId"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    tools:text="EMP001" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:text="电话："
                    android:textColor="@color/text_hint"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tvPhone"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    tools:text="13800138000" />
            </LinearLayout>

            <!-- 第二行：部门和职位 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="部门："
                    android:textColor="@color/text_hint"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tvDepartment"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    tools:text="技术部（TECH001）" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:text="职位："
                    android:textColor="@color/text_hint"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tvPosition"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    tools:text="工程师（ENG001）" />
            </LinearLayout>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView>
