package com.opms.data.repository;

import com.opms.data.local.entity.Position;
import com.opms.data.model.request.PositionRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.PositionResponse;

import java.util.List;

import retrofit2.Call;

public interface PositionRepository {
    Call<ApiResponse<List<PositionResponse>>> getPositions();
    Call<ApiResponse<PositionResponse>> createPosition(PositionRequest request);
    Call<ApiResponse<PositionResponse>> updatePosition(int id, PositionRequest request);

    Call<ApiResponse<Void>> deletePosition(PositionRequest request);
    
    // Local database operations
    long insert(Position position);
    void update(Position position);
    void delete(Position position);
    Position findByCode(String code);
    Position findById(long id);
    List<Position> getAllPositions();
} 