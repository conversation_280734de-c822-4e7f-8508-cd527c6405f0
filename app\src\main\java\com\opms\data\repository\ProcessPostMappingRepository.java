package com.opms.data.repository;

import com.opms.data.model.request.ProcessPostMappingRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.ProcessPostMappingListResponse;
import com.opms.data.model.response.ProcessPostMappingResponse;

import retrofit2.Call;

/**
 * 流程岗位映射仓库接口
 */
public interface ProcessPostMappingRepository {
    /**
     * 获取流程岗位映射列表（分页）
     */
    Call<ApiResponse<ProcessPostMappingListResponse>> getProcessPostMappings(int page, int size, String keyword);

    /**
     * 创建流程岗位映射
     */
    Call<ApiResponse<ProcessPostMappingResponse>> createProcessPostMapping(ProcessPostMappingRequest request);

    /**
     * 更新流程岗位映射
     */
    Call<ApiResponse<ProcessPostMappingResponse>> updateProcessPostMapping(int id, ProcessPostMappingRequest request);

    /**
     * 删除流程岗位映射
     */
    Call<ApiResponse<Void>> deleteProcessPostMapping(ProcessPostMappingRequest request);
}
