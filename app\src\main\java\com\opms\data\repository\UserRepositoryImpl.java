package com.opms.data.repository;

import com.opms.data.local.entity.User;
import com.opms.data.model.request.ChangePasswordRequest;
import com.opms.data.model.request.LoginRequest;
import com.opms.data.model.request.RegisterRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.AvatarResponse;
import com.opms.data.model.response.LoginResponse;
import com.opms.data.model.response.RegisterResponse;
import com.opms.data.model.response.UserResponse;
import com.opms.data.remote.ApiService;

import javax.inject.Inject;
import javax.inject.Singleton;

import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import retrofit2.Call;
import retrofit2.Response;

@Singleton
public class UserRepositoryImpl implements UserRepository {
    private final ApiService apiService;

    @Inject
    public UserRepositoryImpl(ApiService apiService) {
        this.apiService = apiService;
    }

    @Override
    public Call<ApiResponse<LoginResponse>> login(LoginRequest request) {
        return apiService.login(request);
    }

    @Override
    public Call<ApiResponse<RegisterResponse>> register(RegisterRequest request) {
        return apiService.register(request);
    }

    @Override
    public Call<ApiResponse<RegisterResponse>> registerWithAvatar(
            RegisterRequest registerRequest,
            RequestBody imgRequest,
            MultipartBody.Part avatar) {
        return apiService.registerWithAvatar(registerRequest, imgRequest, avatar);
    }

    @Override
    public Call<ApiResponse<UserResponse>> getUserProfile() {
        return apiService.getUserProfile();
    }

    @Override
    public Call<ApiResponse<AvatarResponse>> getUserAvatar() {
        return apiService.getUserAvatar();
    }

    @Override
    public Call<ApiResponse<UserResponse>> updateUserProfile(UserResponse request) {
        return apiService.updateUserProfile(request);
    }

    @Override
    public Call<ApiResponse<Void>> changePassword(ChangePasswordRequest request) {
        return apiService.changePassword(request);
    }

    @Override
    public Call<ApiResponse<UserResponse>> checkUsernameExists(String username) {
        return apiService.checkUsernameExists(username);
    }

    @Override
    public long insert(User user) {
        try {
            Response<ApiResponse<UserResponse>> response = apiService.findByUsername(user.getUsername()).execute();
            if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                ApiResponse<UserResponse> apiResponse = response.body();
                if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                    UserResponse userResponse = apiResponse.getData();
                    if (userResponse != null) {
                        return userResponse.getId();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return -1;
    }

    @Override
    public void update(User user) {
        try {
            apiService.updateUserProfile(convertToUserResponse(user)).execute();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void delete(User user) {
        // 服务器端不提供删除用户的功能
    }

    @Override
    public UserResponse findByUsername(String username) {
        try {
            Response<ApiResponse<UserResponse>> response = apiService.findByUsername(username).execute();
            if (response.isSuccessful() && response.body() != null) {
                ApiResponse<UserResponse> apiResponse = response.body();
                if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                    return apiResponse.getData();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private UserResponse convertToUserResponse(User user) {
        UserResponse response = new UserResponse();
        response.setUsername(user.getUsername());
        response.setName(user.getName());
        response.setGender(user.getGender());
        response.setBirthday(user.getBirthDay());
        response.setIdCard(user.getIdCard());
        response.setPhone(user.getPhone());
        response.setEmployeeId(user.getEmployeeId());
        response.setRole(user.getRoleType());
        response.setDepartment(user.getDepartment());
        response.setPosition(user.getPosition());
        response.setJob(user.getPost());
        response.setPermissionTemplate(user.getPermissionTemplate());
        response.setRemark(user.getRemark());
        response.setRegisterTime(user.getRegisterTime());
        response.setAvatarUrl(user.getAvatar());
        return response;
    }
}