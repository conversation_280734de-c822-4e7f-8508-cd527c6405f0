package com.opms.ui.system;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.google.android.material.snackbar.Snackbar;
import com.opms.R;
import com.opms.data.model.request.ProcessTemplateRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.ProcessTemplateResponse;
import com.opms.data.repository.ProcessTemplateRepository;
import com.opms.databinding.ActivityProcessTemplateManagementBinding;
import com.opms.ui.system.adapter.ProcessTemplateAdapter;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class ProcessTemplateManagementActivity extends AppCompatActivity
        implements SwipeRefreshLayout.OnRefreshListener, ProcessTemplateAdapter.OnProcessTemplateClickListener {

    private static final String TAG = "ProcessTemplateManagement";

    @Inject
    ProcessTemplateRepository processTemplateRepository;

    private ActivityProcessTemplateManagementBinding binding;
    private ProcessTemplateAdapter adapter;
    private List<ProcessTemplateResponse> allProcessTemplates;
    private List<ProcessTemplateResponse> filteredProcessTemplates;
    private boolean isToolbarExpanded = false;

    private ActivityResultLauncher<Intent> editActivityLauncher;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityProcessTemplateManagementBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setupActivityResultLauncher();
        setupToolbar();
        setupRecyclerView();
        setupButtons();
        loadProcessTemplates();
    }

    private void setupActivityResultLauncher() {
        editActivityLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (result.getResultCode() == RESULT_OK) {
                        // 刷新列表
                        loadProcessTemplates();
                    }
                }
        );
    }

    private void startAddActivity() {
        Intent intent = new Intent(this, ProcessTemplateEditActivity.class);
        editActivityLauncher.launch(intent);
        // 添加滑动动画效果
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }

    private void setupToolbar() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("流程管理");
        }
    }

    private void setupRecyclerView() {
        adapter = new ProcessTemplateAdapter(this);
        adapter.setOnProcessTemplateClickListener(this);

        binding.rvProcessTemplates.setLayoutManager(new LinearLayoutManager(this));
        binding.rvProcessTemplates.setAdapter(adapter);

        binding.swipeRefresh.setOnRefreshListener(this);
        binding.swipeRefresh.setColorSchemeResources(R.color.primary);
    }

    private void setupButtons() {
        // 搜索按钮
        binding.btnToggleSearch.setOnClickListener(v -> toggleSearchToolbar());

        // 展开工具栏按钮
        binding.btnToggleToolbar.setOnClickListener(v -> toggleToolbar());

        // 搜索框文本变化监听
        binding.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                filterProcessTemplates(s.toString());
            }

            @Override
            public void afterTextChanged(Editable s) {
            }
        });

        // 新增按钮
        binding.fabAdd.setOnClickListener(v -> startAddActivity());
    }

    private void toggleSearchToolbar() {
        if (binding.cardExpandableTools.getVisibility() == View.VISIBLE) {
            binding.cardExpandableTools.setVisibility(View.GONE);
            binding.btnToggleSearch.setText("搜索");
        } else {
            binding.cardExpandableTools.setVisibility(View.VISIBLE);
            binding.btnToggleSearch.setText("收起");
            binding.etSearch.requestFocus();
        }
    }

    private void toggleToolbar() {
        isToolbarExpanded = !isToolbarExpanded;
        if (isToolbarExpanded) {
            binding.btnToggleToolbar.setIconResource(R.drawable.ic_expand_less);
            binding.cardExpandableTools.setVisibility(View.VISIBLE);
        } else {
            binding.btnToggleToolbar.setIconResource(R.drawable.ic_expand_more);
            binding.cardExpandableTools.setVisibility(View.GONE);
        }
    }

    private void filterProcessTemplates(String query) {
        if (allProcessTemplates == null) return;

        if (query.isEmpty()) {
            filteredProcessTemplates = new ArrayList<>(allProcessTemplates);
        } else {
            filteredProcessTemplates = new ArrayList<>();
            String lowerQuery = query.toLowerCase();
            for (ProcessTemplateResponse processTemplate : allProcessTemplates) {
                if (processTemplate.getName().toLowerCase().contains(lowerQuery) ||
                        processTemplate.getCode().toLowerCase().contains(lowerQuery)) {
                    filteredProcessTemplates.add(processTemplate);
                }
            }
        }

        adapter.setProcessTemplates(filteredProcessTemplates);
        updateProcessTemplateCount();
    }

    private void loadProcessTemplates() {
        showLoading(true);
        processTemplateRepository.getProcessTemplates().enqueue(new Callback<ApiResponse<List<ProcessTemplateResponse>>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<List<ProcessTemplateResponse>>> call,
                                   @NonNull Response<ApiResponse<List<ProcessTemplateResponse>>> response) {
                showLoading(false);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<List<ProcessTemplateResponse>> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        allProcessTemplates = apiResponse.getData();
                        filteredProcessTemplates = new ArrayList<>(allProcessTemplates);
                        adapter.setProcessTemplates(filteredProcessTemplates);
                        updateProcessTemplateCount();
                        hideEmptyView();
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "获取流程列表失败");
                        showEmptyView();
                    }
                } else {
                    showError("获取流程列表失败");
                    showEmptyView();
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<List<ProcessTemplateResponse>>> call, @NonNull Throwable t) {
                showLoading(false);
                Log.e(TAG, "获取流程列表失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
                showEmptyView();
            }
        });
    }

    private void updateProcessTemplateCount() {
        int count = filteredProcessTemplates != null ? filteredProcessTemplates.size() : 0;
        binding.tvProcessCount.setText("共 " + count + " 个流程");
    }

    private void showLoading(boolean show) {
        binding.progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
        binding.swipeRefresh.setRefreshing(false);
    }

    private void showError(String message) {
        Snackbar.make(binding.getRoot(), message, Snackbar.LENGTH_LONG).show();
    }

    private void showEmptyView() {
        binding.llEmpty.setVisibility(View.VISIBLE);
        binding.rvProcessTemplates.setVisibility(View.GONE);
    }

    private void hideEmptyView() {
        binding.llEmpty.setVisibility(View.GONE);
        binding.rvProcessTemplates.setVisibility(View.VISIBLE);
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onRefresh() {
        loadProcessTemplates();
    }

    @Override
    public void onProcessTemplateEdit(ProcessTemplateResponse processTemplate, int position) {
        adapter.enterEditMode(position);
    }

    @Override
    public void onProcessTemplateSave(ProcessTemplateResponse processTemplate, int itemPosition, String newName, String newDescription, int newSequence, boolean newStatus) {
        // 验证名称是否重复
        if (allProcessTemplates != null) {
            for (ProcessTemplateResponse pt : allProcessTemplates) {
                if (pt.getId() != processTemplate.getId() && newName.equals(pt.getName())) {
                    Snackbar.make(binding.getRoot(), "流程名称已存在", Snackbar.LENGTH_SHORT).show();
                    return;
                }
            }
        }

        // 验证序号是否重复
        if (allProcessTemplates != null) {
            for (ProcessTemplateResponse pt : allProcessTemplates) {
                if (pt.getId() != processTemplate.getId() && pt.getSequence() == newSequence) {
                    Snackbar.make(binding.getRoot(), "流程序号已存在，请使用其他序号", Snackbar.LENGTH_SHORT).show();
                    return;
                }
            }
        }

        // 保存更改
        int id = processTemplate.getId();
        ProcessTemplateRequest request = new ProcessTemplateRequest();
        request.setId(id);
        request.setName(newName);
        request.setCode(processTemplate.getCode()); // 代码不可修改
        request.setDescription(newDescription);
        request.setSequence(newSequence);
        request.setStatus(newStatus ? "1" : "0");

        updateProcessTemplate(id, request, itemPosition);
    }

    @Override
    public void onProcessTemplateCancel(int itemPosition) {
        // 取消编辑模式
        adapter.cancelEditing();
    }

    @Override
    public void onProcessTemplateDelete(ProcessTemplateResponse processTemplate) {
        showDeleteConfirmDialog(processTemplate);
    }

    @Override
    public void onProcessTemplateEditInNewPage(ProcessTemplateResponse processTemplate) {
        Intent intent = new Intent(this, ProcessTemplateEditActivity.class);
        intent.putExtra("process_template_id", processTemplate.getId());
        intent.putExtra("process_template_name", processTemplate.getName());
        intent.putExtra("process_template_code", processTemplate.getCode());
        intent.putExtra("process_template_description", processTemplate.getDescription());
        intent.putExtra("process_template_sequence", processTemplate.getSequence());
        intent.putExtra("process_template_status", processTemplate.getStatus());
        editActivityLauncher.launch(intent);
        // 添加滑动动画效果
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }

    private void updateProcessTemplate(int id, ProcessTemplateRequest request, int itemPosition) {
        processTemplateRepository.updateProcessTemplate(id, request).enqueue(new Callback<ApiResponse<ProcessTemplateResponse>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<ProcessTemplateResponse>> call,
                                   @NonNull Response<ApiResponse<ProcessTemplateResponse>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<ProcessTemplateResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        // 更新本地数据
                        ProcessTemplateResponse updatedProcessTemplate = apiResponse.getData();
                        if (allProcessTemplates != null && itemPosition < allProcessTemplates.size()) {
                            allProcessTemplates.set(itemPosition, updatedProcessTemplate);
                            filterProcessTemplates(binding.etSearch.getText().toString());
                        }
                        adapter.cancelEditing();
                        Snackbar.make(binding.getRoot(), "流程更新成功", Snackbar.LENGTH_SHORT).show();
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "更新流程失败");
                    }
                } else {
                    showError("更新流程失败");
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<ProcessTemplateResponse>> call, @NonNull Throwable t) {
                Log.e(TAG, "更新流程失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    private void showDeleteConfirmDialog(ProcessTemplateResponse processTemplate) {
        new MaterialAlertDialogBuilder(this)
                .setTitle("删除确认")
                .setMessage("确定要删除流程 \"" + processTemplate.getName() + "\" 吗？")
                .setPositiveButton("删除", (dialog, which) -> deleteProcessTemplate(processTemplate))
                .setNegativeButton("取消", null)
                .show();
    }

    private void deleteProcessTemplate(ProcessTemplateResponse processTemplate) {
        ProcessTemplateRequest request = new ProcessTemplateRequest();
        request.setId(processTemplate.getId());
        request.setCode(processTemplate.getCode());

        processTemplateRepository.deleteProcessTemplate(request).enqueue(new Callback<ApiResponse<Void>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<Void>> call,
                                   @NonNull Response<ApiResponse<Void>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<Void> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        // 从本地数据中移除
                        if (allProcessTemplates != null) {
                            allProcessTemplates.removeIf(pt -> pt.getId() == processTemplate.getId());
                            filterProcessTemplates(binding.etSearch.getText().toString());
                        }
                        Snackbar.make(binding.getRoot(), "流程删除成功", Snackbar.LENGTH_SHORT).show();
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "删除流程失败");
                    }
                } else {
                    showError("删除流程失败");
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<Void>> call, @NonNull Throwable t) {
                Log.e(TAG, "删除流程失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    @Override
    public void finish() {
        super.finish();
        // 添加返回动画
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right);
    }
}
