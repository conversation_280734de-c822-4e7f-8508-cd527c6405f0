<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="4dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- 用户头像 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="60dp"
            android:layout_height="60dp"
            app:cardCornerRadius="30dp"
            app:cardElevation="0dp">

            <ImageView
                android:id="@+id/iv_avatar"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:src="@drawable/ic_person"
                app:tint="@color/text_secondary" />

        </com.google.android.material.card.MaterialCardView>

        <!-- 用户信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 用户名和姓名 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_username"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="user001" />

                <TextView
                    android:id="@+id/tv_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    tools:text="张三" />

            </LinearLayout>

            <!-- 性别和手机号 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_gender"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_gender_badge"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="2dp"
                    android:textColor="@color/primary"
                    android:textSize="12sp"
                    tools:text="男" />

                <TextView
                    android:id="@+id/tv_phone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    tools:text="138****8888" />

            </LinearLayout>

            <!-- 注册时间 -->
            <TextView
                android:id="@+id/tv_register_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                tools:text="注册时间：2024-12-01 10:30" />

        </LinearLayout>

        <!-- 状态和操作 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <!-- 状态标签 -->
            <TextView
                android:id="@+id/tv_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_status_pending"
                android:paddingHorizontal="12dp"
                android:paddingVertical="4dp"
                android:textColor="@color/warning"
                android:textSize="12sp"
                android:textStyle="bold"
                tools:text="待审核" />

            <!-- 操作按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_audit"
                style="@style/Widget.MaterialComponents.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:minWidth="0dp"
                android:text="审核"
                android:textSize="12sp" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
