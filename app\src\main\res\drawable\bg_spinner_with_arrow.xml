<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 背景 -->
    <item>
        <selector>
            <item android:state_pressed="true">
                <shape android:shape="rectangle">
                    <solid android:color="@color/surface" />
                    <stroke
                        android:width="2dp"
                        android:color="@color/primary" />
                    <corners android:radius="8dp" />
                </shape>
            </item>
            <item android:state_focused="true">
                <shape android:shape="rectangle">
                    <solid android:color="@color/surface" />
                    <stroke
                        android:width="2dp"
                        android:color="@color/primary" />
                    <corners android:radius="8dp" />
                </shape>
            </item>
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="@color/surface" />
                    <stroke
                        android:width="1dp"
                        android:color="@color/divider" />
                    <corners android:radius="8dp" />
                </shape>
            </item>
        </selector>
    </item>

    <!-- 下拉箭头 -->
    <item
        android:width="24dp"
        android:height="24dp"
        android:drawable="@drawable/ic_arrow_drop_down"
        android:gravity="end|center_vertical"
        android:right="16dp" />
</layer-list>
