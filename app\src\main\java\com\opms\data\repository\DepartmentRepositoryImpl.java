package com.opms.data.repository;

import com.opms.data.local.dao.DepartmentDao;
import com.opms.data.local.entity.Department;
import com.opms.data.model.request.DepartmentRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.DepartmentResponse;
import com.opms.data.remote.ApiService;

import java.util.List;

import javax.inject.Inject;

import retrofit2.Call;

public class DepartmentRepositoryImpl implements DepartmentRepository {
    private final DepartmentDao departmentDao;
    private final ApiService apiService;

    @Inject
    public DepartmentRepositoryImpl(DepartmentDao departmentDao, ApiService apiService) {
        this.departmentDao = departmentDao;
        this.apiService = apiService;
    }

    public Call<ApiResponse<List<DepartmentResponse>>> getDepartmentList() {
        return apiService.getDepartmentList();
    }

    public Call<ApiResponse<DepartmentResponse>> createDepartment(DepartmentRequest request) {
        return apiService.createDepartment(request);
    }
    public Call<ApiResponse<DepartmentResponse>> updateDepartment(int id, DepartmentRequest request) {
        return apiService.updateDepartment(id, request);
    }

    public Call<ApiResponse<Void>> deleteDepartment(DepartmentRequest request) {
        return apiService.deleteDepartment(request);
    }


    public void insert(Department department) {
        departmentDao.insert(department);
    }


    public void update(Department department) {
        departmentDao.update(department);
    }

    public void delete(Department department) {
        departmentDao.delete(department);
    }

    public Department findByCode(String code) {
        return departmentDao.findByCode(code);
    }

    public Department findById(int id) {
        return departmentDao.findById(id);
    }

    public List<Department> getAllDepartments() {
        return departmentDao.getAllDepartments();
    }

    public List<Department> getChildDepartments(String parentCode) {
        return departmentDao.getChildDepartments(parentCode);
    }
} 