package com.opms.ui.system;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.fragment.app.Fragment;

import com.google.android.material.snackbar.Snackbar;
import com.opms.R;

public class SystemManagementFragment extends Fragment {
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_system_management, container, false);
    }

    @Override
    public void onViewCreated(View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // 初始化各个管理模块的点击事件
        setupClickListeners(view);
    }

    private void setupClickListeners(View view) {
        // 用户管理
        LinearLayout llUserManagement = view.findViewById(R.id.ll_user_management);
        llUserManagement.setOnClickListener(v -> {
            try {
                Intent intent = new Intent(getActivity(), UserManagementActivity.class);
                startActivity(intent);
                // 暂时注释掉动画，看看是否能解决问题
                // if (getActivity() != null) {
                //     getActivity().overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
                // }
            } catch (Exception e) {
                e.printStackTrace();
                if (getView() != null) {
                    Snackbar.make(getView(), "启动用户管理页面失败: " + e.getMessage(), Snackbar.LENGTH_SHORT).show();
                }
            }
        });

        // 部门管理
        LinearLayout llDepartmentManagement = view.findViewById(R.id.ll_department_management);
        llDepartmentManagement.setOnClickListener(v -> {
            try {
                Intent intent = new Intent(getActivity(), DepartmentManagementActivity.class);
                startActivity(intent);
            } catch (Exception e) {
                e.printStackTrace();
                if (getView() != null) {
                    Snackbar.make(getView(), "启动部门管理页面失败: " + e.getMessage(), Snackbar.LENGTH_SHORT).show();
                }
            }
        });

        // 职位管理
        LinearLayout llPositionManagement = view.findViewById(R.id.ll_position_management);
        llPositionManagement.setOnClickListener(v -> {
            try {
                Intent intent = new Intent(getActivity(), PositionManagementActivity.class);
                startActivity(intent);
            } catch (Exception e) {
                e.printStackTrace();
                if (getView() != null) {
                    Snackbar.make(getView(), "启动职位管理页面失败: " + e.getMessage(), Snackbar.LENGTH_SHORT).show();
                }
            }
        });

        // 岗位管理
        LinearLayout llJobManagement = view.findViewById(R.id.ll_job_management);
        llJobManagement.setOnClickListener(v -> {
            try {
                Intent intent = new Intent(getActivity(), PostManagementActivity.class);
                startActivity(intent);
            } catch (Exception e) {
                e.printStackTrace();
                if (getView() != null) {
                    Snackbar.make(getView(), "启动岗位管理页面失败: " + e.getMessage(), Snackbar.LENGTH_SHORT).show();
                }
            }
        });

        // 流程管理
        LinearLayout llProcessManagement = view.findViewById(R.id.ll_process_management);
        llProcessManagement.setOnClickListener(v -> {
            try {
                Intent intent = new Intent(getActivity(), ProcessTemplateManagementActivity.class);
                startActivity(intent);
            } catch (Exception e) {
                e.printStackTrace();
                if (getView() != null) {
                    Snackbar.make(getView(), "启动流程管理页面失败: " + e.getMessage(), Snackbar.LENGTH_SHORT).show();
                }
            }
        });

        // 权限管理
        LinearLayout llPermissionManagement = view.findViewById(R.id.ll_permission_management);
        llPermissionManagement.setOnClickListener(v -> {
            try {
                Intent intent = new Intent(getActivity(), PermissionManagementActivity.class);
                startActivity(intent);
            } catch (Exception e) {
                e.printStackTrace();
                if (getView() != null) {
                    Snackbar.make(getView(), "启动权限管理页面失败: " + e.getMessage(), Snackbar.LENGTH_SHORT).show();
                }
            }
        });

        // 流程岗位映射
        LinearLayout llProcessPostMapping = view.findViewById(R.id.ll_process_post_mapping);
        llProcessPostMapping.setOnClickListener(v -> {
            try {
                Intent intent = new Intent(getActivity(), ProcessPostMappingManagementActivity.class);
                startActivity(intent);
            } catch (Exception e) {
                e.printStackTrace();
                if (getView() != null) {
                    Snackbar.make(getView(), "启动流程岗位映射页面失败: " + e.getMessage(), Snackbar.LENGTH_SHORT).show();
                }
            }
        });
    }
}