package com.opms.common.enums;

public enum RoleType {
    ADMIN("admin", "管理员"),
    USER("user", "普通用户");

    private final String code;
    private final String name;

    RoleType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static RoleType fromCode(String code) {
        for (RoleType type : RoleType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return USER; // 默认返回普通用户
    }

    public String getCode() {
        return code;
    }

    public static String getNameByCode(String code) {
        for (RoleType sde : RoleType.values()) {
            if (code.equalsIgnoreCase(sde.getCode())) {
                return sde.getName();
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }
} 