package com.opms.api;

import com.opms.data.local.entity.Registration;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.PUT;
import retrofit2.http.Path;
import retrofit2.http.Query;

public interface RegistrationApi {
    @POST("auth/register")
    Call<Registration> register(@Body Registration registration);

    @GET("registrations")
    Call<List<Registration>> getRegistrations();

    @GET("registrations/search")
    Call<List<Registration>> searchRegistrations(@Query("query") String query);

    @GET("registrations/{id}")
    Call<Registration> getRegistration(@Path("id") String registrationId);

    @PUT("registrations/{id}")
    Call<Registration> updateRegistration(@Path("id") String registrationId, @Body Registration registration);
} 