package com.opms.data.remote.interceptor;

import android.content.Context;

import com.opms.common.constants.ApiConstants;
import com.opms.data.local.PreferencesManager;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;

import javax.inject.Inject;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;

public class ErrorInterceptor implements Interceptor {
    private final PreferencesManager preferencesManager;

    @Inject
    public ErrorInterceptor(Context context) {
        this.preferencesManager = new PreferencesManager(context);
    }

    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        Response response = chain.proceed(request);

        if (!response.isSuccessful()) {
            ResponseBody responseBody = response.body();
            if (responseBody != null) {
                try {
                    String responseString = responseBody.string();
                    JSONObject jsonObject = new JSONObject(responseString);
                    
                    // 检查是否是token过期
                    if (response.code() == ApiConstants.UNAUTHORIZED) {
                        // 清除本地存储的用户信息
                        preferencesManager.clearUserData();
                        throw new IOException("Token expired");
                    }
                    
                    // 其他错误处理
                    if (jsonObject.has("message")) {
                        String errorMessage = jsonObject.getString("message");
                        throw new IOException(errorMessage);
                    }
                } catch (JSONException e) {
                    throw new IOException("Error parsing error response");
                }
            }
        }
        
        return response;
    }
} 