package com.opms.data.local.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.opms.data.local.entity.OrderProcess;

import java.util.List;

@Dao
public interface OrderProcessDao {
    @Insert
    long insert(OrderProcess orderProcess);

    @Insert
    List<Long> insertAll(List<OrderProcess> orderProcesses);

    @Update
    void update(OrderProcess orderProcess);

    @Delete
    void delete(OrderProcess orderProcess);

    @Query("SELECT * FROM order_processes WHERE id = :id")
    OrderProcess findById(long id);

    @Query("SELECT * FROM order_processes WHERE orderId = :orderId")
    List<OrderProcess> findByOrderId(long orderId);

    @Query("SELECT * FROM order_processes WHERE orderItemId = :orderItemId")
    List<OrderProcess> findByOrderItemId(long orderItemId);

    @Query("SELECT * FROM order_processes WHERE userId = :userId")
    List<OrderProcess> findByUserId(long userId);

    @Query("SELECT * FROM order_processes WHERE status = :status")
    List<OrderProcess> findByStatus(String status);

    @Query("SELECT * FROM order_processes WHERE userId = :userId AND status = :status")
    List<OrderProcess> findByUserIdAndStatus(long userId, String status);

    @Query("SELECT * FROM order_processes")
    List<OrderProcess> getAllOrderProcesses();

    @Query("DELETE FROM order_processes WHERE orderId = :orderId")
    void deleteByOrderId(long orderId);

    @Query("DELETE FROM order_processes WHERE orderItemId = :orderItemId")
    void deleteByOrderItemId(long orderItemId);
} 