package com.opms.ui.system;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.google.android.material.snackbar.Snackbar;
import com.opms.R;
import com.opms.data.model.request.ProcessPostMappingRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.ProcessPostMappingListResponse;
import com.opms.data.model.response.ProcessPostMappingResponse;
import com.opms.data.repository.ProcessPostMappingRepository;
import com.opms.databinding.ActivityProcessPostMappingManagementBinding;
import com.opms.ui.system.adapter.ProcessPostMappingAdapter;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class ProcessPostMappingManagementActivity extends AppCompatActivity
        implements SwipeRefreshLayout.OnRefreshListener, ProcessPostMappingAdapter.OnProcessPostMappingClickListener {

    private static final String TAG = "ProcessPostMappingManagement";

    @Inject
    ProcessPostMappingRepository processPostMappingRepository;

    private ActivityProcessPostMappingManagementBinding binding;
    private ProcessPostMappingAdapter adapter;
    private List<ProcessPostMappingResponse> allMappings;
    private List<ProcessPostMappingResponse> filteredMappings;
    private boolean isToolbarExpanded = false;

    // 分页相关变量
    private int currentPage = 1;
    private int pageSize = 10;
    private boolean isLoading = false;
    private boolean hasMoreData = true;
    private String currentKeyword = "";

    private ActivityResultLauncher<Intent> editActivityLauncher;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityProcessPostMappingManagementBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setupActivityResultLauncher();
        setupToolbar();
        setupRecyclerView();
        setupButtons();
        loadProcessPostMappings();
    }

    private void setupActivityResultLauncher() {
        editActivityLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (result.getResultCode() == RESULT_OK) {
                        // 刷新列表
                        refreshMappingList();
                    }
                }
        );
    }

    private void startAddActivity() {
        Intent intent = new Intent(this, ProcessPostMappingEditActivity.class);
        editActivityLauncher.launch(intent);
        // 添加滑动动画效果
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }

    private void setupToolbar() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("流程岗位映射");
        }
    }

    private void setupRecyclerView() {
        adapter = new ProcessPostMappingAdapter(this);
        adapter.setOnProcessPostMappingClickListener(this);

        binding.rvMappings.setLayoutManager(new LinearLayoutManager(this));
        binding.rvMappings.setAdapter(adapter);

        binding.swipeRefresh.setOnRefreshListener(this);
        binding.swipeRefresh.setColorSchemeResources(R.color.primary);
    }

    private void setupButtons() {
        // 浮动操作按钮
        binding.fabAdd.setOnClickListener(v -> startAddActivity());

        // 工具栏切换按钮
        binding.btnToggleToolbar.setOnClickListener(v -> toggleToolbar());

        // 快速搜索按钮
        binding.btnToggleSearch.setOnClickListener(v -> {
            if (!isToolbarExpanded) {
                toggleToolbar();
            }
            // 聚焦到搜索框
            binding.etSearch.requestFocus();
        });

        // 搜索功能
        binding.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                filterMappings(s.toString());
            }

            @Override
            public void afterTextChanged(Editable s) {
            }
        });
    }

    private void toggleToolbar() {
        isToolbarExpanded = !isToolbarExpanded;
        binding.cardExpandableTools.setVisibility(isToolbarExpanded ? View.VISIBLE : View.GONE);
        binding.btnToggleToolbar.setImageResource(
                isToolbarExpanded ? R.drawable.ic_expand_less : R.drawable.ic_expand_more
        );
    }

    /**
     * 刷新映射列表 - 重置分页状态并重新加载数据
     */
    private void refreshMappingList() {
        // 重置分页状态
        currentPage = 1;
        hasMoreData = true;
        isLoading = false;

        // 清空现有数据
        if (allMappings != null) {
            allMappings.clear();
        }
        if (filteredMappings != null) {
            filteredMappings.clear();
        }

        // 重新加载数据
        loadProcessPostMappings();
    }

    private void loadProcessPostMappings() {
        if (isLoading || !hasMoreData) {
            return;
        }

        isLoading = true;
        showLoading(true);

        processPostMappingRepository.getProcessPostMappings(currentPage, pageSize, currentKeyword)
                .enqueue(new Callback<ApiResponse<ProcessPostMappingListResponse>>() {
                    @Override
                    public void onResponse(@NonNull Call<ApiResponse<ProcessPostMappingListResponse>> call,
                                           @NonNull Response<ApiResponse<ProcessPostMappingListResponse>> response) {
                        isLoading = false;
                        showLoading(false);

                        if (response.isSuccessful() && response.body() != null) {
                            ApiResponse<ProcessPostMappingListResponse> apiResponse = response.body();
                            if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                                ProcessPostMappingListResponse listResponse = apiResponse.getData();
                                List<ProcessPostMappingResponse> newMappings = listResponse.getList();

                                if (newMappings != null) {
                                    if (currentPage == 1) {
                                        // 首次加载或刷新
                                        allMappings = new ArrayList<>(newMappings);
                                        filteredMappings = new ArrayList<>(allMappings);
                                    } else {
                                        // 分页加载更多
                                        allMappings.addAll(newMappings);
                                        filteredMappings.addAll(newMappings);
                                    }

                                    adapter.setMappings(filteredMappings);
                                    updateMappingCount();
                                    hideEmptyView();

                                    // 检查是否还有更多数据
                                    hasMoreData = (listResponse.getPage() * listResponse.getSize() < listResponse.getTotal());
                                    currentPage++;
                                } else {
                                    if (currentPage == 1) {
                                        showEmptyView();
                                    }
                                }
                            } else {
                                showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "获取流程岗位映射列表失败");
                                if (currentPage == 1) {
                                    showEmptyView();
                                }
                            }
                        } else {
                            showError("获取流程岗位映射列表失败");
                            if (currentPage == 1) {
                                showEmptyView();
                            }
                        }
                    }

                    @Override
                    public void onFailure(@NonNull Call<ApiResponse<ProcessPostMappingListResponse>> call, @NonNull Throwable t) {
                        isLoading = false;
                        showLoading(false);
                        Log.e(TAG, "获取流程岗位映射列表失败: " + t.getMessage(), t);
                        showError("网络错误: " + t.getMessage());
                        if (currentPage == 1) {
                            showEmptyView();
                        }
                    }
                });
    }

    private void filterMappings(String keyword) {
        currentKeyword = keyword.trim();
        refreshMappingList();
    }

    private void updateMappingCount() {
        int count = filteredMappings != null ? filteredMappings.size() : 0;
        binding.tvMappingCount.setText(String.format("共 %d 条映射", count));
    }

    private void showLoading(boolean show) {
        binding.progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
        if (binding.swipeRefresh.isRefreshing() && !show) {
            binding.swipeRefresh.setRefreshing(false);
        }
    }

    private void showError(String message) {
        Snackbar.make(binding.getRoot(), message, Snackbar.LENGTH_SHORT).show();
    }

    private void showEmptyView() {
        binding.layoutEmpty.setVisibility(View.VISIBLE);
        binding.rvMappings.setVisibility(View.GONE);
    }

    private void hideEmptyView() {
        binding.layoutEmpty.setVisibility(View.GONE);
        binding.rvMappings.setVisibility(View.VISIBLE);
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onRefresh() {
        refreshMappingList();
    }

    @Override
    public void onProcessPostMappingClick(ProcessPostMappingResponse mapping) {
        Intent intent = new Intent(this, ProcessPostMappingEditActivity.class);
        intent.putExtra("mapping_id", mapping.getId());
        intent.putExtra("process_id", mapping.getProcessId());
        intent.putExtra("process_name", mapping.getProcessName());
        intent.putExtra("post_id", mapping.getPostId());
        intent.putExtra("post_name", mapping.getPostName());
        intent.putExtra("status", mapping.getStatus());
        intent.putExtra("remark", mapping.getRemark());
        editActivityLauncher.launch(intent);
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }

    @Override
    public void onProcessPostMappingDelete(ProcessPostMappingResponse mapping) {
        showDeleteConfirmDialog(mapping);
    }

    private void showDeleteConfirmDialog(ProcessPostMappingResponse mapping) {
        new MaterialAlertDialogBuilder(this)
                .setTitle("删除确认")
                .setMessage("确定要删除流程「" + mapping.getProcessName() + "」与岗位「" + mapping.getPostName() + "」的映射吗？")
                .setPositiveButton("删除", (dialog, which) -> deleteMapping(mapping))
                .setNegativeButton("取消", null)
                .show();
    }

    private void deleteMapping(ProcessPostMappingResponse mapping) {
        ProcessPostMappingRequest request = new ProcessPostMappingRequest();
        request.setId(mapping.getId());

        processPostMappingRepository.deleteProcessPostMapping(request)
                .enqueue(new Callback<ApiResponse<Void>>() {
                    @Override
                    public void onResponse(@NonNull Call<ApiResponse<Void>> call,
                                           @NonNull Response<ApiResponse<Void>> response) {
                        if (response.isSuccessful() && response.body() != null) {
                            ApiResponse<Void> apiResponse = response.body();
                            if (apiResponse.isSuccess()) {
                                Snackbar.make(binding.getRoot(), "删除成功", Snackbar.LENGTH_SHORT).show();
                                refreshMappingList(); // 刷新列表
                            } else {
                                showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "删除失败");
                            }
                        } else {
                            showError("删除失败");
                        }
                    }

                    @Override
                    public void onFailure(@NonNull Call<ApiResponse<Void>> call, @NonNull Throwable t) {
                        Log.e(TAG, "删除流程岗位映射失败: " + t.getMessage(), t);
                        showError("网络错误: " + t.getMessage());
                    }
                });
    }
}
