package com.opms.data.local;

import android.content.Context;
import android.content.SharedPreferences;

import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class PreferencesManager {
    private static final String PREF_NAME = "opms_prefs";
    private static final String KEY_TOKEN = "token";
    private static final String KEY_USER_NAME = "user_name";
    private static final String KEY_USER_ROLE = "user_role";

    private final SharedPreferences preferences;

    @Inject
    public PreferencesManager(Context context) {
        preferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
    }

    public void saveToken(String token) {
        preferences.edit().putString(KEY_TOKEN, token).apply();
    }

    public String getToken() {
        return preferences.getString(KEY_TOKEN, "");
    }

    public void saveUsername(String username) {
        preferences.edit().putString(KEY_USER_NAME, username).apply();
    }

    public String getUsername() {
        return preferences.getString(KEY_USER_NAME, "");
    }

    public void saveUserRole(String role) {
        preferences.edit().putString(KEY_USER_ROLE, role).apply();
    }

    public String getUserRole() {
        return preferences.getString(KEY_USER_ROLE, "");
    }

    public boolean isLoggedIn() {
        return !getToken().isEmpty() && !getUsername().isEmpty();
    }

    public void clearUserData() {
        preferences.edit()
                .remove(KEY_TOKEN)
                .remove(KEY_USER_NAME)
                .remove(KEY_USER_ROLE)
                .apply();
    }
} 