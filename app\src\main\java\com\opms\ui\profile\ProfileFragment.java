package com.opms.ui.profile;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.bumptech.glide.Glide;
import com.google.android.material.snackbar.Snackbar;
import com.opms.R;
import com.opms.common.enums.RoleType;
import com.opms.common.utils.AvatarCacheUtils;
import com.opms.common.utils.ImageUtils;
import com.opms.data.local.PreferencesManager;
import com.opms.data.model.User;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.DepartmentResponse;
import com.opms.data.model.response.PermissionResponse;
import com.opms.data.model.response.PositionResponse;
import com.opms.data.model.response.PostResponse;
import com.opms.data.model.response.UserResponse;
import com.opms.data.remote.ApiService;
import com.opms.data.repository.UserRepository;
import com.opms.databinding.FragmentProfileCompactBinding;
import com.opms.ui.login.LoginActivity;

import java.util.List;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class ProfileFragment extends Fragment {
    private FragmentProfileCompactBinding binding;
    private ImageView ivAvatar; // 移到这里，解决前向引用问题

    // 定义头像编辑结果处理器
    private final ActivityResultLauncher<Intent> avatarEditLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            result -> {
                if (result.getResultCode() == android.app.Activity.RESULT_OK) {
                    // 刷新头像显示
                    if (binding != null && binding.ivAvatar != null && getContext() != null) {
                        ImageUtils.loadUserAvatarFromCache(this, binding.ivAvatar);
                    } else if (ivAvatar != null && getContext() != null) {
                        ImageUtils.loadUserAvatarFromCache(this, ivAvatar);
                    }
                }
            });

    private final ActivityResultLauncher<Intent> imagePickerLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            result -> {
                if (result.getResultCode() == android.app.Activity.RESULT_OK && result.getData() != null) {
                    Uri selectedImage = result.getData().getData();
                    if (selectedImage != null) {
                        // TODO: Upload image to server and update avatar URL
                        if (binding != null && binding.ivAvatar != null) {
                            ImageUtils.loadUserAvatarCircle(this, selectedImage, binding.ivAvatar);
                        } else if (ivAvatar != null) {
                            ImageUtils.loadUserAvatarCircle(this, selectedImage, ivAvatar);
                        } else {
                            Log.e("ProfileFragment", "Avatar ImageView is null in imagePickerLauncher");
                        }
                    }
                }
            });

    // 个人信息编辑结果处理
    private final ActivityResultLauncher<Intent> profileEditLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            result -> {
                if (result.getResultCode() == android.app.Activity.RESULT_OK) {
                    // 编辑成功，重新加载用户信息
                    loadUserProfile();
                    if (getView() != null) {
                        Snackbar.make(getView(), "个人信息已更新", Snackbar.LENGTH_SHORT).show();
                    }
                }
            });
    @Inject
    PreferencesManager preferencesManager;
    @Inject
    ApiService apiService;
    @Inject
    UserRepository userRepository;
    private User currentUser;
    private boolean isEditing = false;

    // 下拉选项数据
    private List<DepartmentResponse> departments;
    private List<PositionResponse> positions;
    private List<PostResponse> posts;
    private List<PermissionResponse> permissions;

    // 数据加载状态管理
    private boolean isDepartmentsLoaded = false;
    private boolean isPositionsLoaded = false;
    private boolean isPostsLoaded = false;
    private boolean isPermissionsLoaded = false;
    private boolean isUserDataLoaded = false;
    private UserResponse pendingUserData = null;
    private TextView tvUsername;
    private TextView tvName;
    private TextView tvGender;
    private TextView tvBirthday;
    private TextView tvIdCard;
    private TextView tvPhone;
    private TextView tvEmployeeId;
    private TextView tvRole;
    private TextView tvDepartment;
    private TextView tvPosition;
    private TextView tvJob;
    private TextView tvPermissionTemplate;
    private TextView tvRemark;
    private TextView tvRegisterTime;
    private Button btnEdit;
    private Button btnLogout;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 在新版API中，不再需要setHasOptionsMenu(true)
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        // 使用更紧凑的布局文件
        binding = FragmentProfileCompactBinding.inflate(inflater, container, false);
        View view = binding.getRoot();
        initViews(view);
        setupListeners();
        loadDropdownData();
        loadUserProfile();
        return view;
    }

    private void initViews(View view) {
        // 使用新布局中的视图ID
        ivAvatar = view.findViewById(R.id.ivAvatar);
        btnEdit = view.findViewById(R.id.btnEdit);
        btnLogout = view.findViewById(R.id.btnLogout);

        // 其他视图通过binding对象访问
        // 这些字段在新布局中可能不存在，所以我们不再使用它们
        tvUsername = null;
        tvName = null;
        tvGender = null;
        tvBirthday = null;
        tvIdCard = null;
        tvPhone = null;
        tvEmployeeId = null;
        tvRole = null;
        tvDepartment = null;
        tvPosition = null;
        tvJob = null;
        tvPermissionTemplate = null;
        tvRemark = null;
        tvRegisterTime = null;
    }

    private void setupListeners() {
        if (binding == null) {
            Log.e("ProfileFragment", "Binding is null in setupListeners");
            return;
        }

        // 设置头像点击事件
        if (binding.ivAvatar != null) {
            binding.ivAvatar.setOnClickListener(v -> {
                if (getContext() != null) {
                    // 打开头像查看/编辑页面，允许编辑
                    Intent intent = AvatarViewActivity.createIntent(getContext(), true);
                    avatarEditLauncher.launch(intent);

                    // 添加过渡动画
                    if (getActivity() != null) {
                        getActivity().overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
                    }
                }
            });
        }

        if (binding.btnEdit != null) {
            binding.btnEdit.setOnClickListener(v -> {
                // 跳转到编辑个人信息页面
                if (getContext() != null) {
                    Intent intent = ProfileEditActivity.createIntent(getContext());
                    profileEditLauncher.launch(intent);

                    // 添加过渡动画
                    if (getActivity() != null) {
                        getActivity().overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
                    }
                }
            });
        }

        if (binding.btnChangePassword != null) {
            binding.btnChangePassword.setOnClickListener(v -> {
                // 跳转到修改密码页面
                if (getContext() != null) {
                    Intent intent = PasswordChangeActivity.createIntent(getContext());
                    startActivity(intent);
                    if (getActivity() != null) {
                        getActivity().overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
                    }
                }
            });
        }

        if (binding.btnLogout != null) {
            binding.btnLogout.setOnClickListener(v -> {
                // 清除本地存储的用户信息
                preferencesManager.clearUserData();

                // 清除缓存的头像
                if (getContext() != null) {
                    AvatarCacheUtils.clearCachedAvatar(getContext());
                }

                // 跳转到登录页面
                Intent intent = new Intent(getActivity(), LoginActivity.class);
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                startActivity(intent);
                if (getActivity() != null) {
                    getActivity().finish();
                }
            });
        }
    }

    private void loadUserProfile() {
        if (getContext() == null) {
            Log.e("ProfileFragment", "Context is null in loadUserProfile");
            return;
        }

        if (!preferencesManager.isLoggedIn()) {
            if (getView() != null) {
                Snackbar.make(getView(), "请先登录", Snackbar.LENGTH_SHORT).show();
            }
            return;
        }

        userRepository.getUserProfile().enqueue(new Callback<ApiResponse<UserResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<UserResponse>> call, Response<ApiResponse<UserResponse>> response) {
                if (getContext() == null || !isAdded()) {
                    Log.e("ProfileFragment", "Fragment not attached in onResponse");
                    return;
                }

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<UserResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        isUserDataLoaded = true;
                        pendingUserData = apiResponse.getData();
                        checkAndShowUserInfo();
                    } else {
                        if (getView() != null) {
                            Snackbar.make(getView(), apiResponse.getMessage(), Snackbar.LENGTH_SHORT).show();
                        }
                    }
                } else {
                    if (getView() != null) {
                        Snackbar.make(getView(), "获取用户信息失败", Snackbar.LENGTH_SHORT).show();
                    }
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<UserResponse>> call, Throwable t) {
                if (getContext() == null || !isAdded()) {
                    Log.e("ProfileFragment", "Fragment not attached in onFailure");
                    return;
                }

                Log.e("ProfileFragment", "Network error: " + t.getMessage(), t);
                if (getView() != null) {
                    Snackbar.make(getView(), "网络错误: " + t.getMessage(), Snackbar.LENGTH_SHORT).show();
                }
            }
        });
    }

    /**
     * 处理头像加载逻辑
     *
     * @param user      用户信息
     * @param imageView 目标ImageView
     */
    private void handleAvatarLoading(UserResponse user, ImageView imageView) {
        if (imageView == null) {
            Log.e("ProfileFragment", "ImageView is null in handleAvatarLoading");
            return;
        }

        Context context = getContext();
        if (context == null) {
            Log.e("ProfileFragment", "Context is null in handleAvatarLoading");
            return;
        }

        Log.d("ProfileFragment", "Starting to handle avatar loading");

        // 首先尝试从本地缓存加载
        if (AvatarCacheUtils.hasCachedAvatar(context)) {
            Log.d("ProfileFragment", "Loading avatar from cache");
            ImageUtils.loadUserAvatarFromCache(this, imageView);
            return;
        }

        // 如果没有缓存，检查avatarUrl是否为Base64编码的图像
        String avatarUrl = user.getAvatarUrl();
        Log.d("ProfileFragment", "No cached avatar found, checking avatarUrl: " +
                (avatarUrl != null ? (avatarUrl.length() > 50 ? avatarUrl.substring(0, 50) + "..." : avatarUrl) : "null"));

        if (avatarUrl != null && !avatarUrl.isEmpty()) {
            // 直接使用URL加载头像
            Log.d("ProfileFragment", "Loading avatar from URL: " + avatarUrl);
            String processedImageUrl = com.opms.common.utils.ImageUtils.processImageUrl(avatarUrl);
            Glide.with(this)
                    .load(processedImageUrl)
                    .placeholder(R.drawable.ic_default_avatar)
                    .error(R.drawable.ic_default_avatar)
                    .circleCrop()
                    .into(imageView);
        } else {
            // avatarUrl为空，显示默认头像
            Log.d("ProfileFragment", "AvatarUrl is empty, showing default avatar");
            imageView.setImageResource(R.drawable.ic_default_avatar);
        }
    }

    /**
     * 判断字符串是否为Base64编码的图像
     *
     * @param str 要检查的字符串
     * @return 如果是Base64编码的图像则返回true，否则返回false
     */
    private boolean isBase64Image(String str) {
        // 检查是否以data:image开头（常见的Base64图像格式）
        if (str.startsWith("data:image")) {
            return true;
        }

        // 检查是否包含逗号（Base64数据通常有前缀和数据部分，用逗号分隔）
        if (str.contains(",")) {
            return true;
        }

        // 检查长度（Base64编码的图像通常很长）
        if (str.length() > 100 && !str.contains("http://") && !str.contains("https://")) {
            // 检查是否包含Base64字符集中的字符
            return str.matches("^[A-Za-z0-9+/=]+$");
        }

        return false;
    }

    private void updateUI(UserResponse user) {
        if (user == null) return;

        Log.d("ProfileFragment", "Updating UI for user: " + user.getUsername());
        Log.d("ProfileFragment", "User info - Role: " + user.getRole() +
                                ", Department: " + user.getDepartment() +
                                ", DepartmentName: " + user.getDepartmentName() +
                                ", Position: " + user.getPosition() +
                                ", PositionName: " + user.getPositionName() +
                                ", Job: " + user.getJob() +
                                ", JobName: " + user.getJobName() +
                                ", PermissionTemplate: " + user.getPermissionTemplate());

        // 加载头像
        if (binding != null && binding.ivAvatar != null) {
            handleAvatarLoading(user, binding.ivAvatar);
        } else if (ivAvatar != null) {
            handleAvatarLoading(user, ivAvatar);
        } else {
            Log.e("ProfileFragment", "Avatar ImageView is null");
        }

        // 更新用户信息
        if (binding != null) {
            // 设置顶部显示的用户名
            if (binding.tvDisplayUsername != null) {
                // 直接显示用户名，而非姓名
                String displayName = user.getUsername();
                binding.tvDisplayUsername.setText(displayName);
            }

            // 设置表单字段
            if (binding.etName != null) binding.etName.setText(user.getName());
            if (binding.actGender != null) binding.actGender.setText(user.getGender());
            if (binding.etBirthday != null) binding.etBirthday.setText(user.getBirthday());
            if (binding.etIdCard != null) binding.etIdCard.setText(user.getIdCard());
            if (binding.etPhone != null) binding.etPhone.setText(user.getPhone());
            if (binding.etEmployeeId != null) binding.etEmployeeId.setText(user.getEmployeeId());

            // 角色显示 - 使用格式化显示文本
            if (binding.actRole != null) {
                String roleDisplay = getRoleTypeDisplayText(user);
                binding.actRole.setText(roleDisplay);
                Log.d("ProfileFragment", "Set role: " + roleDisplay);
            }

            // 部门显示 - 优先使用名称，如果为空则使用code
            if (binding.actDepartment != null) {
                String departmentDisplay = getDepartmentDisplayText(user);
                binding.actDepartment.setText(departmentDisplay);
                Log.d("ProfileFragment", "Set department: " + departmentDisplay);
            }

            // 职务显示 - 优先使用名称，如果为空则使用code
            if (binding.actPosition != null) {
                String positionDisplay = getPositionDisplayText(user);
                binding.actPosition.setText(positionDisplay);
                Log.d("ProfileFragment", "Set position: " + positionDisplay);
            }

            // 岗位显示 - 优先使用名称，如果为空则使用code
            if (binding.actJob != null) {
                String jobDisplay = getJobDisplayText(user);
                binding.actJob.setText(jobDisplay);
                Log.d("ProfileFragment", "Set job: " + jobDisplay);
            }

            // 权限模板显示 - 直接显示，因为没有额外的格式化需求
            if (binding.actPermissionTemplate != null) {
                String permissionDisplay = getPermissionTemplateDisplayText(user);
                binding.actPermissionTemplate.setText(permissionDisplay);
                Log.d("ProfileFragment", "Set permission template: " + permissionDisplay);
            }

            if (binding.etRemark != null) binding.etRemark.setText(user.getRemark());
            if (binding.etRegisterTime != null)
                binding.etRegisterTime.setText(user.getRegisterTime());
        } else {
            Log.e("ProfileFragment", "Binding is null in updateUI");
        }
    }

    /**
     * 获取角色类型显示文本
     */
    private String getRoleTypeDisplayText(UserResponse user) {
        // 如果用户的角色字段为空，直接返回
        if (user.getRole() == null || user.getRole().isEmpty()) {
            return "未设置";
        }

        // 尝试从RoleType枚举中查找匹配的角色类型
        for (RoleType roleType : RoleType.values()) {
            // 先尝试按code匹配
            if (roleType.getCode().equals(user.getRole())) {
                return roleType.getName();
            }
            // 再尝试按name匹配（以防服务器返回的是name）
            if (roleType.getName().equals(user.getRole())) {
                return roleType.getName();
            }
        }

        // 如果没有找到匹配的角色类型，返回原始值
        Log.w("ProfileFragment", "No matching role type found for: " + user.getRole());
        return user.getRole();
    }

    /**
     * 获取部门显示文本
     */
    private String getDepartmentDisplayText(UserResponse user) {
        // 优先使用部门名称
        if (user.getDepartmentName() != null && !user.getDepartmentName().isEmpty()) {
            return user.getDepartmentName();
        }

        // 如果部门名称为空，尝试从已加载的部门列表中查找
        if (user.getDepartment() != null && !user.getDepartment().isEmpty() && departments != null) {
            for (DepartmentResponse dept : departments) {
                if (dept.getCode().equals(user.getDepartment())) {
                    Log.d("ProfileFragment", "Found department name: " + dept.getName() + " for code: " + user.getDepartment());
                    return dept.getName();
                }
            }
            Log.w("ProfileFragment", "No matching department found for code: " + user.getDepartment());
            // 如果没有找到匹配的部门，返回代码
            return user.getDepartment();
        }

        return "未设置";
    }

    /**
     * 获取职务显示文本
     */
    private String getPositionDisplayText(UserResponse user) {
        // 优先使用职务名称
        if (user.getPositionName() != null && !user.getPositionName().isEmpty()) {
            return user.getPositionName();
        }

        // 如果职务名称为空，尝试从已加载的职务列表中查找
        if (user.getPosition() != null && !user.getPosition().isEmpty() && positions != null) {
            for (PositionResponse position : positions) {
                if (position.getCode().equals(user.getPosition())) {
                    Log.d("ProfileFragment", "Found position name: " + position.getName() + " for code: " + user.getPosition());
                    return position.getName();
                }
            }
            Log.w("ProfileFragment", "No matching position found for code: " + user.getPosition());
            // 如果没有找到匹配的职务，返回代码
            return user.getPosition();
        }

        return "未设置";
    }

    /**
     * 获取岗位显示文本
     */
    private String getJobDisplayText(UserResponse user) {
        // 优先使用岗位名称
        if (user.getJobName() != null && !user.getJobName().isEmpty()) {
            return user.getJobName();
        }

        // 如果岗位名称为空，尝试从已加载的岗位列表中查找
        if (user.getJob() != null && !user.getJob().isEmpty() && posts != null) {
            for (PostResponse post : posts) {
                if (post.getCode().equals(user.getJob())) {
                    Log.d("ProfileFragment", "Found job name: " + post.getName() + " for code: " + user.getJob());
                    return post.getName();
                }
            }
            Log.w("ProfileFragment", "No matching job found for code: " + user.getJob());
            // 如果没有找到匹配的岗位，返回代码
            return user.getJob();
        }

        return "未设置";
    }

    /**
     * 获取权限模板显示文本
     */
    private String getPermissionTemplateDisplayText(UserResponse user) {
        // 如果用户的权限模板字段为空，直接返回
        if (user.getPermissionTemplate() == null || user.getPermissionTemplate().isEmpty()) {
            return "未设置";
        }

        // 尝试从已加载的权限列表中查找匹配的权限模板
        if (permissions != null && !permissions.isEmpty()) {
            for (PermissionResponse permission : permissions) {
                // 先尝试按code匹配
                if (permission.getCode().equals(user.getPermissionTemplate())) {
                    Log.d("ProfileFragment", "Found permission name: " + permission.getName() + " for code: " + user.getPermissionTemplate());
                    return permission.getName();
                }
                // 再尝试按name匹配（以防服务器返回的是name）
                if (permission.getName().equals(user.getPermissionTemplate())) {
                    Log.d("ProfileFragment", "Found permission by name: " + permission.getName());
                    return permission.getName();
                }
            }
            Log.w("ProfileFragment", "No matching permission found for: " + user.getPermissionTemplate());
        }

        // 如果没有找到匹配的权限模板，返回原始值
        return user.getPermissionTemplate();
    }

    /**
     * 检查所有必要的数据是否已加载，如果是则显示用户信息
     */
    private void checkAndShowUserInfo() {
        Log.d("ProfileFragment", "Checking data loading status - Departments: " + isDepartmentsLoaded +
                   ", Positions: " + isPositionsLoaded +
                   ", Posts: " + isPostsLoaded +
                   ", Permissions: " + isPermissionsLoaded +
                   ", UserData: " + isUserDataLoaded);

        // 检查是否所有必要的数据都已加载
        if (isDepartmentsLoaded && isPositionsLoaded && isPostsLoaded &&
            isPermissionsLoaded && isUserDataLoaded && pendingUserData != null) {

            Log.d("ProfileFragment", "All data loaded, showing user info");
            updateUI(pendingUserData);
            pendingUserData = null; // 清除待处理的用户数据
        } else {
            Log.d("ProfileFragment", "Still waiting for data to load");
        }
    }

    private void loadDropdownData() {
        loadDepartments();
        loadPositions();
        loadPosts();
        loadPermissions();
    }

    private void loadDepartments() {
        if (getContext() == null || !isAdded()) {
            Log.e("ProfileFragment", "Fragment not attached in loadDepartments");
            return;
        }

        apiService.getDepartments().enqueue(new Callback<ApiResponse<List<DepartmentResponse>>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<List<DepartmentResponse>>> call,
                                   @NonNull Response<ApiResponse<List<DepartmentResponse>>> response) {
                if (getContext() == null || !isAdded()) {
                    Log.e("ProfileFragment", "Fragment not attached in departments onResponse");
                    return;
                }

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<List<DepartmentResponse>> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        departments = apiResponse.getData();
                        isDepartmentsLoaded = true;
                        Log.d("ProfileFragment", "Departments loaded: " + departments.size() + " items");
                        checkAndShowUserInfo();
                    }
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<List<DepartmentResponse>>> call, @NonNull Throwable t) {
                if (getContext() == null || !isAdded()) {
                    return;
                }
                Log.e("ProfileFragment", "获取部门列表失败: " + t.getMessage(), t);
            }
        });
    }

    private void loadPositions() {
        if (getContext() == null || !isAdded()) {
            Log.e("ProfileFragment", "Fragment not attached in loadPositions");
            return;
        }

        apiService.getPositions().enqueue(new Callback<ApiResponse<List<PositionResponse>>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<List<PositionResponse>>> call,
                                   @NonNull Response<ApiResponse<List<PositionResponse>>> response) {
                if (getContext() == null || !isAdded()) {
                    Log.e("ProfileFragment", "Fragment not attached in positions onResponse");
                    return;
                }

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<List<PositionResponse>> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        positions = apiResponse.getData();
                        isPositionsLoaded = true;
                        Log.d("ProfileFragment", "Positions loaded: " + positions.size() + " items");
                        checkAndShowUserInfo();
                    }
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<List<PositionResponse>>> call, @NonNull Throwable t) {
                if (getContext() == null || !isAdded()) {
                    return;
                }
                Log.e("ProfileFragment", "获取职位列表失败: " + t.getMessage(), t);
            }
        });
    }

    private void loadPosts() {
        if (getContext() == null || !isAdded()) {
            Log.e("ProfileFragment", "Fragment not attached in loadPosts");
            return;
        }

        apiService.getPosts().enqueue(new Callback<ApiResponse<List<PostResponse>>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<List<PostResponse>>> call,
                                   @NonNull Response<ApiResponse<List<PostResponse>>> response) {
                if (getContext() == null || !isAdded()) {
                    Log.e("ProfileFragment", "Fragment not attached in posts onResponse");
                    return;
                }

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<List<PostResponse>> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        posts = apiResponse.getData();
                        isPostsLoaded = true;
                        Log.d("ProfileFragment", "Posts loaded: " + posts.size() + " items");
                        checkAndShowUserInfo();
                    }
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<List<PostResponse>>> call, @NonNull Throwable t) {
                if (getContext() == null || !isAdded()) {
                    return;
                }
                Log.e("ProfileFragment", "获取岗位列表失败: " + t.getMessage(), t);
            }
        });
    }

    private void loadPermissions() {
        if (getContext() == null || !isAdded()) {
            Log.e("ProfileFragment", "Fragment not attached in loadPermissions");
            return;
        }

        apiService.getPermissions().enqueue(new Callback<ApiResponse<List<PermissionResponse>>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<List<PermissionResponse>>> call,
                                   @NonNull Response<ApiResponse<List<PermissionResponse>>> response) {
                if (getContext() == null || !isAdded()) {
                    Log.e("ProfileFragment", "Fragment not attached in permissions onResponse");
                    return;
                }

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<List<PermissionResponse>> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        permissions = apiResponse.getData();
                        isPermissionsLoaded = true;
                        Log.d("ProfileFragment", "Permissions loaded: " + permissions.size() + " items");
                        for (PermissionResponse permission : permissions) {
                            Log.d("ProfileFragment", "Permission: " + permission.getName() + " (" + permission.getCode() + ")");
                        }
                        checkAndShowUserInfo();
                    }
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<List<PermissionResponse>>> call, @NonNull Throwable t) {
                if (getContext() == null || !isAdded()) {
                    return;
                }
                Log.e("ProfileFragment", "获取权限列表失败: " + t.getMessage(), t);
            }
        });
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        // 不再需要在ActionBar中添加修改密码按钮，因为我们已经在布局中添加了
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}