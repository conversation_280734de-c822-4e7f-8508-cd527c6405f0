# 多图片上传集成示例

## 📋 完整集成示例

以下是将多图片上传功能集成到产品编辑页面的完整示例：

### 1. Activity 集成示例

```java
public class ProductEditActivity extends AppCompatActivity {
    
    private static final int REQUEST_CODE_PICK_IMAGES = 1001;
    private static final String TAG = "ProductEditActivity";
    
    private ActivityProductEditBinding binding;
    private MultiImageManager imageManager;
    private int productId;
    private boolean isEditMode;
    
    @Inject
    ImageUploadRepository imageUploadRepository;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = DataBindingUtil.setContentView(this, R.layout.activity_product_edit);
        
        // 获取参数
        productId = getIntent().getIntExtra("productId", 0);
        isEditMode = productId > 0;
        
        setupImageManager();
        setupUI();
        
        if (isEditMode) {
            loadProductData();
        }
    }
    
    private void setupImageManager() {
        // 初始化多图片管理器
        imageManager = new MultiImageManager(this, binding.rvProductImages);
        
        // 设置配置
        imageManager.setup(
            imageUploadRepository,
            ImageUploadUtils.BusinessType.PRODUCT,
            String.valueOf(productId),
            getCurrentUser(),
            isEditMode
        );
        
        // 设置监听器
        imageManager.setOnImageActionListener(new MultiImageManager.OnImageActionListener() {
            @Override
            public void onAddImageClick() {
                checkPermissionAndOpenImagePicker();
            }
            
            @Override
            public void onImageClick(MultiImageManager.ImageItem item, int position) {
                showImagePreview(item);
            }
            
            @Override
            public void onImageLongClick(MultiImageManager.ImageItem item, int position) {
                showImageOptions(item, position);
            }
            
            @Override
            public void onImageDelete(MultiImageManager.ImageItem item, int position) {
                confirmDeleteImage(item, position);
            }
            
            @Override
            public void onImageUploadProgress(int uploadedCount, int totalCount) {
                updateUploadProgress(uploadedCount, totalCount);
            }
            
            @Override
            public void onImageUploadComplete(List<String> successUrls, List<String> failureMessages) {
                handleUploadComplete(successUrls, failureMessages);
            }
        });
    }
    
    private void setupUI() {
        // 设置标题
        setTitle(isEditMode ? "编辑产品" : "新增产品");
        
        // 设置保存按钮
        binding.btnSave.setOnClickListener(v -> saveProduct());
        
        // 设置返回按钮
        binding.btnBack.setOnClickListener(v -> finish());
    }
    
    private void loadProductData() {
        // 加载产品基本信息
        // ... 其他字段加载逻辑
        
        // 加载产品图片
        imageManager.loadImages();
    }
    
    private void checkPermissionAndOpenImagePicker() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE)
                != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this,
                    new String[]{Manifest.permission.READ_EXTERNAL_STORAGE},
                    REQUEST_CODE_PERMISSION);
        } else {
            openImagePicker();
        }
    }
    
    private void openImagePicker() {
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
        intent.setType("image/*");
        intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true);
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        
        try {
            startActivityForResult(Intent.createChooser(intent, "选择图片"), REQUEST_CODE_PICK_IMAGES);
        } catch (ActivityNotFoundException e) {
            Snackbar.make(binding.getRoot(), "没有找到图片选择器", Snackbar.LENGTH_SHORT).show();
        }
    }
    
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        if (requestCode == REQUEST_CODE_PICK_IMAGES && resultCode == RESULT_OK && data != null) {
            List<Uri> imageUris = new ArrayList<>();
            
            if (data.getClipData() != null) {
                // 多选
                ClipData clipData = data.getClipData();
                for (int i = 0; i < clipData.getItemCount(); i++) {
                    Uri imageUri = clipData.getItemAt(i).getUri();
                    if (isValidImageUri(imageUri)) {
                        imageUris.add(imageUri);
                    }
                }
            } else if (data.getData() != null) {
                // 单选
                Uri imageUri = data.getData();
                if (isValidImageUri(imageUri)) {
                    imageUris.add(imageUri);
                }
            }
            
            if (!imageUris.isEmpty()) {
                imageManager.addImages(imageUris);
            } else {
                Snackbar.make(binding.getRoot(), "未选择有效的图片", Snackbar.LENGTH_SHORT).show();
            }
        }
    }
    
    private boolean isValidImageUri(Uri uri) {
        try {
            String mimeType = getContentResolver().getType(uri);
            return mimeType != null && mimeType.startsWith("image/");
        } catch (Exception e) {
            return false;
        }
    }
    
    private void showImagePreview(MultiImageManager.ImageItem item) {
        // 显示图片预览
        Intent intent = new Intent(this, ImagePreviewActivity.class);
        if (item.getImageUrl() != null) {
            intent.putExtra("imageUrl", item.getImageUrl());
        } else if (item.getImageUri() != null) {
            intent.putExtra("imageUri", item.getImageUri().toString());
        }
        startActivity(intent);
    }
    
    private void showImageOptions(MultiImageManager.ImageItem item, int position) {
        String[] options = {"预览", "删除"};
        
        new AlertDialog.Builder(this)
            .setTitle("图片操作")
            .setItems(options, (dialog, which) -> {
                switch (which) {
                    case 0: // 预览
                        showImagePreview(item);
                        break;
                    case 1: // 删除
                        confirmDeleteImage(item, position);
                        break;
                }
            })
            .show();
    }
    
    private void confirmDeleteImage(MultiImageManager.ImageItem item, int position) {
        new AlertDialog.Builder(this)
            .setTitle("删除图片")
            .setMessage("确定要删除这张图片吗？")
            .setPositiveButton("删除", (dialog, which) -> {
                imageManager.deleteImage(item, position);
            })
            .setNegativeButton("取消", null)
            .show();
    }
    
    private void updateUploadProgress(int uploadedCount, int totalCount) {
        String message = String.format("正在上传图片 %d/%d", uploadedCount, totalCount);
        // 可以显示在进度条或状态栏中
        Log.d(TAG, message);
    }
    
    private void handleUploadComplete(List<String> successUrls, List<String> failureMessages) {
        StringBuilder message = new StringBuilder();
        
        if (!successUrls.isEmpty()) {
            message.append("成功上传 ").append(successUrls.size()).append(" 张图片");
        }
        
        if (!failureMessages.isEmpty()) {
            if (message.length() > 0) {
                message.append("，");
            }
            message.append(failureMessages.size()).append(" 张图片上传失败");
        }
        
        if (message.length() > 0) {
            Snackbar.make(binding.getRoot(), message.toString(), Snackbar.LENGTH_LONG).show();
        }
        
        // 如果有失败的图片，可以显示详细错误信息
        if (!failureMessages.isEmpty()) {
            Log.e(TAG, "上传失败的图片: " + String.join(", ", failureMessages));
        }
    }
    
    private void saveProduct() {
        // 验证表单
        if (!validateForm()) {
            return;
        }
        
        // 保存产品信息
        // ... 保存逻辑
        
        Snackbar.make(binding.getRoot(), "产品保存成功", Snackbar.LENGTH_SHORT).show();
        finish();
    }
    
    private boolean validateForm() {
        // 表单验证逻辑
        return true;
    }
    
    private String getCurrentUser() {
        // 获取当前用户
        return "currentUser";
    }
    
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, 
                                         @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        
        if (requestCode == REQUEST_CODE_PERMISSION) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                openImagePicker();
            } else {
                Snackbar.make(binding.getRoot(), "需要存储权限才能选择图片", Snackbar.LENGTH_SHORT).show();
            }
        }
    }
}
```

### 2. 布局文件示例

```xml
<!-- activity_product_edit.xml -->
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 产品基本信息 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="基本信息"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <!-- 产品名称 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:hint="产品名称">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_product_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- 其他字段... -->

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 产品图片 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="产品图片"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_product_images"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <Button
                android:id="@+id/btn_back"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="返回"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/btn_save"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="保存" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
```

### 3. 权限配置

在 `AndroidManifest.xml` 中添加必要权限：

```xml
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.INTERNET" />
```

### 4. 依赖注入配置

确保在 Dagger Hilt 模块中提供 ImageUploadRepository：

```java
@Module
@InstallIn(SingletonComponent.class)
public abstract class RepositoryModule {
    
    @Binds
    abstract ImageUploadRepository bindImageUploadRepository(
        ImageUploadRepositoryImpl imageUploadRepositoryImpl
    );
}
```

## 🔧 自定义配置

### 1. 修改最大图片数量

```java
// 在 MultiImageManager 中修改
private static final int MAX_IMAGES = 6; // 改为6张
```

### 2. 自定义图片网格列数

```java
// 在 MultiImageManager.initRecyclerView() 中修改
recyclerView.setLayoutManager(new GridLayoutManager(context, 4)); // 改为4列
```

### 3. 自定义图片尺寸

修改 `item_multi_image.xml` 中的高度：

```xml
<com.google.android.material.card.MaterialCardView
    android:layout_height="100dp" <!-- 改为100dp -->
    ... />
```

## 📱 最佳实践

1. **权限处理**：在使用前检查和申请必要权限
2. **错误处理**：提供友好的错误提示和重试机制
3. **性能优化**：使用 Glide 进行图片缓存和压缩
4. **用户体验**：显示上传进度和状态反馈
5. **内存管理**：及时清理临时文件和释放资源

## 🚀 扩展功能

1. **图片编辑**：集成图片裁剪和滤镜功能
2. **拖拽排序**：支持图片顺序调整
3. **批量操作**：支持批量删除和下载
4. **云存储**：集成云存储服务
5. **离线支持**：支持离线缓存和同步
