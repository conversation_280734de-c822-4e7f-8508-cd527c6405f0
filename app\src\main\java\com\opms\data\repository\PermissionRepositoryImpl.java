package com.opms.data.repository;

import com.opms.data.local.entity.Permission;
import com.opms.data.model.request.PermissionRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.PermissionCompleteResponse;
import com.opms.data.model.response.PermissionResponse;
import com.opms.data.remote.ApiService;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Singleton;

import retrofit2.Call;

@Singleton
public class PermissionRepositoryImpl implements PermissionRepository {
    private final ApiService api;

    @Inject
    public PermissionRepositoryImpl(ApiService apiService) {
        this.api = apiService;
    }

    @Override
    public Call<ApiResponse<List<PermissionResponse>>> getPermissions() {
        return api.getPermissions();
    }

    @Override
    public Call<ApiResponse<PermissionCompleteResponse>> getPermissionsById(int id) {
        return api.getPermissionsById(id);
    }

    @Override
    public Call<ApiResponse<PermissionResponse>> createPermission(PermissionRequest request) {
        return api.createPermission(request);
    }

    @Override
    public Call<ApiResponse<PermissionResponse>> updatePermission(int id, PermissionRequest request) {
        return api.updatePermission(id, request);
    }

    @Override
    public Call<ApiResponse<Void>> deletePermission(PermissionRequest request) {
        return api.deletePermission(request);
    }

    @Override
    public Permission getPermissionById(int id) {
        return null;
    }

    @Override
    public Permission getPermissionByCode(String code) {
        return null;
    }

    @Override
    public void insert(Permission permission) {

    }

    @Override
    public void update(Permission permission) {

    }

    @Override
    public void delete(Permission permission) {

    }
}