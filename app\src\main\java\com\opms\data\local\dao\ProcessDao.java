package com.opms.data.local.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.opms.data.local.entity.Process;

import java.util.List;

@Dao
public interface ProcessDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(Process process);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<Process> processes);

    @Update
    void update(Process process);

    @Delete
    void delete(Process process);

    @Query("SELECT * FROM processes")
    List<Process> getAll();

    @Query("SELECT * FROM processes WHERE id = :id")
    Process getById(int id);

    @Query("SELECT * FROM processes WHERE orderId = :orderId")
    List<Process> getByOrderId(int orderId);
} 