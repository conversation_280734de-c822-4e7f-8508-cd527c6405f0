package com.opms.ui.system;

import android.os.Bundle;
import android.util.Log;
import android.view.MenuItem;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import com.opms.R;
import com.opms.data.model.request.PositionRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.PositionResponse;
import com.opms.data.repository.PositionRepository;
import com.opms.databinding.ActivityPositionEditBinding;

import java.util.List;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class PositionEditActivity extends AppCompatActivity {

    private static final String TAG = "PositionEdit";

    @Inject
    PositionRepository positionRepository;

    private ActivityPositionEditBinding binding;
    private boolean isEditMode = false;
    private int positionId = -1;
    private List<PositionResponse> allPositions;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityPositionEditBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setupToolbar();
        setupButtons();
        loadExistingData();
        loadAllPositions();
    }

    private void setupToolbar() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
    }

    private void setupButtons() {
        binding.btnSave.setOnClickListener(v -> savePosition());
        binding.btnCancel.setOnClickListener(v -> finish());
    }

    private void loadExistingData() {
        // 检查是否是编辑模式
        positionId = getIntent().getIntExtra("position_id", -1);
        if (positionId != -1) {
            isEditMode = true;
            if (getSupportActionBar() != null) {
                getSupportActionBar().setTitle("编辑职位");
            }

            // 填充现有数据
            String name = getIntent().getStringExtra("position_name");
            String code = getIntent().getStringExtra("position_code");
            String status = getIntent().getStringExtra("position_status");

            binding.etName.setText(name);
            binding.etCode.setText(code);
            binding.etCode.setEnabled(false); // 编辑时不允许修改代码

            // 设置状态
            binding.switchStatus.setChecked("1".equals(status));
        } else {
            if (getSupportActionBar() != null) {
                getSupportActionBar().setTitle("添加职位");
            }
        }
    }

    private void loadAllPositions() {
        positionRepository.getPositions().enqueue(new Callback<ApiResponse<List<PositionResponse>>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<List<PositionResponse>>> call,
                                   @NonNull Response<ApiResponse<List<PositionResponse>>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<List<PositionResponse>> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        allPositions = apiResponse.getData();
                    }
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<List<PositionResponse>>> call, @NonNull Throwable t) {
                Log.e(TAG, "获取职位列表失败: " + t.getMessage(), t);
            }
        });
    }

    private void savePosition() {
        String name = binding.etName.getText().toString().trim();
        String code = binding.etCode.getText().toString().trim();
        boolean isActive = binding.switchStatus.isChecked();

        if (validateInput(name, code)) {
            PositionRequest request = new PositionRequest();
            request.setId(positionId);
            request.setName(name);
            request.setCode(code);
            request.setStatus(isActive ? "1" : "0");

            if (isEditMode) {
                updatePosition(request);
            } else {
                createPosition(request);
            }
        }
    }

    private boolean validateInput(String name, String code) {
        boolean isValid = true;

        // 验证名称
        if (name.isEmpty()) {
            binding.tilName.setError("职位名称不能为空");
            isValid = false;
        } else {
            binding.tilName.setError(null);
        }

        // 验证代码
        if (code.isEmpty()) {
            binding.tilCode.setError("职位代码不能为空");
            isValid = false;
        } else if (!code.matches("^[A-Z0-9_]+$")) {
            binding.tilCode.setError("职位代码只能包含大写字母、数字和下划线");
            isValid = false;
        } else {
            binding.tilCode.setError(null);
        }

        // 检查代码是否重复（基于内存中的数据）
        if (isValid && allPositions != null) {
            for (PositionResponse position : allPositions) {
                // 如果是编辑模式，跳过当前编辑的职位
                if (isEditMode && position.getId() == positionId) {
                    continue;
                }

                if (code.equals(position.getCode())) {
                    String status = position.getStatus();
                    String statusText = "1".equals(status) ? "" : "(已禁用)";
                    String msg = "职位代码[" + code + "]已存在[" + position.getName() + "]" + statusText;
                    binding.tilCode.setError(msg);
                    isValid = false;
                    break;
                }
            }
        }

        return isValid;
    }

    private void createPosition(PositionRequest request) {
        binding.btnSave.setEnabled(false);

        positionRepository.createPosition(request).enqueue(new Callback<ApiResponse<PositionResponse>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<PositionResponse>> call,
                                   @NonNull Response<ApiResponse<PositionResponse>> response) {
                binding.btnSave.setEnabled(true);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<PositionResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        Toast.makeText(PositionEditActivity.this, "职位创建成功", Toast.LENGTH_SHORT).show();
                        finish();
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "创建失败");
                    }
                } else {
                    showError("创建失败");
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<PositionResponse>> call, @NonNull Throwable t) {
                binding.btnSave.setEnabled(true);
                Log.e(TAG, "创建职位失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    private void updatePosition(PositionRequest request) {
        binding.btnSave.setEnabled(false);

        positionRepository.updatePosition((int) positionId, request)
                .enqueue(new Callback<ApiResponse<PositionResponse>>() {
                    @Override
                    public void onResponse(@NonNull Call<ApiResponse<PositionResponse>> call,
                                           @NonNull Response<ApiResponse<PositionResponse>> response) {
                        binding.btnSave.setEnabled(true);

                        if (response.isSuccessful() && response.body() != null) {
                            ApiResponse<PositionResponse> apiResponse = response.body();
                            if (apiResponse.isSuccess()) {
                                Toast.makeText(PositionEditActivity.this, "职位更新成功", Toast.LENGTH_SHORT).show();
                                finish();
                            } else {
                                showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "更新失败");
                            }
                        } else {
                            showError("更新失败");
                        }
                    }

                    @Override
                    public void onFailure(@NonNull Call<ApiResponse<PositionResponse>> call, @NonNull Throwable t) {
                        binding.btnSave.setEnabled(true);
                        Log.e(TAG, "更新职位失败: " + t.getMessage(), t);
                        showError("网络错误: " + t.getMessage());
                    }
                });
    }

    private void showError(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void finish() {
        super.finish();
        // 添加返回动画
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right);
    }
}
