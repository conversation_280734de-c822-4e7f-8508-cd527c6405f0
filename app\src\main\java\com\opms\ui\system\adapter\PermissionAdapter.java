package com.opms.ui.system.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.opms.R;
import com.opms.data.model.response.PermissionResponse;

import java.util.ArrayList;
import java.util.List;

/**
 * 权限列表适配器
 */
public class PermissionAdapter extends RecyclerView.Adapter<PermissionAdapter.ViewHolder> {
    private final Context context;
    private List<PermissionResponse> permissions;
    private OnPermissionClickListener onPermissionClickListener; // 当前编辑的位置

    public PermissionAdapter(Context context) {
        this.context = context;
        this.permissions = new ArrayList<>();
    }

    public void setOnPermissionClickListener(OnPermissionClickListener listener) {
        this.onPermissionClickListener = listener;
    }

    public void setPermissions(List<PermissionResponse> permissions) {
        this.permissions = permissions != null ? permissions : new ArrayList<>();
        notifyDataSetChanged();
    }



    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_permission, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        PermissionResponse permission = permissions.get(position);
        holder.bind(permission, position);
    }

    @Override
    public int getItemCount() {
        return permissions.size();
    }

    /**
     * 权限点击监听器
     */
    public interface OnPermissionClickListener {
        void onPermissionDelete(PermissionResponse permission);

        void onPermissionEditInNewPage(PermissionResponse permission);
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        private final ImageView ivPermissionIcon;
        private final TextView tvName;
        private final TextView tvCode;
        private final TextView tvDescription;
        private final TextView tvStatus;
        private final ImageView ivDelete;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            ivPermissionIcon = itemView.findViewById(R.id.iv_permission_icon);
            tvName = itemView.findViewById(R.id.tv_name);
            tvCode = itemView.findViewById(R.id.tv_code);
            tvDescription = itemView.findViewById(R.id.tv_description);
            tvStatus = itemView.findViewById(R.id.tv_status);
            ivDelete = itemView.findViewById(R.id.iv_delete);
        }

        public void bind(PermissionResponse permission, int currentPosition) {
            tvName.setText(permission.getName());
            tvCode.setText(permission.getCode());
            tvDescription.setText(permission.getDescription() != null ? permission.getDescription() : "");

            // 设置状态
            String status = permission.getStatus();
            if ("ACTIVE".equals(status) || "1".equals(status)) {
                tvStatus.setText("启用");
                tvStatus.setTextColor(context.getColor(R.color.success));
                tvStatus.setBackgroundResource(R.drawable.bg_status_active);
            } else {
                tvStatus.setText("禁用");
                tvStatus.setTextColor(context.getColor(R.color.error));
                tvStatus.setBackgroundResource(R.drawable.bg_status_inactive);
            }

            // 设置点击事件 - 跳转到编辑页面
            itemView.setOnClickListener(v -> {
                if (onPermissionClickListener != null) {
                    onPermissionClickListener.onPermissionEditInNewPage(permission);
                }
            });

            // 设置删除按钮点击事件
            ivDelete.setOnClickListener(v -> {
                if (onPermissionClickListener != null) {
                    onPermissionClickListener.onPermissionDelete(permission);
                }
            });
        }
    }
}
