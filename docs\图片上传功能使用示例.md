# 通用图片上传功能使用示例

## 📋 概述

本文档展示如何使用重构后的通用图片上传功能，适用于不同业务模块的图片上传需求。

## 🛠️ 核心组件

### 1. ImageUploadUtils 工具类

提供静态方法进行图片上传，支持多种业务类型。

### 2. ImageUploadRepository

通过依赖注入提供图片上传服务。

### 3. 业务类型枚举

```java
public enum BusinessType {
    USER("user"),           // 用户头像
    CUSTOMER("customer"),   // 客户图片
    PRODUCT("product"),     // 产品图片
    ORDER("order"),         // 订单图片
    COMPONENT("component"); // 部件图片
}
```

## 📝 使用示例

### 1. 客户图片上传（已重构）

```java
public class CustomerEditActivity extends AppCompatActivity {
    
    @Inject
    ImageUploadRepository imageUploadRepository;
    
    private void uploadCustomerImage() {
        String operator = ImageUploadUtils.getCurrentUser(this);
        
        imageUploadRepository.uploadCustomerImage(
                this,
                selectedImageUri,
                customerId,
                operator,
                new ImageUploadUtils.ImageUploadCallback() {
                    @Override
                    public void onUploadStart() {
                        showLoading(true);
                    }

                    @Override
                    public void onUploadSuccess(String imageUrl) {
                        Snackbar.make(binding.getRoot(), "图片上传成功", Snackbar.LENGTH_SHORT).show();
                    }

                    @Override
                    public void onUploadFailure(String errorMessage) {
                        showError(errorMessage);
                    }

                    @Override
                    public void onUploadComplete() {
                        showLoading(false);
                    }
                }
        );
    }
}
```

### 2. 用户头像上传示例

```java
public class ProfileActivity extends AppCompatActivity {
    
    @Inject
    ImageUploadRepository imageUploadRepository;
    
    private void uploadUserAvatar() {
        String operator = ImageUploadUtils.getCurrentUser(this);
        
        imageUploadRepository.uploadUserAvatar(
                this,
                selectedImageUri,
                userId,
                operator,
                new ImageUploadUtils.ImageUploadCallback() {
                    @Override
                    public void onUploadStart() {
                        binding.progressBar.setVisibility(View.VISIBLE);
                    }

                    @Override
                    public void onUploadSuccess(String imageUrl) {
                        // 更新UI显示新头像
                        Glide.with(this)
                                .load(imageUrl)
                                .into(binding.ivUserAvatar);
                        Toast.makeText(this, "头像更新成功", Toast.LENGTH_SHORT).show();
                    }

                    @Override
                    public void onUploadFailure(String errorMessage) {
                        Toast.makeText(this, "头像上传失败: " + errorMessage, Toast.LENGTH_SHORT).show();
                    }

                    @Override
                    public void onUploadComplete() {
                        binding.progressBar.setVisibility(View.GONE);
                    }
                }
        );
    }
}
```

### 3. 产品图片上传示例

```java
public class ProductEditActivity extends AppCompatActivity {
    
    @Inject
    ImageUploadRepository imageUploadRepository;
    
    private void uploadProductImage() {
        String operator = ImageUploadUtils.getCurrentUser(this);
        
        imageUploadRepository.uploadImage(
                this,
                selectedImageUri,
                ImageUploadUtils.BusinessType.PRODUCT,
                String.valueOf(productId),
                operator,
                new ImageUploadUtils.ImageUploadCallback() {
                    @Override
                    public void onUploadStart() {
                        showProgressDialog("正在上传产品图片...");
                    }

                    @Override
                    public void onUploadSuccess(String imageUrl) {
                        hideProgressDialog();
                        updateProductImageDisplay(imageUrl);
                        showSuccessMessage("产品图片上传成功");
                    }

                    @Override
                    public void onUploadFailure(String errorMessage) {
                        hideProgressDialog();
                        showErrorDialog("上传失败", errorMessage);
                    }

                    @Override
                    public void onUploadComplete() {
                        // 可以在这里做一些清理工作
                    }
                }
        );
    }
}
```

### 4. 直接使用工具类（无依赖注入）

```java
public class SomeActivity extends AppCompatActivity {
    
    private void uploadImage() {
        // 直接使用工具类
        ImageUploadUtils.uploadImage(
                this,
                apiService, // 需要手动获取ApiService实例
                selectedImageUri,
                ImageUploadUtils.BusinessType.ORDER,
                String.valueOf(orderId),
                "currentUser",
                new ImageUploadUtils.ImageUploadCallback() {
                    // 实现回调方法...
                }
        );
    }
}
```

## 🔧 集成步骤

### 1. 添加依赖注入

在需要使用图片上传的Activity中添加：

```java
@Inject
ImageUploadRepository imageUploadRepository;
```

### 2. 实现图片选择

```java
private ActivityResultLauncher<Intent> imagePickerLauncher;

private void setupImagePicker() {
    imagePickerLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            result -> {
                if (result.getResultCode() == RESULT_OK && result.getData() != null) {
                    Uri selectedImageUri = result.getData().getData();
                    if (selectedImageUri != null) {
                        uploadImage(selectedImageUri);
                    }
                }
            }
    );
}

private void openImagePicker() {
    Intent intent = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
    intent.setType("image/*");
    imagePickerLauncher.launch(intent);
}
```

### 3. 权限处理

确保在AndroidManifest.xml中添加必要权限：

```xml
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
<uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
```

## 🎯 优势

1. **代码复用**：统一的图片上传逻辑，避免重复代码
2. **类型安全**：使用枚举定义业务类型，避免字符串错误
3. **统一接口**：所有业务模块使用相同的API接口
4. **易于维护**：集中管理图片上传逻辑
5. **扩展性强**：新增业务类型只需添加枚举值
6. **错误处理**：统一的错误处理和回调机制

## 📁 相关文件

```
app/src/main/java/com/opms/
├── common/utils/
│   └── ImageUploadUtils.java                     # 通用图片上传工具类
├── data/
│   ├── remote/ApiService.java                    # 通用图片上传API
│   └── repository/
│       ├── ImageUploadRepository.java            # 图片上传Repository接口
│       └── ImageUploadRepositoryImpl.java        # 图片上传Repository实现
└── di/module/
    └── RepositoryModule.java                     # 依赖注入配置

docs/
└── 图片上传功能使用示例.md                      # 本文档
```

## ⚠️ 注意事项

1. **操作人获取**：`ImageUploadUtils.getCurrentUser()`需要根据实际项目实现
2. **权限检查**：确保在上传前检查存储权限
3. **文件清理**：工具类会自动清理临时文件
4. **网络状态**：建议在上传前检查网络连接状态
5. **文件大小**：考虑添加文件大小限制和压缩功能
